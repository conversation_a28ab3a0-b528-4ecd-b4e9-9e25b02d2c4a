function runLegoAssembly(ldr_filename)
% 运行完整的LEGO积木组装系统
% 输入: ldr_filename - LDR文件路径

fprintf('=== LEGO积木组装系统 ===\n');
fprintf('高效双臂协调 + 智能路径规划 + 错误恢复\n\n');

if nargin < 1
    ldr_filename = 'main_building.ldr';
end

try
    %% 第一步：检查文件
    fprintf('第一步：检查LDR文件...\n');
    if ~exist(ldr_filename, 'file')
        fprintf('警告: 文件 %s 不存在\n', ldr_filename);
        fprintf('创建示例LDR文件...\n');
        createSampleLDR(ldr_filename);
    end
    
    %% 第二步：创建组装系统
    fprintf('\n第二步：初始化组装系统...\n');
    assembly_system = LegoAssemblySystem(ldr_filename);
    
    %% 第三步：分析目标结构
    fprintf('\n第三步：分析目标结构...\n');
    analyzeTargetStructure(assembly_system);
    
    %% 第四步：优化组装序列
    fprintf('\n第四步：优化组装序列...\n');
    optimizeAssemblySequence(assembly_system);
    
    %% 第五步：执行组装
    fprintf('\n第五步：执行组装...\n');
    success = assembly_system.executeAssembly();
    
    %% 第六步：结果分析
    fprintf('\n第六步：结果分析...\n');
    analyzeResults(assembly_system, success);
    
    %% 第七步：保存结果
    fprintf('\n第七步：保存结果...\n');
    saveResults(assembly_system);
    
    fprintf('\n=== 组装系统运行完成 ===\n');
    
catch ME
    fprintf('系统运行错误: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 显示详细错误信息
    displayErrorDetails(ME);
end

end

function createSampleLDR(filename)
% 创建示例LDR文件

fprintf('创建示例LDR文件: %s\n', filename);

sample_content = {
    '0 // LEGO积木组装示例'
    '0 // 简单的塔状结构'
    '0 Name: Sample Building'
    '0 Author: LEGO Assembly System'
    '0'
    '1 4 0 0 0 1 0 0 0 1 0 0 0 1 3001.dat'      % 红色 2x4 brick 底层
    '1 2 0 32 0 1 0 0 0 1 0 0 0 1 3001.dat'     % 绿色 2x4 brick 底层
    '1 1 0 16 9.6 1 0 0 0 1 0 0 0 1 3003.dat'   % 蓝色 2x2 brick 第二层
    '1 14 16 16 9.6 1 0 0 0 1 0 0 0 1 3003.dat' % 黄色 2x2 brick 第二层
    '1 15 8 16 19.2 1 0 0 0 1 0 0 0 1 3005.dat' % 白色 1x1 brick 顶层
};

fid = fopen(filename, 'w');
if fid ~= -1
    for i = 1:length(sample_content)
        fprintf(fid, '%s\n', sample_content{i});
    end
    fclose(fid);
    fprintf('示例LDR文件创建成功\n');
else
    fprintf('无法创建示例文件\n');
end

end

function analyzeTargetStructure(assembly_system)
% 分析目标结构

if isempty(assembly_system.target_structure)
    fprintf('警告: 没有目标结构数据\n');
    return;
end

bricks = assembly_system.target_structure;
fprintf('目标结构分析:\n');
fprintf('- 总积木数: %d\n', length(bricks));

% 分析积木分布
positions = reshape([bricks.position], 3, [])';
fprintf('- 位置范围:\n');
fprintf('  X: %.1f 到 %.1f\n', min(positions(:,1)), max(positions(:,1)));
fprintf('  Y: %.1f 到 %.1f\n', min(positions(:,2)), max(positions(:,2)));
fprintf('  Z: %.1f 到 %.1f\n', min(positions(:,3)), max(positions(:,3)));

% 分析层数
z_coords = positions(:,3);
unique_z = unique(round(z_coords * 10) / 10);  % 四舍五入到0.1精度
fprintf('- 层数: %d\n', length(unique_z));

% 分析颜色分布
colors = [bricks.color];
unique_colors = unique(colors);
fprintf('- 颜色种类: %d\n', length(unique_colors));

for color = unique_colors
    count = sum(colors == color);
    color_name = assembly_system.ldr_parser.getColorName(color);
    fprintf('  %s: %d个\n', color_name, count);
end

% 分析复杂度
complexity_score = calculateComplexityScore(bricks);
fprintf('- 复杂度评分: %.1f/10\n', complexity_score);

end

function score = calculateComplexityScore(bricks)
% 计算结构复杂度评分

% 基础分数
score = 1;

% 积木数量影响
num_bricks = length(bricks);
score = score + min(num_bricks / 10, 3);  % 最多3分

% 层数影响
positions = reshape([bricks.position], 3, [])';
z_coords = positions(:,3);
num_layers = length(unique(round(z_coords * 10) / 10));
score = score + min(num_layers / 2, 2);  % 最多2分

% 颜色种类影响
colors = [bricks.color];
num_colors = length(unique(colors));
score = score + min(num_colors / 5, 1);  % 最多1分

% 空间分布影响
x_range = max(positions(:,1)) - min(positions(:,1));
y_range = max(positions(:,2)) - min(positions(:,2));
spatial_complexity = (x_range + y_range) / 100;
score = score + min(spatial_complexity, 3);  % 最多3分

score = min(score, 10);  % 限制在10分以内

end

function optimizeAssemblySequence(assembly_system)
% 优化组装序列

if isempty(assembly_system.assembly_sequence)
    fprintf('警告: 没有组装序列\n');
    return;
end

sequence = assembly_system.assembly_sequence;
fprintf('组装序列优化:\n');
fprintf('- 原始序列长度: %d\n', length(sequence));

% 分析序列特征
analyzeSequenceFeatures(sequence);

% 优化策略
optimized_sequence = applyOptimizationStrategies(sequence);
assembly_system.assembly_sequence = optimized_sequence;

fprintf('- 优化后序列长度: %d\n', length(optimized_sequence));

% 估算执行时间
estimated_time = estimateExecutionTime(optimized_sequence);
fprintf('- 估算执行时间: %.1f 秒\n', estimated_time);

end

function analyzeSequenceFeatures(sequence)
% 分析序列特征

fprintf('序列特征分析:\n');

% 按层分组
positions = reshape([sequence.position], 3, [])';
z_coords = positions(:,3);
unique_z = unique(round(z_coords * 10) / 10);

for i = 1:length(unique_z)
    z = unique_z(i);
    layer_mask = abs(z_coords - z) < 0.05;
    layer_count = sum(layer_mask);
    fprintf('  第%d层: %d个积木\n', i, layer_count);
end

% 颜色分布
colors = [sequence.color];
unique_colors = unique(colors);
fprintf('  颜色切换次数: %d\n', sum(diff(colors) ~= 0));

end

function optimized_sequence = applyOptimizationStrategies(sequence)
% 应用优化策略

% 策略1: 按层优先
layer_optimized = optimizeByLayers(sequence);

% 策略2: 减少手臂切换
arm_optimized = optimizeArmSwitching(layer_optimized);

% 策略3: 颜色聚类
color_optimized = optimizeByColor(arm_optimized);

optimized_sequence = color_optimized;

end

function optimized = optimizeByLayers(sequence)
% 按层优化

positions = reshape([sequence.position], 3, [])';
z_coords = positions(:,3);

% 按Z坐标排序
[~, sort_idx] = sort(z_coords);
optimized = sequence(sort_idx);

end

function optimized = optimizeArmSwitching(sequence)
% 优化手臂切换

% 简单策略：按Y坐标分组
positions = reshape([sequence.position], 3, [])';
y_coords = positions(:,2);

% 左臂处理Y>0的积木，右臂处理Y<=0的积木
left_mask = y_coords > 0;
right_mask = y_coords <= 0;

left_bricks = sequence(left_mask);
right_bricks = sequence(right_mask);

% 交替安排
optimized = [];
max_len = max(length(left_bricks), length(right_bricks));

for i = 1:max_len
    if i <= length(left_bricks)
        optimized = [optimized, left_bricks(i)];
    end
    if i <= length(right_bricks)
        optimized = [optimized, right_bricks(i)];
    end
end

end

function optimized = optimizeByColor(sequence)
% 按颜色优化（减少颜色切换）

colors = [sequence.color];
unique_colors = unique(colors);

optimized = [];
for color = unique_colors
    color_mask = colors == color;
    color_bricks = sequence(color_mask);
    optimized = [optimized, color_bricks];
end

end

function time = estimateExecutionTime(sequence)
% 估算执行时间

base_time_per_brick = 15;  % 每个积木基础时间（秒）
num_bricks = length(sequence);

% 基础时间
time = num_bricks * base_time_per_brick;

% 复杂度调整
positions = reshape([sequence.position], 3, [])';
z_coords = positions(:,3);
num_layers = length(unique(round(z_coords * 10) / 10));

% 层数越多，时间增加
time = time * (1 + num_layers * 0.1);

% 手臂切换惩罚
y_coords = positions(:,2);
arm_switches = sum(diff(y_coords > 0) ~= 0);
time = time + arm_switches * 5;  % 每次切换增加5秒

end

function analyzeResults(assembly_system, success)
% 分析结果

fprintf('执行结果分析:\n');
fprintf('- 执行状态: %s\n', assembly_system.system_state.status);

if success
    fprintf('- 组装成功! 🎉\n');
else
    fprintf('- 组装失败 ❌\n');
end

% 性能指标
metrics = assembly_system.performance_metrics;
fprintf('- 总执行时间: %.2f 秒\n', metrics.total_time);
fprintf('- 成功率: %.1f%%\n', metrics.success_rate * 100);
fprintf('- 平均步骤时间: %.2f 秒\n', metrics.average_step_time);
fprintf('- 错误次数: %d\n', assembly_system.error_count);

% 效率分析
if metrics.total_time > 0
    bricks_per_minute = length(assembly_system.assembly_sequence) / (metrics.total_time / 60);
    fprintf('- 组装效率: %.1f 积木/分钟\n', bricks_per_minute);
end

% 建议
provideSuggestions(assembly_system);

end

function provideSuggestions(assembly_system)
% 提供改进建议

fprintf('\n改进建议:\n');

% 基于错误率的建议
if assembly_system.error_count > 0
    error_rate = assembly_system.error_count / length(assembly_system.assembly_sequence);
    if error_rate > 0.1
        fprintf('- 错误率较高(%.1f%%)，建议:\n', error_rate * 100);
        fprintf('  * 检查碰撞检测参数\n');
        fprintf('  * 优化路径规划算法\n');
        fprintf('  * 增加安全距离\n');
    end
end

% 基于执行时间的建议
if assembly_system.performance_metrics.total_time > 0
    avg_time = assembly_system.performance_metrics.average_step_time;
    if avg_time > 20
        fprintf('- 平均步骤时间较长(%.1f秒)，建议:\n', avg_time);
        fprintf('  * 优化运动轨迹\n');
        fprintf('  * 提高运动速度\n');
        fprintf('  * 改进双臂协调策略\n');
    end
end

% 基于结构复杂度的建议
if ~isempty(assembly_system.target_structure)
    complexity = calculateComplexityScore(assembly_system.target_structure);
    if complexity > 7
        fprintf('- 结构复杂度较高(%.1f/10)，建议:\n', complexity);
        fprintf('  * 分阶段组装\n');
        fprintf('  * 增加中间检查点\n');
        fprintf('  * 使用更保守的策略\n');
    end
end

end

function saveResults(assembly_system)
% 保存结果

try
    % 创建结果目录
    result_dir = 'assembly_results';
    if ~exist(result_dir, 'dir')
        mkdir(result_dir);
    end
    
    % 生成时间戳
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    
    % 保存系统状态
    filename = fullfile(result_dir, sprintf('assembly_result_%s.mat', timestamp));
    
    system_state = assembly_system.system_state;
    performance_metrics = assembly_system.performance_metrics;
    target_structure = assembly_system.target_structure;
    assembly_sequence = assembly_system.assembly_sequence;
    error_count = assembly_system.error_count;
    
    save(filename, 'system_state', 'performance_metrics', 'target_structure', ...
         'assembly_sequence', 'error_count');
    
    fprintf('结果已保存到: %s\n', filename);
    
    % 生成报告
    report_filename = fullfile(result_dir, sprintf('assembly_report_%s.txt', timestamp));
    generateReport(assembly_system, report_filename);
    
    fprintf('报告已生成: %s\n', report_filename);
    
catch ME
    fprintf('保存结果失败: %s\n', ME.message);
end

end

function generateReport(assembly_system, filename)
% 生成详细报告

try
    fid = fopen(filename, 'w');
    if fid == -1
        fprintf('无法创建报告文件\n');
        return;
    end
    
    fprintf(fid, '=== LEGO积木组装系统报告 ===\n');
    fprintf(fid, '生成时间: %s\n\n', datestr(now));
    
    % 系统状态
    fprintf(fid, '系统状态:\n');
    fprintf(fid, '- 执行状态: %s\n', assembly_system.system_state.status);
    fprintf(fid, '- 当前步骤: %d/%d\n', assembly_system.system_state.current_step, assembly_system.system_state.total_steps);
    fprintf(fid, '- 错误次数: %d\n\n', assembly_system.error_count);
    
    % 性能指标
    metrics = assembly_system.performance_metrics;
    fprintf(fid, '性能指标:\n');
    fprintf(fid, '- 总执行时间: %.2f 秒\n', metrics.total_time);
    fprintf(fid, '- 成功率: %.1f%%\n', metrics.success_rate * 100);
    fprintf(fid, '- 平均步骤时间: %.2f 秒\n', metrics.average_step_time);
    
    if metrics.total_time > 0
        bricks_per_minute = length(assembly_system.assembly_sequence) / (metrics.total_time / 60);
        fprintf(fid, '- 组装效率: %.1f 积木/分钟\n', bricks_per_minute);
    end
    
    % 目标结构信息
    if ~isempty(assembly_system.target_structure)
        fprintf(fid, '\n目标结构:\n');
        fprintf(fid, '- 总积木数: %d\n', length(assembly_system.target_structure));
        
        positions = reshape([assembly_system.target_structure.position], 3, [])';
        fprintf(fid, '- 尺寸: %.1f x %.1f x %.1f\n', ...
                max(positions(:,1)) - min(positions(:,1)), ...
                max(positions(:,2)) - min(positions(:,2)), ...
                max(positions(:,3)) - min(positions(:,3)));
        
        complexity = calculateComplexityScore(assembly_system.target_structure);
        fprintf(fid, '- 复杂度: %.1f/10\n', complexity);
    end
    
    fclose(fid);
    
catch ME
    fprintf('生成报告失败: %s\n', ME.message);
    if exist('fid', 'var') && fid ~= -1
        fclose(fid);
    end
end

end

function displayErrorDetails(ME)
% 显示详细错误信息

fprintf('\n=== 错误详情 ===\n');
fprintf('错误类型: %s\n', ME.identifier);
fprintf('错误消息: %s\n', ME.message);

if ~isempty(ME.stack)
    fprintf('调用堆栈:\n');
    for i = 1:length(ME.stack)
        fprintf('  %d. %s (第%d行)\n', i, ME.stack(i).name, ME.stack(i).line);
    end
end

fprintf('\n故障排除建议:\n');
fprintf('1. 检查LDR文件格式是否正确\n');
fprintf('2. 确认YuMi机器人模型可用\n');
fprintf('3. 验证所有依赖文件存在\n');
fprintf('4. 检查MATLAB工具箱安装\n');

end
