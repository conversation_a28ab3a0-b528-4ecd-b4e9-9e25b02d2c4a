function debugYumiConfig()
% Debug YuMi configuration types

fprintf('🔍 Debugging YuMi configuration...\n');

try
    % Load YuMi robot
    yumi = loadrobot('abbYumi');
    fprintf('✅ <PERSON><PERSON>i loaded\n');
    
    % Check homeConfiguration
    q_home = yumi.homeConfiguration;
    fprintf('Home configuration type: %s\n', class(q_home));
    fprintf('Home configuration size: %s\n', mat2str(size(q_home)));
    
    if isa(q_home, 'rigidBodyJointConfiguration')
        fprintf('Converting to numeric array...\n');
        q_numeric = [q_home.JointPosition];
        fprintf('Numeric array size: %s\n', mat2str(size(q_numeric)));
        fprintf('Numeric array type: %s\n', class(q_numeric));
    end
    
catch ME
    fprintf('❌ Error: %s\n', ME.message);
end

end
