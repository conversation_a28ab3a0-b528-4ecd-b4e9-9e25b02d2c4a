function config = castle_lego_config()
% 乐高城堡配置 - 严格按照图片笔记设计
% 基于用户提供的精确坐标和层级信息

fprintf('🏰 === 加载乐高城堡配置 === 🏰\n');

% === 基础参数 (严格按照笔记) ===
config.center = [0.5, 0, 0.05];  % center = [0.5, 0, 0.05]
config.brick_length = 0.0318;    % 0.0318m (笔记中的标准长度)
config.brick_width = 0.0159;     % 0.0159m (笔记中的标准宽度)  
config.brick_height = 0.0096;    % 标准乐高积木高度
config.layer_height = 0.0096;    % 每层高度

% === 层级定义 ===
config.levels = struct();
config.levels.level1 = 0.0096;   % 基础层
config.levels.level2 = 0.0192;   % 第二层
config.levels.level3 = 0.0288;   % 第三层
config.levels.level4 = 0.0384;   % 第四层
config.levels.level5 = 0.0480;   % 第五层 (笔记重点层级)

% === 城堡积木配置 (严格按照笔记坐标) ===

% 第五层积木 - 按照笔记中的精确坐标
fprintf('配置第五层积木 (level5)...\n');

% 左手积木序列 (按照笔记顺序)
config.left_arm_bricks = [
    % B01: [0.5, -0.00745, z=level5(1)]
    0.5, -0.00745, config.levels.level5, 0, 1;
    
    % B09: [0.5-0.0318, +0.00745, z=level5(1)]  
    0.5-0.0318, +0.00745, config.levels.level5, 0, 9;
    
    % B07: [0.5-0.0318, -0.00745, z=level5(1)]
    0.5-0.0318, -0.00745, config.levels.level5, 0, 7;
    
    % B05: [0.5-0.0318*2, +0.00745, z=level5(1)]
    0.5-0.0318*2, +0.00745, config.levels.level5, 0, 5;
    
    % B03: [0.5-0.0318*2, -0.00745, z=level5(1)]
    0.5-0.0318*2, -0.00745, config.levels.level5, 0, 3;
    
    % B01: [0.5-0.0318*2, 0.0318-0.0159, 0, z=level5(1)] (90°旋转)
    0.5-0.0318*2, 0.0318-0.0159, config.levels.level5, pi/2, 1;
];

% 右手积木序列 (按照笔记顺序)
config.right_arm_bricks = [
    % B12: [0.5, +0.00745, z=level5(1)]
    0.5, +0.00745, config.levels.level5, 0, 12;
    
    % B10: [0.5+0.0318, +0.00745, z=level5(1)]
    0.5+0.0318, +0.00745, config.levels.level5, 0, 10;
    
    % B08: [0.5+0.0318, -0.00745, z=level5(1)]
    0.5+0.0318, -0.00745, config.levels.level5, 0, 8;
    
    % B06: [0.5+0.0318*2, +0.00745, z=level5(1)]
    0.5+0.0318*2, +0.00745, config.levels.level5, 0, 6;
    
    % B04: [0.5+0.0318*2, -0.00745, z=level5(1)]
    0.5+0.0318*2, -0.00745, config.levels.level5, 0, 4;
    
    % B02: [0.5+0.0318*2, 0.0318+0.0159, 0, z=level5(1)] (90°旋转)
    0.5+0.0318*2, 0.0318+0.0159, config.levels.level5, pi/2, 2;
];

% === 城堡基础结构 ===
fprintf('配置城堡基础结构...\n');

% 基础平台 (Level 1)
config.foundation = [
    % 中央基础
    0.5, 0, config.levels.level1, 0, 101;
    0.5-0.0318, 0, config.levels.level1, 0, 102;
    0.5+0.0318, 0, config.levels.level1, 0, 103;
    
    % 左侧基础
    0.5-0.0318*2, 0, config.levels.level1, 0, 104;
    0.5-0.0318*3, 0, config.levels.level1, 0, 105;
    
    % 右侧基础
    0.5+0.0318*2, 0, config.levels.level1, 0, 106;
    0.5+0.0318*3, 0, config.levels.level1, 0, 107;
];

% 城墙结构 (Level 2-4)
config.walls = [];
for level = 2:4
    z_height = config.levels.(sprintf('level%d', level));
    
    % 左城墙
    config.walls = [config.walls;
        0.5-0.0318*2, -0.0159, z_height, 0, 200+level*10+1;
        0.5-0.0318*2, +0.0159, z_height, 0, 200+level*10+2;
        0.5-0.0318*3, -0.0159, z_height, 0, 200+level*10+3;
        0.5-0.0318*3, +0.0159, z_height, 0, 200+level*10+4;
    ];
    
    % 右城墙
    config.walls = [config.walls;
        0.5+0.0318*2, -0.0159, z_height, 0, 200+level*10+5;
        0.5+0.0318*2, +0.0159, z_height, 0, 200+level*10+6;
        0.5+0.0318*3, -0.0159, z_height, 0, 200+level*10+7;
        0.5+0.0318*3, +0.0159, z_height, 0, 200+level*10+8;
    ];
    
    % 中央塔楼
    config.walls = [config.walls;
        0.5, -0.0159, z_height, 0, 200+level*10+9;
        0.5, +0.0159, z_height, 0, 200+level*10+10;
    ];
end

% === 塔楼顶部结构 ===
fprintf('配置塔楼顶部...\n');

% 塔楼顶部 (Level 6+)
config.tower_top = [
    % 中央塔楼顶部
    0.5, 0, config.levels.level5 + 0.0096, 0, 301;
    0.5, 0, config.levels.level5 + 0.0192, 0, 302;
    
    % 圆锥形屋顶 (简化为几个积木)
    0.5, 0, config.levels.level5 + 0.0288, 0, 303;
];

% === 拼接任务序列 ===
fprintf('生成拼接任务序列...\n');

% 按照拼接顺序组织任务
config.assembly_sequence = [];

% 1. 基础平台
for i = 1:size(config.foundation, 1)
    config.assembly_sequence = [config.assembly_sequence; 
        config.foundation(i, :), 1, 0];  % [x,y,z,angle,id, phase, arm]
end

% 2. 城墙 (分层拼接)
for i = 1:size(config.walls, 1)
    arm = mod(i, 2);  % 交替使用双臂
    config.assembly_sequence = [config.assembly_sequence; 
        config.walls(i, :), 2, arm];
end

% 3. 第五层积木 (按照笔记顺序)
% 左手积木
for i = 1:size(config.left_arm_bricks, 1)
    config.assembly_sequence = [config.assembly_sequence; 
        config.left_arm_bricks(i, :), 3, 0];  % 左臂
end

% 右手积木
for i = 1:size(config.right_arm_bricks, 1)
    config.assembly_sequence = [config.assembly_sequence; 
        config.right_arm_bricks(i, :), 3, 1];  % 右臂
end

% 4. 塔楼顶部
for i = 1:size(config.tower_top, 1)
    arm = mod(i, 2);
    config.assembly_sequence = [config.assembly_sequence; 
        config.tower_top(i, :), 4, arm];
end

% === 积木类型定义 ===
config.brick_types = struct();
config.brick_types.standard = 'brick_2x4';
config.brick_types.rotated = 'brick_2x4_rotated';

% === 颜色配置 ===
config.colors = struct();
config.colors.foundation = [0.8, 0.7, 0.5];    % 米色基础
config.colors.walls = [0.7, 0.6, 0.4];         % 棕色城墙
config.colors.level5 = [0.9, 0.8, 0.6];        % 浅色第五层
config.colors.tower = [0.6, 0.5, 0.3];         % 深色塔楼

% === 输出配置信息 ===
fprintf('\n🏰 === 城堡配置完成 === 🏰\n');
fprintf('中心坐标: [%.3f, %.3f, %.3f]\n', config.center);
fprintf('积木尺寸: %.4f x %.4f x %.4f m\n', ...
        config.brick_length, config.brick_width, config.brick_height);
fprintf('总积木数: %d\n', size(config.assembly_sequence, 1));
fprintf('  - 基础平台: %d块\n', size(config.foundation, 1));
fprintf('  - 城墙结构: %d块\n', size(config.walls, 1));
fprintf('  - 第五层: %d块\n', size(config.left_arm_bricks, 1) + size(config.right_arm_bricks, 1));
fprintf('  - 塔楼顶部: %d块\n', size(config.tower_top, 1));
fprintf('拼接阶段: 4个阶段\n');
fprintf('✅ 严格按照图片笔记配置完成\n');

end

function [bricks] = parse_ldr(file_path)
    % Reads an LDR file and extracts brick information.
    % Each line in the LDR file corresponds to a brick and has the format:
    % line_type color x y z a b c d e f g h i part_name.dat
    % We are interested in x, y, z for position and the 3x3 rotation matrix.
    
    fileID = fopen(file_path, 'r');
    if fileID == -1
        error('Could not open the LDR file.');
    end
    
    C = textscan(fileID, '%f %f %f %f %f %f %f %f %f %f %f %f %f %s', 'HeaderLines', 2, 'CollectOutput', true);
    fclose(fileID);
    
    data = C{1};
    part_names = C{2};
    
    num_bricks = size(data, 1);
    bricks = struct('part_name', {}, 'position', {}, 'orientation', {});
    
    for i = 1:num_bricks
        bricks(i).part_name = part_names{i};
        
        % Position vector
        bricks(i).position = data(i, 3:5)';
        
        % Rotation matrix
        rot_matrix = [data(i, 6:8); data(i, 9:11); data(i, 12:14)]';
        bricks(i).orientation = rot_matrix;
    end
end
