function yumiVisualizerFunction(robotData)
% YuMi 3D Visualizer Function for Simulink
% This function goes in the "Visualizer" MATLAB Function block

% Persistent variables for visualization
persistent fig ax robot qHome isSetup legoBlocks endEffectorPlots;

% Initialize visualization on first call
if isempty(isSetup)
    try
        fprintf('Initializing YuMi 3D Visualization...\n');
        
        % Create figure
        fig = figure('Name', 'YuMi Robot - Lego Assembly Simulation', ...
                     'Position', [100, 100, 1000, 700], ...
                     'Color', [0.95, 0.95, 0.95]);
        
        % Create axes
        ax = axes('Parent', fig, 'Position', [0.1, 0.1, 0.8, 0.8]);
        
        % Try to load and display robot
        try
            robot = loadrobot('abbYumi');
            qHome = robot.homeConfiguration;
            show(robot, qHome, 'Parent', ax, 'Frames', 'off');
            fprintf('SUCCESS: <PERSON>Mi robot displayed\n');
        catch ME
            fprintf('WARNING: Could not display robot: %s\n', ME.message);
            robot = [];
            qHome = [];
            % Create simple robot representation
            plot3(ax, 0, 0, 0.5, 'ro', 'MarkerSize', 20, 'MarkerFaceColor', 'red');
        end
        
        % Set up axes
        hold(ax, 'on');
        grid(ax, 'on');
        axis(ax, 'equal');
        view(ax, 45, 30);
        
        % Set limits
        xlim(ax, [0.2, 0.8]);
        ylim(ax, [-0.4, 0.4]);
        zlim(ax, [0, 0.6]);
        
        % Labels and title
        title(ax, 'YuMi Robot - Lego Assembly Simulation', 'FontSize', 16, 'FontWeight', 'bold');
        xlabel(ax, 'X (m)', 'FontSize', 12);
        ylabel(ax, 'Y (m)', 'FontSize', 12);
        zlabel(ax, 'Z (m)', 'FontSize', 12);
        
        % Add lighting
        lighting(ax, 'gouraud');
        light('Parent', ax, 'Position', [2, 2, 2]);
        light('Parent', ax, 'Position', [-2, -2, 2]);
        
        % Create Lego environment
        createLegoEnvironment(ax);
        
        % Initialize end effector plots
        endEffectorPlots = [];
        endEffectorPlots(1) = plot3(ax, 0.5, 0.2, 0.3, 'ro', 'MarkerSize', 12, 'MarkerFaceColor', 'red');
        endEffectorPlots(2) = plot3(ax, 0.5, -0.2, 0.3, 'bo', 'MarkerSize', 12, 'MarkerFaceColor', 'blue');
        
        % Add legend
        legend(ax, {'Right End Effector', 'Left End Effector'}, 'Location', 'northeast');
        
        isSetup = true;
        fprintf('SUCCESS: 3D Visualization initialized\n');
        
    catch ME
        fprintf('ERROR: Visualization setup failed: %s\n', ME.message);
        isSetup = false;
        return;
    end
end

% Update robot visualization
if isSetup && ~isempty(robotData)
    try
        % Update robot pose if available
        if ~isempty(robot) && length(robotData) >= 14
            % Convert robotData to robot configuration
            q = qHome;
            for i = 1:min(length(robotData), length(q))
                q(i).JointPosition = robotData(i);
            end
            
            % Update robot display
            show(robot, q, 'Parent', ax, 'PreservePlot', false, 'Frames', 'off');
            
            % Update end effector positions
            try
                T_right = getTransform(robot, q, 'yumi_link_7_r');
                T_left = getTransform(robot, q, 'yumi_link_7_l');
                
                % Update end effector plots
                if ~isempty(endEffectorPlots) && length(endEffectorPlots) >= 2
                    set(endEffectorPlots(1), 'XData', T_right(1,4), 'YData', T_right(2,4), 'ZData', T_right(3,4));
                    set(endEffectorPlots(2), 'XData', T_left(1,4), 'YData', T_left(2,4), 'ZData', T_left(3,4));
                end
                
            catch
                % Fallback end effector update
                if length(robotData) >= 2
                    rightPos = [0.5 + 0.2*sin(robotData(1)), 0.2*cos(robotData(1)), 0.3];
                    leftPos = [0.5 - 0.2*sin(robotData(8)), -0.2*cos(robotData(8)), 0.3];
                    
                    if ~isempty(endEffectorPlots) && length(endEffectorPlots) >= 2
                        set(endEffectorPlots(1), 'XData', rightPos(1), 'YData', rightPos(2), 'ZData', rightPos(3));
                        set(endEffectorPlots(2), 'XData', leftPos(1), 'YData', leftPos(2), 'ZData', leftPos(3));
                    end
                end
            end
        end
        
        % Update display
        drawnow limitrate;
        
    catch ME
        % Ignore update errors to prevent simulation stop
        if mod(now*86400, 5) < 0.1  % Print error every 5 seconds
            fprintf('Visualization update warning: %s\n', ME.message);
        end
    end
end

end

function createLegoEnvironment(ax)
% Create Lego block environment

fprintf('Creating Lego environment...\n');

% Lego block positions (based on typical pick-and-place scenario)
legoPositions = [
    0.6,  0.2,  0.05;   % Blue block 1 (pick target)
    0.6, -0.2,  0.05;   % Blue block 2 (pick target)
    0.65, 0.0,  0.05;   % Blue block 3 (pick target)
    0.5,  0.0,  0.01;   % Green platform (place target)
];

legoColors = [
    0, 0, 1;    % Blue
    0, 0, 1;    % Blue
    0, 0, 1;    % Blue
    0, 1, 0;    % Green
];

legoSizes = [
    0.08, 0.08, 0.08;   % Standard brick
    0.08, 0.08, 0.08;   % Standard brick
    0.08, 0.08, 0.08;   % Standard brick
    0.25, 0.25, 0.02;   % Platform
];

% Create Lego blocks
for i = 1:size(legoPositions, 1)
    pos = legoPositions(i, :);
    color = legoColors(i, :);
    size = legoSizes(i, :);
    
    % Create block
    createLegoBlock(ax, pos, size, color, i);
end

% Add work surface
createWorkSurface(ax);

% Add labels
text(ax, 0.6, 0.2, 0.12, 'Pick 1', 'FontSize', 10, 'Color', 'blue', 'FontWeight', 'bold');
text(ax, 0.6, -0.2, 0.12, 'Pick 2', 'FontSize', 10, 'Color', 'blue', 'FontWeight', 'bold');
text(ax, 0.65, 0.0, 0.12, 'Pick 3', 'FontSize', 10, 'Color', 'blue', 'FontWeight', 'bold');
text(ax, 0.5, 0.0, 0.08, 'Assembly Target', 'FontSize', 10, 'Color', 'green', 'FontWeight', 'bold');

fprintf('SUCCESS: Lego environment created\n');

end

function createLegoBlock(ax, center, size, color, blockId)
% Create a single Lego block

try
    % Calculate vertices
    dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;
    
    vertices = [
        center(1)-dx, center(2)-dy, center(3)-dz;  % 1
        center(1)+dx, center(2)-dy, center(3)-dz;  % 2
        center(1)+dx, center(2)+dy, center(3)-dz;  % 3
        center(1)-dx, center(2)+dy, center(3)-dz;  % 4
        center(1)-dx, center(2)-dy, center(3)+dz;  % 5
        center(1)+dx, center(2)-dy, center(3)+dz;  % 6
        center(1)+dx, center(2)+dy, center(3)+dz;  % 7
        center(1)-dx, center(2)+dy, center(3)+dz;  % 8
    ];
    
    % Define faces
    faces = [
        1 2 3 4;  % Bottom
        5 6 7 8;  % Top
        1 2 6 5;  % Front
        3 4 8 7;  % Back
        1 4 8 5;  % Left
        2 3 7 6;  % Right
    ];
    
    % Create patch
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', 0.8, ...
          'EdgeColor', 'black', 'LineWidth', 1, ...
          'Parent', ax);
    
    % Add studs for Lego appearance (top face only)
    if blockId <= 3  % Only for pick blocks
        studX = center(1) + [-0.02, 0.02];
        studY = center(2) + [-0.02, 0.02];
        studZ = center(3) + dz + 0.005;
        
        for sx = studX
            for sy = studY
                plot3(ax, sx, sy, studZ, 'o', 'Color', color*0.8, ...
                      'MarkerSize', 4, 'MarkerFaceColor', color*0.8);
            end
        end
    end
    
catch
    % Fallback: simple marker
    plot3(ax, center(1), center(2), center(3), 'o', ...
          'Color', color, 'MarkerSize', 15, 'MarkerFaceColor', color);
end

end

function createWorkSurface(ax)
% Create work surface

try
    % Work surface parameters
    surfacePos = [0.5, 0, -0.01];
    surfaceSize = [0.6, 0.6, 0.02];
    surfaceColor = [0.7, 0.7, 0.7];
    
    % Create surface
    createLegoBlock(ax, surfacePos, surfaceSize, surfaceColor, 0);
    
catch
    % Fallback
    plot3(ax, 0.5, 0, 0, 's', 'Color', [0.7, 0.7, 0.7], ...
          'MarkerSize', 30, 'MarkerFaceColor', [0.7, 0.7, 0.7]);
end

end
