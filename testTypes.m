% 测试类型兼容性
function testTypes()
    fprintf('=== 测试类型兼容性 ===\n');
    
    try
        % 加载YuMi机器人
        robot = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
        
        % 检查homeConfiguration的类型
        home_config = robot.homeConfiguration;
        fprintf('homeConfiguration类型: %s\n', class(home_config));
        fprintf('homeConfiguration大小: %s\n', mat2str(size(home_config)));
        fprintf('homeConfiguration值: %s\n', mat2str(home_config));
        
        % 创建测试路径
        test_path = [0.1, -0.2, 0.1, 0.5, 0.1, 0.2, 0.0];
        fprintf('test_path类型: %s\n', class(test_path));
        fprintf('test_path大小: %s\n', mat2str(size(test_path)));
        
        % 尝试赋值
        fprintf('尝试赋值...\n');
        full_config = home_config;
        full_config(1:7) = test_path;
        fprintf('✅ 赋值成功！\n');
        
    catch ME
        fprintf('❌ 错误: %s\n', ME.message);
        fprintf('错误位置: %s\n', ME.stack(1).name);
        fprintf('错误行号: %d\n', ME.stack(1).line);
    end
end
