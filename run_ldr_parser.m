function run_ldr_parser()
    % This script parses the mainbu.ldr file and saves the brick configuration.
    
    % Add the path to the file containing the parse_ldr function
    addpath(fileparts(which('castle_lego_config.m')));
    
    % Parse the LDR file
    bricks = parse_ldr('mainbu.ldr');
    
    % Save the parsed data
    save('brick_config_from_ldr.mat', 'bricks');
    
    fprintf('LDR file parsed and configuration saved to brick_config_from_ldr.mat\n');
end 