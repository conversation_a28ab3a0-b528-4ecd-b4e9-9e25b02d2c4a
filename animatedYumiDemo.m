function animatedYumiDemo()
% Animated YuMi Demo - Add motion to the existing YuMi visualization
% Based on your successful static display, now add animation

fprintf('=== Animated YuMi Demo ===\n');
fprintf('Adding animation to your successful YuMi visualization\n\n');

try
    % === Load YuMi robot ===
    fprintf('Step 1: Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    fprintf('SUCCESS: YuMi robot loaded with %d joints\n', robot.NumBodies);
    
    % === Create enhanced figure ===
    fprintf('Step 2: Creating enhanced visualization...\n');
    
    fig = figure('Name', 'YuMi Robot - Animated Pick and Place', ...
                 'Position', [100, 100, 1400, 900], ...
                 'Color', [0.95, 0.95, 0.95]);
    
    % === Display initial robot ===
    fprintf('Step 3: Setting up initial display...\n');
    
    qHome = robot.homeConfiguration;
    show(robot, qHome, 'Frames', 'off');
    hold on;
    
    % Set view and lighting (same as your successful display)
    view(45, 30);
    axis equal;
    grid on;
    lighting gouraud;
    light('Position', [2, 2, 2]);
    light('Position', [-2, -2, 2]);
    
    % === Add environment objects (same as your display) ===
    fprintf('Step 4: Adding environment objects...\n');
    
    % Blue blocks (pick targets)
    block1 = addAnimatedBlock([0.6, 0.2, 0.05], [0.08, 0.08, 0.08], 'blue', 'Pick Object 1');
    block2 = addAnimatedBlock([0.6, -0.2, 0.05], [0.08, 0.08, 0.08], 'blue', 'Pick Object 2');
    
    % Green platform (place target)
    platform = addAnimatedBlock([0.5, 0, 0.01], [0.25, 0.25, 0.02], 'green', 'Place Target');
    
    % Work surface
    surface = addAnimatedBlock([0.5, 0, -0.01], [0.4, 0.4, 0.02], [0.7, 0.7, 0.7], 'Work Surface');
    
    % === Add labels and title ===
    fprintf('Step 5: Adding labels and information...\n');
    
    title('YuMi Robot - Animated Pick and Place Simulation', ...
          'FontSize', 16, 'FontWeight', 'bold');
    
    xlabel('X (m)', 'FontSize', 12);
    ylabel('Y (m)', 'FontSize', 12);
    zlabel('Z (m)', 'FontSize', 12);
    
    % Add text labels
    text(0.3, 0.3, 0.4, 'YuMi Robot', 'FontSize', 14, 'Color', 'red', 'FontWeight', 'bold');
    text(0.6, 0.2, 0.15, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue');
    text(0.6, -0.2, 0.15, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue');
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green');
    
    % Set axis limits
    xlim([0.2, 0.8]);
    ylim([-0.4, 0.4]);
    zlim([0, 0.6]);
    
    % === Create animation control panel ===
    fprintf('Step 6: Creating animation controls...\n');
    
    % Control panel
    controlPanel = uipanel('Parent', fig, ...
                          'Position', [0.02, 0.7, 0.25, 0.25], ...
                          'Title', 'Animation Controls', ...
                          'FontSize', 12, ...
                          'BackgroundColor', 'white');
    
    % Start animation button
    startBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', 'Start Animation', ...
                        'Position', [10, 150, 120, 30], ...
                        'FontSize', 10, ...
                        'Callback', @startAnimation);
    
    % Stop animation button
    stopBtn = uicontrol('Parent', controlPanel, ...
                       'Style', 'pushbutton', ...
                       'String', 'Stop Animation', ...
                       'Position', [10, 110, 120, 30], ...
                       'FontSize', 10, ...
                       'Callback', @stopAnimation);
    
    % Reset button
    resetBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', 'Reset Position', ...
                        'Position', [10, 70, 120, 30], ...
                        'FontSize', 10, ...
                        'Callback', @resetPosition);
    
    % Speed slider
    uicontrol('Parent', controlPanel, ...
              'Style', 'text', ...
              'String', 'Animation Speed:', ...
              'Position', [10, 40, 100, 20], ...
              'FontSize', 9);
    
    speedSlider = uicontrol('Parent', controlPanel, ...
                           'Style', 'slider', ...
                           'Position', [10, 20, 120, 20], ...
                           'Min', 0.1, 'Max', 2.0, 'Value', 1.0);
    
    % === Animation variables ===
    animationRunning = false;
    animationTimer = [];
    currentStep = 1;
    
    % === Start automatic animation ===
    fprintf('Step 7: Starting automatic animation...\n');
    fprintf('Watch the YuMi robot perform pick and place operations!\n\n');
    
    % Automatically start animation
    startAnimation();
    
    fprintf('=== Animation Controls ===\n');
    fprintf('• Start Animation: Begin/resume motion\n');
    fprintf('• Stop Animation: Pause motion\n');
    fprintf('• Reset Position: Return to home\n');
    fprintf('• Speed Slider: Adjust animation speed\n\n');
    
    fprintf('=== Animation Sequence ===\n');
    fprintf('1. Move right arm to first blue block\n');
    fprintf('2. Pick up first blue block\n');
    fprintf('3. Move left arm to second blue block\n');
    fprintf('4. Pick up second blue block\n');
    fprintf('5. Move both arms to green platform\n');
    fprintf('6. Place both blocks on platform\n');
    fprintf('7. Return to home position\n');
    fprintf('8. Repeat sequence\n\n');
    
    % === Callback functions ===
    function startAnimation(~, ~)
        if ~animationRunning
            animationRunning = true;
            fprintf('Starting animation...\n');
            
            % Create timer for animation
            animationTimer = timer('ExecutionMode', 'fixedRate', ...
                                  'Period', 0.1, ...
                                  'TimerFcn', @updateAnimation);
            start(animationTimer);
            
            set(startBtn, 'String', 'Running...', 'Enable', 'off');
            set(stopBtn, 'Enable', 'on');
        end
    end
    
    function stopAnimation(~, ~)
        if animationRunning
            animationRunning = false;
            fprintf('Stopping animation...\n');
            
            if ~isempty(animationTimer) && isvalid(animationTimer)
                stop(animationTimer);
                delete(animationTimer);
            end
            
            set(startBtn, 'String', 'Start Animation', 'Enable', 'on');
            set(stopBtn, 'Enable', 'off');
        end
    end
    
    function resetPosition(~, ~)
        fprintf('Resetting to home position...\n');
        stopAnimation();
        currentStep = 1;
        
        % Reset robot to home position
        show(robot, qHome, 'PreservePlot', false, 'Frames', 'off');
        drawnow;
    end
    
    function updateAnimation(~, ~)
        try
            % Get animation speed
            speed = get(speedSlider, 'Value');
            
            % Generate motion based on current step
            q = generateMotionStep(qHome, currentStep, speed);
            
            % Update robot display
            show(robot, q, 'PreservePlot', false, 'Frames', 'off');
            
            % Update step counter
            currentStep = currentStep + 1;
            if currentStep > 200  % Reset after complete cycle
                currentStep = 1;
            end
            
            % Update display
            drawnow;
            
        catch ME
            fprintf('Animation error: %s\n', ME.message);
            stopAnimation();
        end
    end
    
    fprintf('SUCCESS: Animated YuMi demo is now running!\n');
    fprintf('The robot should be moving in a pick-and-place pattern.\n');
    
catch ME
    fprintf('ERROR: Animation setup failed: %s\n', ME.message);
    
    % Fallback to static display
    try
        fprintf('Falling back to static display...\n');
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Robot - Static Display');
        view(45, 30);
        grid on;
        fprintf('SUCCESS: Static display created\n');
    catch
        fprintf('ERROR: Complete failure\n');
    end
end

end

function q = generateMotionStep(qHome, step, speed)
% Generate robot configuration for animation step

q = qHome;
t = step * 0.05 * speed;  % Time parameter

% Pick and place motion sequence
cycle_time = 10;  % Complete cycle in 10 seconds
phase = mod(t, cycle_time) / cycle_time;  % 0 to 1

if phase < 0.2
    % Phase 1: Move right arm to first block
    progress = phase / 0.2;
    q(1) = 0.4 * progress;
    q(2) = -0.3 * progress;
    q(3) = 0.2 * progress;
    
elseif phase < 0.4
    % Phase 2: Pick first block and move left arm to second block
    progress = (phase - 0.2) / 0.2;
    q(1) = 0.4;
    q(2) = -0.3;
    q(3) = 0.2;
    q(4) = -0.5 * progress;
    
    % Left arm starts moving
    q(8) = -0.4 * progress;
    q(9) = -0.3 * progress;
    q(10) = -0.2 * progress;
    
elseif phase < 0.6
    % Phase 3: Pick second block
    progress = (phase - 0.4) / 0.2;
    q(1) = 0.4;
    q(2) = -0.3;
    q(3) = 0.2;
    q(4) = -0.5;
    
    q(8) = -0.4;
    q(9) = -0.3;
    q(10) = -0.2;
    q(11) = -0.5 * progress;
    
elseif phase < 0.8
    % Phase 4: Move both arms to platform
    progress = (phase - 0.6) / 0.2;
    
    % Right arm to platform
    q(1) = 0.4 - 0.3 * progress;
    q(2) = -0.3 + 0.1 * progress;
    q(3) = 0.2;
    q(4) = -0.5;
    
    % Left arm to platform
    q(8) = -0.4 + 0.3 * progress;
    q(9) = -0.3 + 0.1 * progress;
    q(10) = -0.2;
    q(11) = -0.5;
    
else
    % Phase 5: Place blocks and return home
    progress = (phase - 0.8) / 0.2;
    
    % Return to home
    q(1) = 0.1 * (1 - progress);
    q(2) = -0.2 * (1 - progress);
    q(3) = 0.2 * (1 - progress);
    q(4) = -0.5 * (1 - progress);
    
    q(8) = -0.1 * (1 - progress);
    q(9) = -0.2 * (1 - progress);
    q(10) = -0.2 * (1 - progress);
    q(11) = -0.5 * (1 - progress);
end

end

function blockHandle = addAnimatedBlock(center, size, color, label)
% Add animated block to the scene

% Convert color name to RGB if needed
if ischar(color)
    switch color
        case 'blue'
            color = [0, 0, 1];
        case 'green'
            color = [0, 0.8, 0];
        case 'red'
            color = [1, 0, 0];
        otherwise
            color = [0.5, 0.5, 0.5];
    end
end

% Create block vertices
dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;
vertices = [
    center(1)-dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)+dy, center(3)+dz;
    center(1)-dx, center(2)+dy, center(3)+dz;
];

% Block faces
faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];

% Create patch
blockHandle = patch('Vertices', vertices, 'Faces', faces, ...
                   'FaceColor', color, 'FaceAlpha', 0.8, ...
                   'EdgeColor', 'black', 'LineWidth', 1);

fprintf('  Added: %s\n', label);

end
