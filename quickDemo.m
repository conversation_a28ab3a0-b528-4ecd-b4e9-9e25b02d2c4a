function quickDemo()
% 快速YuMi机器人演示
% 确保用户能看到实际的运行结果

fprintf('🚀 === 快速YuMi机器人演示 === 🚀\n');

try
    % 1. 清理环境
    close all;
    clc;
    
    % 2. 加载YuMi机器人
    fprintf('加载YuMi机器人...\n');
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    fprintf('✅ YuMi机器人加载成功\n');
    
    % 3. 创建图形窗口
    fprintf('创建3D显示窗口...\n');
    fig = figure('Name', 'YuMi机器人快速演示', 'Position', [100, 100, 1000, 700]);
    
    % 4. 显示YuMi机器人
    fprintf('显示YuMi机器人3D模型...\n');
    show(yumi, qHome, 'Frames', 'off');
    
    % 设置视角
    view(45, 30);
    axis equal;
    grid on;
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');
    title('YuMi机器人3D模型', 'FontSize', 14, 'FontWeight', 'bold');
    
    fprintf('✅ YuMi机器人3D模型显示成功！\n');
    
    % 5. 简单运动演示
    fprintf('开始简单运动演示...\n');
    
    % 定义几个关键位置
    positions = [
        qHome;  % 初始位置
        qHome + [0.2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];  % 右手臂移动
        qHome + [0, 0, 0, 0, 0, 0, 0, 0.2, 0, 0, 0, 0, 0, 0];  % 左手臂移动
        qHome;  % 回到初始位置
    ];
    
    for i = 1:size(positions, 1)
        fprintf('  位置 %d/%d\n', i, size(positions, 1));
        
        % 更新机器人显示
        show(yumi, positions(i, :), 'PreservePlot', false, 'Frames', 'off');
        
        % 更新标题
        title(sprintf('YuMi机器人运动演示 - 位置 %d/%d', i, size(positions, 1)), ...
              'FontSize', 14, 'FontWeight', 'bold');
        
        drawnow;
        pause(2); % 暂停2秒
    end
    
    fprintf('✅ 运动演示完成！\n');
    
    % 6. 显示完成信息
    title('🎉 YuMi机器人演示完成！', 'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    
    fprintf('\n🎉 === 演示完成 === 🎉\n');
    fprintf('您应该能在图形窗口中看到：\n');
    fprintf('1. YuMi机器人的3D模型\n');
    fprintf('2. 机器人的简单运动\n');
    fprintf('3. 坐标轴和网格\n');
    
    % 7. 提供下一步选项
    fprintf('\n💡 下一步可以尝试：\n');
    fprintf('1. 运行完整演示: yumiLegoDemo()\n');
    fprintf('2. 运行主程序: main\n');
    fprintf('3. 运行性能测试: testImprovedPlanner()\n');
    
catch ME
    fprintf('❌ 演示失败: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
end

end
