function cleanTrajectoryAnimation(yumi, qHome, trajectories, brick_config)
% 清洁版本的轨迹动画，解决残影问题
% 输入：
%   yumi - YuMi机器人模型
%   qHome - 初始关节角
%   trajectories - 轨迹序列
%   brick_config - 乐高配置（可选）

if nargin < 4
    % 如果没有提供brick_config，加载默认配置
    brick_config = lego_config();
end

% 關閉之前的圖形
close all;

fprintf('=== 启动清洁版YuMi机器人动画 ===\n');

% 创建新的图形窗口
fig = figure('Name', 'YuMi机器人轨迹动画 (无残影版)', ...
             'Position', [100, 100, 1200, 800], ...
             'Color', 'white');

% 设置坐标轴
ax = axes('Parent', fig);
hold(ax, 'on');

% 设置环境
[~, ~, ~, ~] = setupRobotEnv();
ax = gca;

% 设置视角和光照
view(ax, 45, 30);
lighting(ax, 'gouraud');
camlight(ax, 'headlight');

% 设置坐标轴属性
xlabel(ax, 'X (m)');
ylabel(ax, 'Y (m)');
zlabel(ax, 'Z (m)');
title(ax, 'YuMi机器人轨迹动画 (清洁版)', 'FontSize', 14, 'FontWeight', 'bold');

% 显示初始机器人配置
show(yumi, qHome, 'Parent', ax, 'PreservePlot', true, 'Frames', 'off');

% 检查是否有改进的轨迹（包含任务信息）
has_task_info = false;
if ~isempty(trajectories) && isfield(trajectories{1}, 'task')
    has_task_info = true;
    fprintf('检测到改进轨迹，将显示完整的抓取和放置动画\n');
    
    % 使用乐高可视化系统
    visualization_options = struct();
    visualization_options.animation_speed = 0.08;
    
    lego_viz = LegoVisualization(yumi, brick_config, visualization_options);
    lego_viz.animateTrajectoryWithLego(yumi, trajectories, qHome);
    return;
else
    fprintf('使用基础轨迹，将显示清洁版机器人运动轨迹\n');
end

% === 清洁版動畫參數設置 ===
colors = struct('left', [1 0 0], 'right', [0 0 1]); % 紅色=左手，藍色=右手
pauseTime = 0.05; % 较快的动画速度
show_trajectory_lines = true; % 是否显示轨迹线
max_trajectory_points = 50; % 限制轨迹线显示的最大点数

fprintf('\n=== 開始清洁版機器人軌跡動畫 ===\n');
total_trajectories = length(trajectories);
total_points = sum(cellfun(@(t) size(t.Q,1), trajectories));
fprintf('總軌跡數: %d\n', total_trajectories);
fprintf('總點數: %d\n', total_points);
fprintf('預估時間: %.1f 秒\n\n', total_points * pauseTime);

% 存储轨迹数据
all_trajectory_data = {};
current_point = 0;

for i = 1:length(trajectories)
    traj = trajectories{i};
    armColor = colors.(traj.arm);
    traj_points = size(traj.Q, 1);
    
    fprintf('軌跡 %d: %s 手臂 (%d 點)\n', i, traj.arm, traj_points);
    
    % 初始化当前轨迹数据
    current_traj_data = struct('x', [], 'y', [], 'z', []);
    
    % 获取末端执行器名称
    if strcmp(traj.arm, 'right')
        eeName = 'gripper_r_base';
    else
        eeName = 'gripper_l_base';
    end
    
    for j = 1:traj_points
        current_point = current_point + 1;
        currentQ = traj.Q(j, :);
        
        % 获取末端执行器位置
        try
            T = getTransform(yumi, currentQ, eeName);
            current_pos = T(1:3, 4)';
        catch
            % 如果获取变换失败，使用默认位置
            current_pos = [0.5, 0, 0.2];
        end
        
        % 添加到轨迹数据
        current_traj_data.x = [current_traj_data.x, current_pos(1)];
        current_traj_data.y = [current_traj_data.y, current_pos(2)];
        current_traj_data.z = [current_traj_data.z, current_pos(3)];
        
        % 每隔几个点更新一次显示（减少计算量）
        if mod(j, 3) == 1 || j == traj_points
            % 清除坐标轴内容（避免残影）
            cla(ax);
            
            % 重新设置坐标轴属性
            hold(ax, 'on');
            xlabel(ax, 'X (m)');
            ylabel(ax, 'Y (m)');
            zlabel(ax, 'Z (m)');
            view(ax, 45, 30);
            
            % 重新设置环境
            [~, ~, table, ~] = setupRobotEnv();
            
            % 显示机器人
            show(yumi, currentQ, 'Parent', ax, 'PreservePlot', true, 'Frames', 'off');
            
            % 显示轨迹线（如果启用）
            if show_trajectory_lines
                % 显示已完成的轨迹
                for k = 1:i-1
                    if ~isempty(all_trajectory_data{k}.x)
                        plot3(ax, all_trajectory_data{k}.x, all_trajectory_data{k}.y, all_trajectory_data{k}.z, ...
                              'Color', colors.(trajectories{k}.arm), 'LineWidth', 2, ...
                              'DisplayName', sprintf('%s 手臂軌跡 %d', trajectories{k}.arm, k));
                    end
                end
                
                % 显示当前轨迹（限制点数避免过于密集）
                if length(current_traj_data.x) > 1
                    % 如果点数过多，进行采样
                    if length(current_traj_data.x) > max_trajectory_points
                        indices = round(linspace(1, length(current_traj_data.x), max_trajectory_points));
                        x_data = current_traj_data.x(indices);
                        y_data = current_traj_data.y(indices);
                        z_data = current_traj_data.z(indices);
                    else
                        x_data = current_traj_data.x;
                        y_data = current_traj_data.y;
                        z_data = current_traj_data.z;
                    end
                    
                    plot3(ax, x_data, y_data, z_data, ...
                          'Color', armColor, 'LineWidth', 3, ...
                          'DisplayName', sprintf('%s 手臂軌跡 %d (当前)', traj.arm, i));
                end
            end
            
            % 显示当前位置标记
            plot3(ax, current_pos(1), current_pos(2), current_pos(3), ...
                  'o', 'MarkerSize', 10, 'MarkerFaceColor', armColor, ...
                  'MarkerEdgeColor', 'white', 'LineWidth', 2);
            
            % 计算进度
            total_progress = current_point / total_points * 100;
            traj_progress = j / traj_points * 100;
            
            % 更新標題
            title(ax, sprintf('YuMi轨迹动画 - 轨迹 %d/%d (%s臂) | 进度: %.1f%% | 总进度: %.1f%%', ...
                  i, total_trajectories, traj.arm, traj_progress, total_progress), ...
                  'FontSize', 12, 'FontWeight', 'bold');
            
            % 在命令窗口显示进度
            if mod(total_progress, 20) < 0.1
                fprintf('  总进度: %.0f%%\n', total_progress);
            end
            
            drawnow;
            pause(pauseTime);
        end
    end
    
    % 保存当前轨迹数据
    all_trajectory_data{i} = current_traj_data;
    
    % 轨迹完成标记
    plot3(ax, current_traj_data.x(end), current_traj_data.y(end), current_traj_data.z(end), ...
          's', 'MarkerSize', 12, 'MarkerFaceColor', 'red', ...
          'MarkerEdgeColor', 'black', 'LineWidth', 2, ...
          'DisplayName', sprintf('终点 %d', i));
    
    fprintf('  ✓ 軌跡 %d 完成\n', i);
    pause(0.8); % 軌跡間暫停
end

% 最终显示
cla(ax);
hold(ax, 'on');
xlabel(ax, 'X (m)');
ylabel(ax, 'Y (m)');
zlabel(ax, 'Z (m)');
view(ax, 45, 30);

% 重新设置环境
[~, ~, table, ~] = setupRobotEnv();

% 显示最终机器人状态
if ~isempty(trajectories)
    final_traj = trajectories{end};
    final_Q = final_traj.Q(end, :);
    show(yumi, final_Q, 'Parent', ax, 'PreservePlot', true, 'Frames', 'off');
end

% 显示所有完整轨迹
if show_trajectory_lines
    for i = 1:length(all_trajectory_data)
        if ~isempty(all_trajectory_data{i}.x)
            plot3(ax, all_trajectory_data{i}.x, all_trajectory_data{i}.y, all_trajectory_data{i}.z, ...
                  'Color', colors.(trajectories{i}.arm), 'LineWidth', 2, ...
                  'DisplayName', sprintf('%s 手臂軌跡 %d', trajectories{i}.arm, i));
            
            % 添加起点和终点标记
            plot3(ax, all_trajectory_data{i}.x(1), all_trajectory_data{i}.y(1), all_trajectory_data{i}.z(1), ...
                  '^', 'MarkerSize', 10, 'MarkerFaceColor', 'green', ...
                  'MarkerEdgeColor', 'black', 'LineWidth', 1);
            plot3(ax, all_trajectory_data{i}.x(end), all_trajectory_data{i}.y(end), all_trajectory_data{i}.z(end), ...
                  's', 'MarkerSize', 10, 'MarkerFaceColor', 'red', ...
                  'MarkerEdgeColor', 'black', 'LineWidth', 1);
        end
    end
end

% 添加图例
legend(ax, 'Location', 'best');

% 最终标题
title(ax, '🎉 YuMi机器人轨迹动画完成！', ...
      'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);

fprintf('\n🎉 === 清洁版动画完成 === 🎉\n');
fprintf('✓ 无残影显示\n');
fprintf('✓ 轨迹清晰可见\n');
fprintf('✓ 机器人运动流畅\n');

end
