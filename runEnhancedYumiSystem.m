function runEnhancedYumiSystem()
% 启动增强的YuMi乐高拼接系统
% 专注于可视化和真实的拼接动画

fprintf('🤖 === 启动增强YuMi乐高拼接系统 === 🤖\n');
fprintf('专注于可视化和真实拼接动画\n\n');

try
    %% 环境检查
    fprintf('🔍 环境检查...\n');
    
    % 检查必要文件
    required_files = {'mainbu.ldr', 'LDRParser.m'};
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            error('缺少必要文件: %s', required_files{i});
        end
        fprintf('  ✅ %s 存在\n', required_files{i});
    end
    
    % 检查工具箱
    if ~exist('loadrobot', 'file')
        error('需要Robotics System Toolbox');
    end
    fprintf('  ✅ Robotics System Toolbox 可用\n');
    
    %% 创建增强系统
    fprintf('\n🚀 创建增强YuMi拼接系统...\n');
    
    assembly_system = EnhancedYumiAssembly();
    
    %% 显示系统信息
    fprintf('\n📊 === 系统信息 === 📊\n');
    
    fprintf('🤖 YuMi机器人:\n');
    fprintf('   - 双臂结构: 7+7 自由度\n');
    fprintf('   - 真实机器人模型显示\n');
    fprintf('   - 可控制夹爪系统\n');
    
    fprintf('\n🏗️ 建筑信息:\n');
    fprintf('   - 设计文件: mainbu.ldr\n');
    fprintf('   - 积木总数: %d 个\n', length(assembly_system.target_bricks));
    fprintf('   - 拼接步骤: %d 步\n', assembly_system.total_steps);
    
    fprintf('\n🎯 增强功能:\n');
    fprintf('   ✅ 真实YuMi机器人显示\n');
    fprintf('   ✅ 逐步拼接动画\n');
    fprintf('   ✅ 机械臂运动轨迹\n');
    fprintf('   ✅ 夹爪开合动作\n');
    fprintf('   ✅ 实时状态反馈\n');
    fprintf('   ✅ 积木逐个放置\n');
    
    fprintf('\n🎮 操作说明:\n');
    fprintf('   ▶️  点击"开始拼接" - 启动完整拼接动画\n');
    fprintf('   ⏸️  点击"暂停" - 暂停当前动画\n');
    fprintf('   ⏹️  点击"停止" - 停止并重置\n');
    fprintf('   📊 拖动进度条 - 跳转到任意步骤\n');
    fprintf('   🤏 夹爪控制 - 手动控制左右夹爪\n');
    
    fprintf('\n🔧 动画流程:\n');
    fprintf('   每个拼接步骤包含:\n');
    fprintf('   1. 机械臂移动到积木供应区\n');
    fprintf('   2. 夹爪开启准备抓取\n');
    fprintf('   3. 夹爪关闭抓取积木\n');
    fprintf('   4. 机械臂移动到目标位置\n');
    fprintf('   5. 夹爪开启放置积木\n');
    fprintf('   6. 积木出现在目标位置\n');
    fprintf('   7. 机械臂返回待机位置\n');
    
    fprintf('\n🎉 === 系统启动完成 === 🎉\n');
    fprintf('💡 现在你可以看到:\n');
    fprintf('   - 完整的YuMi双臂机器人\n');
    fprintf('   - 真实的机械臂运动\n');
    fprintf('   - 夹爪的开合动作\n');
    fprintf('   - 积木的逐步拼接过程\n');
    fprintf('   - 严格按照mainbu.ldr的设计\n');
    
    fprintf('\n🚀 点击"开始拼接"按钮开始观看动画！\n');
    
catch ME
    fprintf('❌ 系统启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n🔧 故障排除:\n');
    fprintf('1. 确保安装了Robotics System Toolbox\n');
    fprintf('2. 确保mainbu.ldr和LDRParser.m文件存在\n');
    fprintf('3. 检查MATLAB版本兼容性\n');
    fprintf('4. 尝试重启MATLAB\n');
end

end
