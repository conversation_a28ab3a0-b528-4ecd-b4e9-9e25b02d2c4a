function createYumiSimscapeModel()
% 重新创建YuMi Simscape模型
% 解决版本兼容性问题

fprintf('=== 重新创建YuMi Simscape模型 ===\n');

try
    % 1. 加载YuMi机器人模型
    fprintf('1. 加载YuMi机器人模型...\n');
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    fprintf('✓ YuMi模型加载成功\n');
    
    % 2. 检查是否存在旧的模型文件
    modelName = 'YumiSimscape';
    if bdIsLoaded(modelName)
        fprintf('2. 关闭现有模型...\n');
        close_system(modelName, 0);
    end
    
    % 删除旧的缓存文件
    if exist([modelName '.slxc'], 'file')
        fprintf('3. 删除旧缓存文件...\n');
        delete([modelName '.slxc']);
        fprintf('✓ 缓存文件已删除\n');
    end
    
    % 3. 导出URDF文件
    fprintf('4. 导出URDF文件...\n');
    urdf_filename = 'yumi_exported.urdf';
    
    try
        exportrobot(yumi, urdf_filename);
        fprintf('✓ URDF文件导出成功: %s\n', urdf_filename);
    catch ME
        fprintf('⚠ URDF导出失败: %s\n', ME.message);
        fprintf('  继续使用现有URDF文件...\n');
    end
    
    % 4. 创建新的Simscape模型
    fprintf('5. 创建Simscape模型...\n');
    
    if exist(urdf_filename, 'file')
        try
            % 使用smimport创建Simscape模型
            smimport(urdf_filename, 'ModelName', modelName);
            fprintf('✓ Simscape模型创建成功: %s.slx\n', modelName);
            
            % 保存模型
            save_system(modelName);
            fprintf('✓ 模型已保存\n');
            
        catch ME
            fprintf('❌ Simscape模型创建失败: %s\n', ME.message);
            fprintf('   尝试替代方案...\n');
            createBasicSimulinkModel(modelName, yumi);
        end
    else
        fprintf('⚠ URDF文件不存在，创建基础Simulink模型...\n');
        createBasicSimulinkModel(modelName, yumi);
    end
    
    % 5. 验证模型
    fprintf('6. 验证模型...\n');
    if exist([modelName '.slx'], 'file')
        try
            open_system(modelName);
            fprintf('✓ 模型验证成功，已打开\n');
            
            % 设置基本参数
            set_param(modelName, 'StopTime', '10');
            set_param(modelName, 'SolverType', 'Variable-step');
            set_param(modelName, 'Solver', 'ode45');
            
            fprintf('✓ 模型参数设置完成\n');
            
        catch ME
            fprintf('❌ 模型验证失败: %s\n', ME.message);
        end
    else
        fprintf('❌ 模型文件不存在\n');
    end
    
    fprintf('\n🎉 YuMi Simscape模型重建完成！\n');
    fprintf('💡 提示: 现在可以运行 runSimulink() 进行仿真\n');
    
catch ME
    fprintf('\n❌ 模型重建过程出错:\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    fprintf('\n🔧 故障排除建议:\n');
    fprintf('1. 确认MATLAB Robotics Toolbox已安装\n');
    fprintf('2. 确认Simscape Multibody已安装\n');
    fprintf('3. 尝试手动删除所有.slxc文件\n');
    fprintf('4. 重启MATLAB后重试\n');
end

end

function createBasicSimulinkModel(modelName, yumi)
% 创建基础的Simulink模型作为备选方案

fprintf('  创建基础Simulink模型...\n');

try
    % 创建新的Simulink模型
    new_system(modelName);
    open_system(modelName);
    
    % 添加基本模块
    % 注意：这是一个简化的模型，主要用于数据传输
    
    % 添加输入端口
    add_block('simulink/Sources/In1', [modelName '/RightArmInput']);
    add_block('simulink/Sources/In1', [modelName '/LeftArmInput']);
    
    % 添加输出端口
    add_block('simulink/Sinks/Out1', [modelName '/RightArmOutput']);
    add_block('simulink/Sinks/Out1', [modelName '/LeftArmOutput']);
    
    % 添加示波器用于可视化
    add_block('simulink/Sinks/Scope', [modelName '/RightArmScope']);
    add_block('simulink/Sinks/Scope', [modelName '/LeftArmScope']);
    
    % 设置位置
    set_param([modelName '/RightArmInput'], 'Position', [50, 50, 80, 70]);
    set_param([modelName '/LeftArmInput'], 'Position', [50, 150, 80, 170]);
    set_param([modelName '/RightArmOutput'], 'Position', [300, 50, 330, 70]);
    set_param([modelName '/LeftArmOutput'], 'Position', [300, 150, 330, 170]);
    set_param([modelName '/RightArmScope'], 'Position', [200, 80, 250, 120]);
    set_param([modelName '/LeftArmScope'], 'Position', [200, 180, 250, 220]);
    
    % 连接信号线
    add_line(modelName, 'RightArmInput/1', 'RightArmOutput/1');
    add_line(modelName, 'LeftArmInput/1', 'LeftArmOutput/1');
    add_line(modelName, 'RightArmInput/1', 'RightArmScope/1');
    add_line(modelName, 'LeftArmInput/1', 'LeftArmScope/1');
    
    % 保存模型
    save_system(modelName);
    
    fprintf('✓ 基础Simulink模型创建成功\n');
    fprintf('⚠ 注意: 这是简化模型，不包含3D可视化\n');
    
catch ME
    fprintf('❌ 基础模型创建失败: %s\n', ME.message);
end

end
