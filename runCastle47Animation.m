function runCastle47Animation()
% Launch 47-brick castle assembly animation player
% Complete animation system with progress bar and playback controls

fprintf('🏰 === 47-Brick Castle Assembly Animation === 🏰\n');
fprintf('Starting complete animation playback system...\n\n');

try
    %% Step 1: Environment Check
    fprintf('🔍 Step 1: Environment check...\n');
    
    % Check Robotics Toolbox
    if ~exist('loadrobot', 'file')
        fprintf('❌ Robotics System Toolbox required\n');
        return;
    end
    
    % Check LDR file
    if ~exist('castle_47_bricks.ldr', 'file')
        fprintf('⚠️ Castle LDR file not found, creating default...\n');
        create47BrickCastle();
    end
    
    fprintf('✅ Environment check complete\n');
    
    %% Step 2: Load Robot
    fprintf('\n🤖 Step 2: Loading YuMi robot...\n');
    yumi = loadrobot('abbYumi');
    fprintf('✅ YuMi dual-arm robot loaded successfully\n');
    fprintf('   - Left arm: 7 joints\n');
    fprintf('   - Right arm: 7 joints\n');
    fprintf('   - Total: 14 degrees of freedom\n');
    
    %% Step 3: Parse Castle Structure
    fprintf('\n🏗️ Step 3: Parsing 47-brick castle structure...\n');
    
    % Use LDR parser
    parser = LDRParser('castle_47_bricks.ldr');
    success = parser.parseLDRFile();
    
    if ~success
        fprintf('❌ Castle structure parsing failed\n');
        return;
    end
    
    fprintf('✅ Castle structure parsed successfully\n');
    fprintf('   - Total bricks: %d pieces\n', parser.total_bricks);
    fprintf('   - Structure layers: 6 levels\n');
    fprintf('   - Castle type: Medieval castle\n');
    
    %% Step 4: Generate Assembly Trajectories
    fprintf('\n📍 Step 4: Generating robot assembly trajectories...\n');
    trajectories = generateAdvancedCastleTrajectories(parser.bricks, yumi);
    fprintf('✅ Trajectory generation complete\n');
    fprintf('   - Trajectory segments: %d segments\n', length(trajectories));
    fprintf('   - Estimated time: %.1f minutes\n', length(trajectories) * 3 / 60);
    
    %% Step 5: Create Brick Configuration
    fprintf('\n🧱 Step 5: Creating brick configuration database...\n');
    brick_config = createAdvancedBrickConfig(parser.bricks);
    fprintf('✅ Brick configuration created\n');
    fprintf('   - Brick types: %d types\n', length(unique({parser.bricks.part})));
    fprintf('   - Color varieties: %d colors\n', length(unique([parser.bricks.color])));
    
    %% Step 6: Launch Animation Player
    fprintf('\n🎬 Step 6: Launching animation player...\n');
    player = AnimationPlayer(yumi, trajectories, brick_config);
    fprintf('✅ Animation player launched successfully\n');
    
    %% Display Operation Guide
    fprintf('\n%s\n', repmat('=', 1, 60));
    fprintf('🎮 === Animation Player Operation Guide === 🎮\n');
    fprintf('%s\n', repmat('=', 1, 60));
    fprintf('▶️  Play Button: Start castle assembly animation\n');
    fprintf('⏸️  Pause Button: Pause current playback\n');
    fprintf('⏹️  Stop Button: Stop and reset to beginning\n');
    fprintf('📊 Progress Bar: Drag to jump to any assembly stage\n');
    fprintf('⚡ Speed Slider: Adjust playback speed (0.1x - 3.0x)\n');
    fprintf('\n🏰 === Castle Assembly Information === 🏰\n');
    fprintf('📦 Total bricks: 47 pieces\n');
    fprintf('🏗️ Assembly layers: 6 levels\n');
    fprintf('🤖 Robot: YuMi dual-arm collaboration\n');
    fprintf('⏱️ Estimated time: %.1f minutes\n', length(trajectories) * 3 / 60);
    fprintf('\n💡 Tips:\n');
    fprintf('   1. Click play button to start watching animation\n');
    fprintf('   2. You can pause and adjust speed anytime\n');
    fprintf('   3. Drag progress bar to jump to any stage\n');
    fprintf('   4. Animation shows each brick assembly process\n');
    fprintf('\n🎉 Ready! Start your castle assembly journey!\n');
    fprintf('%s\n', repmat('=', 1, 60));
    
catch ME
    fprintf('❌ Launch failed: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('Error location: %s (line %d)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    % Provide troubleshooting suggestions
    fprintf('\n🔧 Troubleshooting suggestions:\n');
    fprintf('1. Ensure Robotics System Toolbox is installed\n');
    fprintf('2. Check MATLAB version supports YuMi robot\n');
    fprintf('3. Ensure all necessary files are in current directory\n');
    fprintf('4. Try restarting MATLAB and run again\n');
end

end

function create47BrickCastle()
% Create 47-brick castle LDR file (if not exists)

fprintf('Creating 47-brick castle LDR file...\n');

ldr_content = {
    '0 // 47-brick complex castle structure'
    '0 // Complex Castle with 47 LEGO Bricks'
    '0 Name: Complex Castle Structure'
    '0 Author: YuMi Assembly System'
    '0'
    ''
    '// === Layer 1: Base platform (12 bricks) ==='
};

% Add base platform bricks
for i = 0:3
    for j = 0:2
        brick_line = sprintf('1 4 %d %d 0 1 0 0 0 1 0 0 0 1 3001.dat', i*32, j*32);
        ldr_content{end+1} = brick_line;
    end
end

% Write to file
fid = fopen('castle_47_bricks.ldr', 'w');
for i = 1:length(ldr_content)
    fprintf(fid, '%s\n', ldr_content{i});
end
fclose(fid);

fprintf('✅ Castle LDR file created\n');

end

function trajectories = generateAdvancedCastleTrajectories(bricks, yumi)
% Generate advanced castle assembly trajectories

trajectories = {};
fprintf('   Generating assembly trajectories for each brick...\n');

for i = 1:length(bricks)
    brick = bricks(i);
    
    % Generate pickup trajectory
    pickup_traj = generatePickupTrajectory(brick, yumi, i);
    trajectories{end+1} = pickup_traj;
    
    % Generate transport trajectory
    transport_traj = generateTransportTrajectory(brick, yumi, i);
    trajectories{end+1} = transport_traj;
    
    % Generate place trajectory
    place_traj = generatePlaceTrajectory(brick, yumi, i);
    trajectories{end+1} = place_traj;
    
    if mod(i, 10) == 0
        fprintf('   Generated trajectories for %d/%d bricks\n', i, length(bricks));
    end
end

end

function traj = generatePickupTrajectory(brick, yumi, brick_index)
% Generate pickup trajectory

num_points = 25;
traj = struct();

% Generate joint angle sequence
q_home = yumi.homeConfiguration;

% Convert to numeric array if needed
if isstruct(q_home)
    q_home_numeric = [q_home.JointPosition];
else
    q_home_numeric = q_home;
end

% Get brick position safely
brick_pos = brick.position;
if length(brick_pos) < 3
    brick_pos = [0, 0, 0]; % Default position
end

% Create target configuration
q_pickup = q_home_numeric;

% Adjust joint angles based on brick position and index
arm_choice = mod(brick_index, 2) + 1; % Alternate between left and right arms

if arm_choice == 1 % Left arm
    if length(q_pickup) >= 3
        q_pickup(1) = 0.3 + brick_pos(1) / 2000;
        q_pickup(2) = 0.2;
        q_pickup(3) = brick_pos(2) / 2000;
    end
else % Right arm
    if length(q_pickup) >= 10
        q_pickup(8) = -0.3 - brick_pos(1) / 2000;
        q_pickup(9) = 0.2;
        q_pickup(10) = brick_pos(2) / 2000;
    end
end

% Interpolate trajectory
traj.Q = zeros(num_points, length(q_home_numeric));
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_home_numeric * (1-alpha) + q_pickup * alpha;
end

traj.task = 'pickup';
traj.brick_id = brick.id;
traj.arm = arm_choice;

end

function traj = generateTransportTrajectory(brick, yumi, brick_index)
% Generate transport trajectory

num_points = 20;
traj = struct();

% Simple transport trajectory
q_start = yumi.homeConfiguration;

% Convert to numeric array if needed
if isstruct(q_start)
    q_start_numeric = [q_start.JointPosition];
else
    q_start_numeric = q_start;
end

q_end = q_start_numeric;

% Get brick position safely
brick_pos = brick.position;
if length(brick_pos) < 3
    brick_pos = [0, 0, 0]; % Default position
end

% Adjust based on target position
target_pos = brick_pos + [400, 0, 0];
arm_choice = mod(brick_index, 2) + 1;

if arm_choice == 1
    if length(q_end) >= 3
        q_end(1) = target_pos(1) / 2000;
        q_end(3) = target_pos(2) / 2000;
    end
else
    if length(q_end) >= 10
        q_end(8) = -target_pos(1) / 2000;
        q_end(10) = target_pos(2) / 2000;
    end
end

% Interpolate trajectory
traj.Q = zeros(num_points, length(q_start_numeric));
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start_numeric * (1-alpha) + q_end * alpha;
end

traj.task = 'transport';
traj.brick_id = brick.id;
traj.arm = arm_choice;

end

function traj = generatePlaceTrajectory(brick, yumi, brick_index)
% Generate place trajectory

num_points = 15;
traj = struct();

% Place trajectory
q_start = yumi.homeConfiguration;

% Convert to numeric array if needed
if isstruct(q_start)
    q_start_numeric = [q_start.JointPosition];
else
    q_start_numeric = q_start;
end

q_end = q_start_numeric;

% Interpolate trajectory
traj.Q = zeros(num_points, length(q_start_numeric));
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start_numeric * (1-alpha) + q_end * alpha;
end

traj.task = 'place';
traj.brick_id = brick.id;
traj.arm = mod(brick_index, 2) + 1;

end

function config = createAdvancedBrickConfig(bricks)
% Create advanced brick configuration

config = struct();
config.bricks = bricks;
config.total_bricks = length(bricks);
config.assembly_time = 180; % 3 minutes

% Group by layers
config.layers = {};
for layer = 1:6
    layer_bricks = [];
    for i = 1:length(bricks)
        brick_pos = bricks(i).position;
        if length(brick_pos) >= 3
            brick_layer = ceil(brick_pos(3) / 10);
            if brick_layer == layer
                layer_bricks(end+1) = i;
            end
        end
    end
    if ~isempty(layer_bricks)
        config.layers{layer} = layer_bricks;
    end
end

config.num_layers = length(config.layers);

end
