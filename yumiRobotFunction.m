function [robotState, endEffectorPos, gripperState] = yumiRobotFunction(armSignals, gripperSignal)
% YuMi Robot Control Function for Simulink
% This function goes in the "YuMi Robot" MATLAB Function block

% Persistent variables to maintain state
persistent robot qHome isInitialized currentTime lastGripperState;

% Initialize robot on first call
if isempty(isInitialized)
    try
        fprintf('Loading YuMi robot...\n');
        robot = loadrobot('abbYumi');
        qHome = robot.homeConfiguration;
        isInitialized = true;
        currentTime = 0;
        lastGripperState = 0;
        fprintf('SUCCESS: YuMi robot loaded with %d joints\n', length(qHome));
    catch ME
        fprintf('WARNING: Could not load YuMi robot: %s\n', ME.message);
        robot = [];
        qHome = [];
        isInitialized = false;
    end
end

% Update time
currentTime = currentTime + 0.1;

% Process arm signals
if length(armSignals) >= 2
    rightArmSignal = armSignals(1);
    leftArmSignal = armSignals(2);
else
    rightArmSignal = 0;
    leftArmSignal = 0;
end

% Generate robot configuration
if ~isempty(robot)
    try
        % Start with home configuration
        q = qHome;
        
        % Right arm motion (joints 1-7)
        q(1).JointPosition = rightArmSignal * 0.8;                    % Shoulder yaw
        q(2).JointPosition = -0.3 + 0.2 * cos(rightArmSignal * 2);   % Shoulder pitch
        q(3).JointPosition = 0.1 * sin(rightArmSignal * 3);          % Shoulder roll
        q(4).JointPosition = -0.5 + 0.3 * cos(rightArmSignal);       % Elbow
        q(5).JointPosition = 0.1 * sin(rightArmSignal * 2);          % Wrist yaw
        q(6).JointPosition = 0.2 * cos(rightArmSignal * 1.5);        % Wrist pitch
        q(7).JointPosition = 0.1 * sin(rightArmSignal);              % Wrist roll
        
        % Left arm motion (joints 8-14) - opposite phase
        q(8).JointPosition = -leftArmSignal * 0.8;                   % Shoulder yaw
        q(9).JointPosition = -0.3 + 0.2 * cos(leftArmSignal * 2 + pi); % Shoulder pitch
        q(10).JointPosition = -0.1 * sin(leftArmSignal * 3);         % Shoulder roll
        q(11).JointPosition = -0.5 + 0.3 * cos(leftArmSignal + pi);  % Elbow
        q(12).JointPosition = -0.1 * sin(leftArmSignal * 2);         % Wrist yaw
        q(13).JointPosition = 0.2 * cos(leftArmSignal * 1.5 + pi);   % Wrist pitch
        q(14).JointPosition = -0.1 * sin(leftArmSignal);             % Wrist roll
        
        % Gripper control (joints 15-18)
        gripperPos = gripperSignal * 0.02;  % Convert to gripper position
        if length(q) >= 18
            q(15).JointPosition = gripperPos;      % Right gripper finger 1
            q(16).JointPosition = -gripperPos;     % Right gripper finger 2
            q(17).JointPosition = gripperPos;      % Left gripper finger 1
            q(18).JointPosition = -gripperPos;     % Left gripper finger 2
        end
        
        % Calculate end effector positions
        try
            T_right = getTransform(robot, q, 'yumi_link_7_r');
            T_left = getTransform(robot, q, 'yumi_link_7_l');
            endEffectorPos = [T_right(1:3, 4)', T_left(1:3, 4)'];
        catch
            % Fallback end effector calculation
            endEffectorPos = [0.5 + 0.2*sin(rightArmSignal), 0.2*cos(rightArmSignal), 0.3, ...
                             0.5 - 0.2*sin(leftArmSignal), -0.2*cos(leftArmSignal), 0.3];
        end
        
        % Extract joint positions for output
        robotState = [q.JointPosition];
        
    catch ME
        fprintf('Robot calculation error: %s\n', ME.message);
        % Fallback calculation
        robotState = [rightArmSignal, -0.3, 0, -0.5, 0, 0, 0, ...
                     -leftArmSignal, -0.3, 0, -0.5, 0, 0, 0, ...
                     gripperSignal*0.02, -gripperSignal*0.02, ...
                     gripperSignal*0.02, -gripperSignal*0.02];
        endEffectorPos = [0.5 + 0.2*sin(rightArmSignal), 0.2*cos(rightArmSignal), 0.3, ...
                         0.5 - 0.2*sin(leftArmSignal), -0.2*cos(leftArmSignal), 0.3];
    end
else
    % Simple fallback when robot not available
    robotState = [rightArmSignal, -0.3, 0, -0.5, 0, 0, 0, ...
                 -leftArmSignal, -0.3, 0, -0.5, 0, 0, 0, ...
                 gripperSignal*0.02, -gripperSignal*0.02, ...
                 gripperSignal*0.02, -gripperSignal*0.02];
    endEffectorPos = [0.5 + 0.2*sin(rightArmSignal), 0.2*cos(rightArmSignal), 0.3, ...
                     0.5 - 0.2*sin(leftArmSignal), -0.2*cos(leftArmSignal), 0.3];
end

% Process gripper state
if gripperSignal > 0.5
    gripperState = 1;  % Closed
else
    gripperState = 0;  % Open
end

% Display gripper state changes
if abs(gripperState - lastGripperState) > 0.5
    if gripperState
        fprintf('[%.1fs] Grippers CLOSED - Grasping Lego blocks\n', currentTime);
    else
        fprintf('[%.1fs] Grippers OPEN - Releasing Lego blocks\n', currentTime);
    end
    lastGripperState = gripperState;
end

% Display motion status
if mod(currentTime, 2) < 0.1  % Every 2 seconds
    fprintf('[%.1fs] YuMi Status - Right: %.2f, Left: %.2f, Gripper: %s\n', ...
            currentTime, rightArmSignal, leftArmSignal, ...
            gripperState ? 'CLOSED' : 'OPEN');
end

end
