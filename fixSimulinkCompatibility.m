function fixSimulinkCompatibility()
% 修复Simulink版本兼容性问题
% 解决R2024a与R2024b之间的缓存文件冲突

fprintf('🔧 === Simulink版本兼容性修复工具 === 🔧\n\n');

try
    % 1. 检查MATLAB版本
    fprintf('1. 检查MATLAB版本信息...\n');
    matlab_version = version('-release');
    fprintf('   当前MATLAB版本: %s\n', matlab_version);
    
    % 2. 检查问题文件
    fprintf('\n2. 检查问题文件...\n');
    problem_files = dir('*.slxc');
    
    if isempty(problem_files)
        fprintf('   ✓ 未发现.slxc缓存文件\n');
    else
        fprintf('   发现 %d 个缓存文件:\n', length(problem_files));
        for i = 1:length(problem_files)
            fprintf('     - %s\n', problem_files(i).name);
        end
    end
    
    % 3. 检查Simulink模型
    fprintf('\n3. 检查Simulink模型文件...\n');
    model_files = dir('*.slx');
    
    if isempty(model_files)
        fprintf('   ⚠ 未发现.slx模型文件\n');
    else
        fprintf('   发现 %d 个模型文件:\n', length(model_files));
        for i = 1:length(model_files)
            fprintf('     - %s\n', model_files(i).name);
        end
    end
    
    % 4. 执行修复
    fprintf('\n4. 执行修复操作...\n');
    
    % 4.1 关闭所有Simulink模型
    fprintf('   4.1 关闭所有打开的Simulink模型...\n');
    try
        bdclose('all');
        fprintf('       ✓ 所有模型已关闭\n');
    catch
        fprintf('       ⚠ 部分模型可能未正确关闭\n');
    end
    
    % 4.2 删除缓存文件
    fprintf('   4.2 删除缓存文件...\n');
    if ~isempty(problem_files)
        for i = 1:length(problem_files)
            try
                delete(problem_files(i).name);
                fprintf('       ✓ 已删除: %s\n', problem_files(i).name);
            catch ME
                fprintf('       ❌ 删除失败: %s (%s)\n', problem_files(i).name, ME.message);
            end
        end
    else
        fprintf('       ✓ 无需删除缓存文件\n');
    end
    
    % 4.3 清理Simulink缓存
    fprintf('   4.3 清理Simulink缓存...\n');
    try
        Simulink.fileGenControl('reset');
        fprintf('       ✓ Simulink缓存已重置\n');
    catch ME
        fprintf('       ⚠ 缓存重置失败: %s\n', ME.message);
    end
    
    % 4.4 检查YuMi模型
    fprintf('   4.4 检查YuMi模型...\n');
    yumiModelName = 'YumiSimscape';
    
    if exist([yumiModelName '.slx'], 'file')
        fprintf('       ✓ 发现YuMi模型文件\n');
        
        % 尝试打开模型
        try
            fprintf('       尝试打开模型...\n');
            open_system(yumiModelName);
            fprintf('       ✓ 模型打开成功\n');
            
            % 检查模型版本
            model_version = get_param(yumiModelName, 'LastModifiedBy');
            fprintf('       模型信息: %s\n', model_version);
            
        catch ME
            fprintf('       ❌ 模型打开失败: %s\n', ME.message);
            fprintf('       建议重新创建模型\n');
            
            % 询问是否重新创建
            fprintf('\n是否重新创建YuMi Simscape模型? (y/n): ');
            user_input = input('', 's');
            
            if strcmpi(user_input, 'y')
                fprintf('       开始重新创建模型...\n');
                createYumiSimscapeModel();
            end
        end
    else
        fprintf('       ⚠ 未发现YuMi模型文件\n');
        fprintf('       建议创建新的模型\n');
        
        % 询问是否创建
        fprintf('\n是否创建YuMi Simscape模型? (y/n): ');
        user_input = input('', 's');
        
        if strcmpi(user_input, 'y')
            fprintf('       开始创建模型...\n');
            createYumiSimscapeModel();
        end
    end
    
    % 5. 验证修复结果
    fprintf('\n5. 验证修复结果...\n');
    
    % 检查缓存文件是否已清理
    remaining_cache = dir('*.slxc');
    if isempty(remaining_cache)
        fprintf('   ✓ 缓存文件已完全清理\n');
    else
        fprintf('   ⚠ 仍有 %d 个缓存文件残留\n', length(remaining_cache));
    end
    
    % 检查模型是否可用
    if exist([yumiModelName '.slx'], 'file')
        try
            if bdIsLoaded(yumiModelName)
                fprintf('   ✓ YuMi模型已加载并可用\n');
            else
                open_system(yumiModelName);
                fprintf('   ✓ YuMi模型测试打开成功\n');
                close_system(yumiModelName, 0);
            end
        catch
            fprintf('   ❌ YuMi模型仍有问题\n');
        end
    end
    
    % 6. 提供使用建议
    fprintf('\n6. 使用建议:\n');
    fprintf('   ✓ 修复完成，现在可以尝试运行仿真\n');
    fprintf('   ✓ 建议使用: yumiLegoDemo() 进行完整演示\n');
    fprintf('   ✓ 如果仍有问题，建议重启MATLAB\n');
    
    % 7. 测试基本功能
    fprintf('\n7. 测试基本功能...\n');
    fprintf('是否测试YuMi机器人加载? (y/n): ');
    test_input = input('', 's');
    
    if strcmpi(test_input, 'y')
        try
            fprintf('   加载YuMi机器人模型...\n');
            yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
            fprintf('   ✓ YuMi机器人模型加载成功\n');
            
            fprintf('   测试基本显示...\n');
            figure('Name', '测试YuMi显示');
            show(yumi, yumi.homeConfiguration);
            title('YuMi机器人测试显示');
            fprintf('   ✓ YuMi显示测试成功\n');
            
        catch ME
            fprintf('   ❌ YuMi测试失败: %s\n', ME.message);
        end
    end
    
    fprintf('\n🎉 === 修复完成 === 🎉\n');
    fprintf('现在可以正常使用YuMi仿真系统了！\n');
    
catch ME
    fprintf('\n❌ 修复过程中发生错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    fprintf('\n🔧 手动修复步骤:\n');
    fprintf('1. 手动删除所有.slxc文件\n');
    fprintf('2. 重启MATLAB\n');
    fprintf('3. 运行 createYumiSimscapeModel()\n');
    fprintf('4. 如果问题持续，请检查MATLAB工具箱安装\n');
end

end
