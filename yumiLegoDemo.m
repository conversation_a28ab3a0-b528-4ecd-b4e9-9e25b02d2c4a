function yumiLegoDemo()
% YuMi机器人乐高堆叠完整演示
% 展示从轨迹规划到3D动画的完整流程

clc; clear; close all;

fprintf('🤖 ===== YuMi机器人乐高堆叠系统演示 ===== 🧱\n\n');

try
    %% 1. 系统初始化
    fprintf('📋 第1步: 初始化YuMi机器人系统...\n');
    [yumi, qHome, table, ax] = setupRobotEnv();
    fprintf('✓ YuMi机器人模型加载完成\n');
    fprintf('✓ 工作环境设置完成\n');
    
    %% 2. 乐高配置加载
    fprintf('\n📋 第2步: 加载乐高积木配置...\n');
    brick_config = lego_config();
    
    % 为演示目的，限制任务数量
    original_tasks = length(brick_config.task_sequence);
    demo_tasks = min(6, original_tasks); % 最多演示6个任务
    brick_config.task_sequence = brick_config.task_sequence(1:demo_tasks);
    
    fprintf('✓ 乐高配置加载完成\n');
    fprintf('✓ 演示任务数量: %d (原始: %d)\n', demo_tasks, original_tasks);
    fprintf('✓ 目标位置数量: %d\n', size(brick_config.all_targets, 1));
    
    %% 3. 轨迹规划方法选择
    fprintf('\n📋 第3步: 选择轨迹规划方法...\n');
    fprintf('1. 原始方法 (基础轨迹规划)\n');
    fprintf('2. 改进方法 (RRT + B-spline + 协调)\n');
    fprintf('3. 自动选择最佳方法\n');
    
    method_choice = input('请选择方法 (1-3, 默认2): ');
    if isempty(method_choice)
        method_choice = 2;
    end
    
    %% 4. 执行轨迹规划
    fprintf('\n📋 第4步: 执行轨迹规划...\n');
    tic;
    
    switch method_choice
        case 1
            fprintf('使用原始轨迹规划方法...\n');
            trajectories = planTrajectory(yumi, brick_config, qHome);
            planning_method = '原始方法';
            
        case 2
            fprintf('使用改进轨迹规划方法...\n');
            options = struct();
            options.planning_mode = 'advanced';
            options.coordination_mode = 'adaptive';
            options.safety_margin = 0.08;
            options.rrt_max_iterations = 2000; % 适中的迭代次数
            options.max_velocity = 1.0;
            options.max_acceleration = 2.0;
            
            trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options);
            planning_method = '改进方法';
            
        case 3
            fprintf('自动选择最佳方法...\n');
            % 先尝试改进方法，如果失败则使用原始方法
            try
                options = struct();
                options.planning_mode = 'advanced';
                options.coordination_mode = 'adaptive';
                trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options);
                planning_method = '改进方法(自动)';
            catch
                fprintf('改进方法失败，回退到原始方法...\n');
                trajectories = planTrajectory(yumi, brick_config, qHome);
                planning_method = '原始方法(回退)';
            end
            
        otherwise
            fprintf('无效选择，使用默认改进方法\n');
            options = struct();
            options.planning_mode = 'advanced';
            trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options);
            planning_method = '改进方法(默认)';
    end
    
    planning_time = toc;
    
    if isempty(trajectories)
        error('轨迹规划失败，无法继续演示');
    end
    
    fprintf('✓ 轨迹规划完成 (%s)\n', planning_method);
    fprintf('✓ 规划时间: %.2f 秒\n', planning_time);
    fprintf('✓ 生成轨迹数: %d\n', length(trajectories));
    
    %% 5. 轨迹质量分析
    fprintf('\n📋 第5步: 轨迹质量分析...\n');
    analyzeTrajectoryQuality(trajectories);
    
    %% 6. 可视化演示选择
    fprintf('\n📋 第6步: 选择演示方式...\n');
    fprintf('1. 🎬 MATLAB 3D动画 (包含乐高积木可视化)\n');
    fprintf('2. 🤖 Simulink 3D仿真 (YuMi机器人模型)\n');
    fprintf('3. 📊 轨迹分析图表\n');
    fprintf('4. 🎯 完整演示 (动画 + 仿真 + 分析)\n');
    
    demo_choice = input('请选择演示方式 (1-4, 默认4): ');
    if isempty(demo_choice)
        demo_choice = 4;
    end
    
    %% 7. 执行演示
    fprintf('\n📋 第7步: 执行演示...\n');
    
    switch demo_choice
        case 1
            % MATLAB 3D动画
            fprintf('🎬 启动MATLAB 3D动画演示...\n');
            runMatlabAnimation(yumi, qHome, trajectories, brick_config);
            
        case 2
            % Simulink 3D仿真
            fprintf('🤖 启动Simulink 3D仿真演示...\n');
            runSimulinkDemo(trajectories);
            
        case 3
            % 轨迹分析
            fprintf('📊 生成轨迹分析图表...\n');
            plotTrajectoryAnalysis(trajectories);
            
        case 4
            % 完整演示
            fprintf('🎯 启动完整演示...\n');
            runCompleteDemo(yumi, qHome, trajectories, brick_config);
            
        otherwise
            fprintf('无效选择，执行默认动画演示\n');
            runMatlabAnimation(yumi, qHome, trajectories, brick_config);
    end
    
    %% 8. 演示总结
    fprintf('\n🎉 ===== 演示完成 ===== 🎉\n');
    fprintf('📊 演示统计:\n');
    fprintf('  - 规划方法: %s\n', planning_method);
    fprintf('  - 规划时间: %.2f 秒\n', planning_time);
    fprintf('  - 轨迹数量: %d\n', length(trajectories));
    fprintf('  - 任务数量: %d\n', demo_tasks);
    fprintf('  - 系统状态: 正常\n');
    
    fprintf('\n💡 提示:\n');
    fprintf('  - 可以重新运行 yumiLegoDemo() 尝试不同的选项\n');
    fprintf('  - 使用 testImprovedPlanner() 进行性能对比测试\n');
    fprintf('  - 查看 README_改进系统使用指南.md 了解更多功能\n');
    
catch ME
    fprintf('\n❌ 演示过程中发生错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    fprintf('\n🔧 故障排除建议:\n');
    fprintf('1. 检查YuMi机器人模型是否正确加载\n');
    fprintf('2. 确认所有必要的文件都在当前路径中\n');
    fprintf('3. 尝试运行 testImprovedPlanner() 进行诊断\n');
end

end

function runMatlabAnimation(yumi, qHome, trajectories, brick_config)
% 运行MATLAB 3D动画
fprintf('  🎬 初始化3D动画系统...\n');
try
    animateTrajectory(yumi, qHome, trajectories, brick_config);
    fprintf('  ✓ MATLAB动画演示完成\n');
catch ME
    fprintf('  ❌ MATLAB动画失败: %s\n', ME.message);
end
end

function runSimulinkDemo(trajectories)
% 运行Simulink演示
fprintf('  🤖 启动Simulink 3D仿真...\n');
try
    T_total = 12;
    simulink_options = struct();
    simulink_options.show_3d_animation = true;
    simulink_options.real_time_factor = 0.8;
    simulink_options.save_animation = false;
    
    runSimulink(trajectories, T_total, simulink_options);
    fprintf('  ✓ Simulink仿真演示完成\n');
catch ME
    fprintf('  ❌ Simulink仿真失败: %s\n', ME.message);
end
end

function plotTrajectoryAnalysis(trajectories)
% 绘制轨迹分析图表
fprintf('  📊 生成轨迹分析图表...\n');

try
    figure('Name', '轨迹质量分析', 'Position', [100, 100, 1000, 600]);
    
    % 子图1：轨迹点数统计
    subplot(2, 3, 1);
    trajectory_lengths = cellfun(@(t) size(t.Q, 1), trajectories);
    bar(trajectory_lengths);
    title('各轨迹点数');
    xlabel('轨迹编号');
    ylabel('点数');
    grid on;
    
    % 子图2：左右手分布
    subplot(2, 3, 2);
    arms = cellfun(@(t) t.arm, trajectories, 'UniformOutput', false);
    arm_counts = [sum(strcmp(arms, 'left')), sum(strcmp(arms, 'right'))];
    pie(arm_counts, {'左手', '右手'});
    title('左右手任务分布');
    
    % 子图3：轨迹平滑度
    subplot(2, 3, 3);
    smoothness_scores = [];
    for i = 1:length(trajectories)
        if size(trajectories{i}.Q, 1) > 2
            smoothness = mean(sqrt(sum(diff(trajectories{i}.Q, 2).^2, 2)));
            smoothness_scores = [smoothness_scores; smoothness];
        end
    end
    plot(smoothness_scores, 'o-', 'LineWidth', 2);
    title('轨迹平滑度');
    xlabel('轨迹编号');
    ylabel('平滑度指标');
    grid on;
    
    % 子图4：关节角度范围
    subplot(2, 3, 4);
    all_Q = [];
    for i = 1:length(trajectories)
        all_Q = [all_Q; trajectories{i}.Q];
    end
    if ~isempty(all_Q)
        boxplot(all_Q(:, 1:min(7, size(all_Q, 2))));
        title('关节角度分布');
        xlabel('关节编号');
        ylabel('角度 (rad)');
    end
    
    % 子图5：轨迹长度分布
    subplot(2, 3, 5);
    histogram(trajectory_lengths, 'BinWidth', 5);
    title('轨迹长度分布');
    xlabel('轨迹点数');
    ylabel('频次');
    grid on;
    
    % 子图6：总体统计
    subplot(2, 3, 6);
    stats_text = {
        sprintf('总轨迹数: %d', length(trajectories));
        sprintf('平均长度: %.1f', mean(trajectory_lengths));
        sprintf('最大长度: %d', max(trajectory_lengths));
        sprintf('最小长度: %d', min(trajectory_lengths));
        sprintf('左手任务: %d', arm_counts(1));
        sprintf('右手任务: %d', arm_counts(2));
    };
    
    text(0.1, 0.5, stats_text, 'FontSize', 12, 'VerticalAlignment', 'middle');
    axis off;
    title('统计信息');
    
    fprintf('  ✓ 轨迹分析图表生成完成\n');
    
catch ME
    fprintf('  ❌ 图表生成失败: %s\n', ME.message);
end
end

function runCompleteDemo(yumi, qHome, trajectories, brick_config)
% 运行完整演示
fprintf('  🎯 执行完整演示流程...\n');

% 1. 轨迹分析
plotTrajectoryAnalysis(trajectories);
pause(2);

% 2. MATLAB动画
runMatlabAnimation(yumi, qHome, trajectories, brick_config);
pause(1);

% 3. Simulink仿真（可选）
fprintf('  是否继续Simulink仿真? (y/n, 默认n): ');
continue_sim = input('', 's');
if strcmpi(continue_sim, 'y')
    runSimulinkDemo(trajectories);
end

fprintf('  ✓ 完整演示流程完成\n');
end

function analyzeTrajectoryQuality(trajectories)
% 分析轨迹质量
total_points = sum(cellfun(@(t) size(t.Q, 1), trajectories));
avg_smoothness = 0;
max_velocity = 0;

for i = 1:length(trajectories)
    traj = trajectories{i};
    
    % 计算平滑度
    if size(traj.Q, 1) > 2
        smoothness = mean(sqrt(sum(diff(traj.Q, 2).^2, 2)));
        avg_smoothness = avg_smoothness + smoothness;
    end
    
    % 计算最大速度
    if isfield(traj, 'velocity')
        max_vel = max(sqrt(sum(traj.velocity.^2, 2)));
        max_velocity = max(max_velocity, max_vel);
    end
end

avg_smoothness = avg_smoothness / length(trajectories);

fprintf('✓ 轨迹质量分析:\n');
fprintf('  - 总轨迹点数: %d\n', total_points);
fprintf('  - 平均平滑度: %.4f\n', avg_smoothness);
if max_velocity > 0
    fprintf('  - 最大速度: %.3f rad/s\n', max_velocity);
end
fprintf('  - 质量评级: %s\n', getQualityRating(avg_smoothness));
end

function rating = getQualityRating(smoothness)
% 根据平滑度给出质量评级
if smoothness < 0.01
    rating = '优秀';
elseif smoothness < 0.05
    rating = '良好';
elseif smoothness < 0.1
    rating = '一般';
else
    rating = '需要改进';
end
end
