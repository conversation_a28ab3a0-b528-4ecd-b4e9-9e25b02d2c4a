function testAnimationPlayer()
% 测试动画播放器 - 简化版本

fprintf('🎬 === 测试动画播放器 === 🎬\n');

try
    %% 加载YuMi机器人
    fprintf('加载YuMi机器人...\n');
    yumi = loadrobot('abbYumi');
    fprintf('✅ YuMi机器人加载成功\n');
    
    %% 创建简单的测试轨迹
    fprintf('创建测试轨迹...\n');
    trajectories = createTestTrajectories();
    fprintf('✅ 创建了 %d 条测试轨迹\n', length(trajectories));
    
    %% 创建简单的积木配置
    fprintf('创建积木配置...\n');
    brick_config = createTestBrickConfig();
    fprintf('✅ 积木配置创建完成\n');
    
    %% 启动动画播放器
    fprintf('启动动画播放器...\n');
    player = AnimationPlayer(yumi, trajectories, brick_config);
    fprintf('✅ 动画播放器启动成功\n');
    
    fprintf('\n🎮 播放器已启动！点击播放按钮开始动画\n');
    
catch ME
    fprintf('❌ 测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end

function trajectories = createTestTrajectories()
% 创建测试轨迹

trajectories = {};

% 创建5条简单轨迹
for i = 1:5
    traj = struct();
    
    % 生成20个关节角度点
    num_points = 20;
    traj.Q = zeros(num_points, 14);
    
    % 简单的正弦运动
    for j = 1:num_points
        t = (j-1) / (num_points-1);
        traj.Q(j, 1) = 0.5 * sin(2*pi*t + i*pi/3);  % 左臂第一关节
        traj.Q(j, 8) = -0.5 * sin(2*pi*t + i*pi/3); % 右臂第一关节
    end
    
    traj.task = sprintf('test_motion_%d', i);
    trajectories{i} = traj;
end

end

function config = createTestBrickConfig()
% 创建测试积木配置

config = struct();
config.total_bricks = 47;
config.assembly_time = 60;

end
