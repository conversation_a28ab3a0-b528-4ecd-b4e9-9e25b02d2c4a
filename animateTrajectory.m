function animateTrajectory(yumi, qHome, trajectories, brick_config)
    % 改进的YuMi机器人轨迹动画，包含乐高积木可视化
    % 输入：
    %   yumi - YuMi机器人模型
    %   qHome - 初始关节角
    %   trajectories - 轨迹序列
    %   brick_config - 乐高配置（可选）

    if nargin < 4
        % 如果没有提供brick_config，加载默认配置
        brick_config = lego_config();
    end

    % 關閉之前的圖形
    close all;

    fprintf('=== 初始化YuMi机器人乐高堆叠动画系统 ===\n');

    % 创建乐高可视化系统
    visualization_options = struct();
    visualization_options.animation_speed = 0.05; % 较快的动画速度

    lego_viz = LegoVisualization(yumi, brick_config, visualization_options);

    % 显示初始机器人配置
    show(yumi, qHome, 'Parent', lego_viz.axes_handle, 'PreservePlot', true, 'Frames', 'off');

    fprintf('乐高可视化系统已启动\n');

    % 检查是否有改进的轨迹（包含任务信息）
    has_task_info = false;
    if ~isempty(trajectories) && isfield(trajectories{1}, 'task')
        has_task_info = true;
        fprintf('检测到改进轨迹，将显示完整的抓取和放置动画\n');
    else
        fprintf('使用基础轨迹，将显示机器人运动轨迹\n');
    end

    % 选择动画模式
    if has_task_info
        % 使用乐高可视化系统的完整动画
        lego_viz.animateTrajectoryWithLego(yumi, trajectories, qHome);
        return;
    end

    % === 基础動畫參數設置 ===
    colors = struct('left', [1 0 0], 'right', [0 0 1]); % 紅色=左手，藍色=右手
    pauseTime = 0.08; % 動畫速度

    fprintf('\n=== 開始機器人軌跡動畫 ===\n');
    total_trajectories = length(trajectories);
    total_points = sum(cellfun(@(t) size(t.Q,1), trajectories));
    fprintf('總軌跡數: %d\n', total_trajectories);
    fprintf('總點數: %d\n', total_points);
    fprintf('預估時間: %.1f 秒\n\n', total_points * pauseTime);
    
    % 為每條軌跡創建線條對象
    trajectory_lines = {};
    trajectory_data = {};

    current_point = 0;
    ax = lego_viz.axes_handle; % 使用乐高可视化系统的坐标轴

    for i = 1:length(trajectories)
        traj = trajectories{i};
        armColor = colors.(traj.arm);
        traj_points = size(traj.Q, 1);

        fprintf('軌跡 %d: %s 手臂 (%d 點)\n', i, traj.arm, traj_points);

        % 為當前軌跡創建空的線條對象
        trajectory_lines{i} = plot3(ax, [], [], [], ...
            'Color', armColor, 'LineWidth', 2, ...
            'Marker', 'none', ...
            'DisplayName', sprintf('%s 手臂軌跡 %d', traj.arm, i));

        % 初始化軌跡數據存儲
        trajectory_data{i} = struct('x', [], 'y', [], 'z', []);
        
        for j = 1:traj_points
            current_point = current_point + 1;
            
            % 更新機器人配置
            show(yumi, traj.Q(j, :), 'PreservePlot', true, 'Frames', 'off', 'Parent', ax);
            
            % 計算當前末端執行器位置
            T = getTransform(yumi, traj.Q(j, :), traj.eeName);
            current_pos = T(1:3, 4)';
            
            % 將當前位置添加到軌跡數據
            trajectory_data{i}.x(end+1) = current_pos(1);
            trajectory_data{i}.y(end+1) = current_pos(2);
            trajectory_data{i}.z(end+1) = current_pos(3);
            
            % 更新軌跡線條
            set(trajectory_lines{i}, ...
                'XData', trajectory_data{i}.x, ...
                'YData', trajectory_data{i}.y, ...
                'ZData', trajectory_data{i}.z);
            
            % 在當前位置添加一個亮點標記（末端執行器當前位置）
            if j == 1
                % 第一次創建當前位置標記
                current_marker = plot3(ax, current_pos(1), current_pos(2), current_pos(3), ...
                    'o', 'MarkerSize', 8, 'MarkerFaceColor', armColor, ...
                    'MarkerEdgeColor', 'white', 'LineWidth', 2);
            else
                % 更新當前位置標記
                set(current_marker, 'XData', current_pos(1), ...
                                   'YData', current_pos(2), ...
                                   'ZData', current_pos(3));
            end
            
            % 計算進度
            total_progress = current_point / total_points * 100;
            traj_progress = j / traj_points * 100;
            
            % 更新標題
            title(sprintf('軌跡 %d/%d (%s 手臂) | 軌跡進度: %.1f%% | 總進度: %.1f%%', ...
                i, total_trajectories, traj.arm, traj_progress, total_progress), ...
                'FontSize', 12, 'FontWeight', 'bold');
            
            % 在命令窗口顯示進度
            if mod(total_progress, 10) < 0.1 && mod(current_point, 10) == 0
                fprintf(' 總進度: %.0f%%\n', total_progress);
            end
            
            drawnow;
            pause(pauseTime);
        end
        
        % 軌跡完成後，移除當前位置標記，添加終點標記
        delete(current_marker);
        plot3(ax, trajectory_data{i}.x(end), trajectory_data{i}.y(end), trajectory_data{i}.z(end), ...
            's', 'MarkerSize', 10, 'MarkerFaceColor', 'red', ...
            'MarkerEdgeColor', 'black', 'LineWidth', 2, ...
            'DisplayName', sprintf('終點 %d', i));
        
        fprintf(' ✓ 軌跡 %d 完成\n', i);
        pause(0.5); % 軌跡間暫停
    end
    
    % 添加圖例
    legend(ax, 'Location', 'bestoutside');
    
    % 最終狀態
    title('🎉 YuMi 機器人 + 軌跡動畫完成！', 'FontSize', 14, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
    
    % 改變視角以展示最終結果
    view(45, 30);
    
    fprintf('\n🎉 動畫完成！\n');
    fprintf('📊 統計信息:\n');
    fprintf(' - 總軌跡: %d\n', total_trajectories);
    fprintf(' - 總點數: %d\n', total_points);
    fprintf(' - 實際用時: %.1f 秒\n', total_points * pauseTime);
    fprintf(' - 紅色線條: 左手軌跡\n');
    fprintf(' - 藍色線條: 右手軌跡\n');
    fprintf(' - 紅色方塊: 軌跡終點\n');
    
    % 保持窗口開啟
    fprintf('\n💡 提示: 窗口將保持開啟以查看最終結果\n');
end