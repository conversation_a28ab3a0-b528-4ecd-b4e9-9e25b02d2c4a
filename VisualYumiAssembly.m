classdef VisualYumiAssembly < handle
    % 可视化YuMi拼接系统 - 真正能看到机械臂运动的版本
    
    properties
        % 机器人相关
        yumi_robot          % YuMi机器人模型
        current_config      % 当前关节配置
        home_config         % 初始配置
        
        % 建筑数据
        ldr_parser          % LDR解析器
        target_bricks       % 目标积木列表
        assembly_sequence   % 拼接序列
        
        % 界面组件
        main_figure         % 主窗口
        axes_3d            % 3D坐标轴
        status_text        % 状态文本
        progress_bar       % 进度条
        
        % 动画控制
        animation_timer     % 动画定时器
        is_playing         % 播放状态
        current_step       % 当前步骤
        total_steps        % 总步骤数
        animation_phase    % 动画阶段
        motion_step        % 运动步骤
        
        % 积木对象
        brick_objects      % 积木显示对象
        placed_bricks      % 已放置的积木
        
        % 运动配置
        start_config       % 起始配置
        target_config      % 目标配置
        motion_progress    % 运动进度
    end
    
    methods
        function obj = VisualYumiAssembly()
            % 构造函数
            fprintf('🎬 === 创建可视化YuMi拼接系统 === 🎬\n');
            
            % 初始化组件
            obj.initializeRobot();
            obj.loadBuildingData();
            obj.createInterface();
            obj.setupAnimation();
            
            fprintf('✅ 可视化YuMi拼接系统创建完成\n');
        end
        
        function initializeRobot(obj)
            % 初始化机器人
            fprintf('初始化YuMi机器人...\n');
            
            try
                % 加载YuMi机器人
                obj.yumi_robot = loadrobot('abbYumi', 'DataFormat', 'row');
                obj.home_config = obj.yumi_robot.homeConfiguration;
                obj.current_config = obj.home_config;
                
                fprintf('✅ YuMi机器人初始化成功\n');
                
            catch ME
                fprintf('❌ YuMi机器人初始化失败: %s\n', ME.message);
                error('无法初始化YuMi机器人');
            end
        end
        
        function loadBuildingData(obj)
            % 加载建筑数据
            fprintf('加载建筑数据...\n');
            
            try
                obj.ldr_parser = LDRParser('mainbu.ldr');
                success = obj.ldr_parser.parseLDRFile();
                
                if success
                    obj.target_bricks = obj.ldr_parser.bricks;
                    obj.generateAssemblySequence();
                    fprintf('✅ 建筑数据加载完成: %d个积木\n', length(obj.target_bricks));
                else
                    error('LDR文件解析失败');
                end
                
            catch ME
                fprintf('❌ 建筑数据加载失败: %s\n', ME.message);
                error('无法加载建筑数据');
            end
        end
        
        function generateAssemblySequence(obj)
            % 生成拼接序列
            bricks = obj.target_bricks;
            
            % 按Z坐标排序（从下到上）
            z_coords = zeros(length(bricks), 1);
            for i = 1:length(bricks)
                if isstruct(bricks(i).position)
                    pos = [bricks(i).position.x, bricks(i).position.y, bricks(i).position.z];
                else
                    pos = bricks(i).position;
                end
                z_coords(i) = pos(3);
            end
            [~, sort_idx] = sort(z_coords);
            
            % 生成序列
            obj.assembly_sequence = struct('step_id', {}, 'brick', {}, 'arm', {}, 'pick_pos', {}, 'place_pos', {});
            
            for i = 1:length(sort_idx)
                brick_idx = sort_idx(i);
                brick = bricks(brick_idx);
                
                if isstruct(brick.position)
                    brick_pos = [brick.position.x, brick.position.y, brick.position.z];
                else
                    brick_pos = brick.position;
                end
                
                % 分配机械臂
                arm = 'right';
                if brick_pos(2) >= -100
                    arm = 'left';
                end
                
                step = struct();
                step.step_id = i;
                step.brick = brick;
                step.arm = arm;
                step.pick_pos = [0.2, 0, 0.3];
                step.place_pos = brick_pos / 1000;
                
                obj.assembly_sequence(end+1) = step;
            end
            
            obj.total_steps = length(obj.assembly_sequence);
            obj.current_step = 1;
            obj.animation_phase = 'idle';
            obj.motion_step = 1;
            obj.motion_progress = 0;
            
            fprintf('✅ 拼接序列生成完成: %d步\n', obj.total_steps);
        end
        
        function createInterface(obj)
            % 创建用户界面
            fprintf('创建用户界面...\n');
            
            % 创建主窗口
            obj.main_figure = figure('Name', '可视化YuMi乐高拼接系统', ...
                                     'Position', [100, 100, 1200, 800], ...
                                     'Color', [0.1, 0.1, 0.1], ...
                                     'CloseRequestFcn', @(~,~) obj.closeSystem());
            
            % 创建3D显示区域
            obj.axes_3d = axes('Parent', obj.main_figure, ...
                               'Position', [0.05, 0.3, 0.9, 0.65], ...
                               'Color', [0.1, 0.1, 0.1]);
            
            % 创建控制面板
            obj.createControlPanel();
            
            % 初始化3D场景
            obj.initialize3DScene();
            
            fprintf('✅ 用户界面创建完成\n');
        end
        
        function createControlPanel(obj)
            % 创建控制面板
            panel = uipanel('Parent', obj.main_figure, ...
                            'Position', [0.05, 0.02, 0.9, 0.25], ...
                            'BackgroundColor', [0.2, 0.2, 0.2], ...
                            'Title', '控制面板', ...
                            'ForegroundColor', 'white', ...
                            'FontSize', 12);
            
            % 播放控制
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '▶️ 开始拼接', ...
                      'Position', [20, 150, 120, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.startAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏸️ 暂停', ...
                      'Position', [150, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.pauseAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏹️ 停止', ...
                      'Position', [240, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.stopAssembly());
            
            % 运动测试按钮
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '🤖 测试左臂运动', ...
                      'Position', [350, 150, 120, 40], ...
                      'FontSize', 10, ...
                      'Callback', @(~,~) obj.testLeftArmMotion());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '🤖 测试右臂运动', ...
                      'Position', [480, 150, 120, 40], ...
                      'FontSize', 10, ...
                      'Callback', @(~,~) obj.testRightArmMotion());
            
            % 进度控制
            uicontrol('Parent', panel, ...
                      'Style', 'text', ...
                      'String', '拼接进度:', ...
                      'Position', [20, 110, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white');
            
            obj.progress_bar = uicontrol('Parent', panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 110, 400, 20], ...
                                         'Min', 1, ...
                                         'Max', obj.total_steps, ...
                                         'Value', 1, ...
                                         'Callback', @(src,~) obj.seekToStep(round(src.Value)));
            
            % 状态显示
            obj.status_text = uicontrol('Parent', panel, ...
                                        'Style', 'text', ...
                                        'String', '准备开始拼接...', ...
                                        'Position', [20, 70, 500, 30], ...
                                        'BackgroundColor', [0.2, 0.2, 0.2], ...
                                        'ForegroundColor', 'white', ...
                                        'FontSize', 11, ...
                                        'HorizontalAlignment', 'left');
        end
        
        function initialize3DScene(obj)
            % 初始化3D场景
            fprintf('初始化3D场景...\n');
            
            axes(obj.axes_3d);
            cla;
            hold on;
            
            % 设置坐标轴
            xlabel('X (m)', 'Color', 'white', 'FontSize', 12);
            ylabel('Y (m)', 'Color', 'white', 'FontSize', 12);
            zlabel('Z (m)', 'Color', 'white', 'FontSize', 12);
            title('🎬 可视化YuMi双臂机器人乐高拼接系统', 'Color', 'white', 'FontSize', 14);
            
            % 设置视角和范围
            view(45, 30);
            axis equal;
            grid on;
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            
            % 添加地面
            [X, Y] = meshgrid(-0.5:0.1:0.8, -0.6:0.1:0.6);
            Z = -0.2 * ones(size(X));
            surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
            
            % 显示YuMi机器人
            obj.displayYumiRobot();
            
            % 显示目标积木轮廓
            obj.displayTargetBricks();
            
            fprintf('✅ 3D场景初始化完成\n');
        end
        
        function displayYumiRobot(obj)
            % 显示YuMi机器人
            try
                % 显示机器人
                show(obj.yumi_robot, obj.current_config, ...
                     'Parent', obj.axes_3d, ...
                     'PreservePlot', true, ...
                     'Frames', 'off', ...
                     'Visuals', 'on');
                
                fprintf('✅ YuMi机器人显示成功\n');
                
            catch ME
                fprintf('⚠️ YuMi机器人显示失败: %s\n', ME.message);
            end
        end

        function displayTargetBricks(obj)
            % 显示目标积木轮廓
            obj.brick_objects = cell(length(obj.target_bricks), 1);
            obj.placed_bricks = false(length(obj.target_bricks), 1);

            for i = 1:length(obj.target_bricks)
                brick = obj.target_bricks(i);

                if isstruct(brick.position)
                    pos = [brick.position.x, brick.position.y, brick.position.z] / 1000;
                else
                    pos = brick.position / 1000;
                end

                % 创建简单的积木表示
                obj.brick_objects{i} = plot3(pos(1), pos(2), pos(3), 'o', ...
                                            'MarkerSize', 8, ...
                                            'MarkerFaceColor', [0.8, 0.8, 0.8], ...
                                            'MarkerEdgeColor', [0.5, 0.5, 0.5], ...
                                            'Parent', obj.axes_3d);
            end

            fprintf('✅ 目标积木轮廓显示完成: %d个\n', length(obj.target_bricks));
        end

        function setupAnimation(obj)
            % 设置动画系统
            obj.is_playing = false;
            obj.animation_timer = [];
            obj.motion_progress = 0;
            obj.start_config = obj.current_config;
            obj.target_config = obj.current_config;

            fprintf('✅ 动画系统设置完成\n');
        end

        function startAssembly(obj)
            % 开始拼接
            fprintf('🎬 开始可视化拼接动画...\n');
            obj.is_playing = true;
            obj.animation_phase = 'moving_to_pick';
            obj.motion_step = 1;
            obj.motion_progress = 0;

            % 更新状态
            obj.updateStatus(sprintf('开始拼接 - 步骤 1/%d', obj.total_steps));

            % 创建动画定时器
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end

            obj.animation_timer = timer('ExecutionMode', 'fixedRate', ...
                                        'Period', 0.1, ...
                                        'TimerFcn', @(~,~) obj.updateAnimation());
            start(obj.animation_timer);

            fprintf('✅ 可视化动画定时器启动成功\n');
        end

        function updateAnimation(obj)
            % 更新动画
            if ~obj.is_playing || obj.current_step > obj.total_steps
                obj.pauseAssembly();
                return;
            end

            try
                % 执行当前步骤
                obj.executeCurrentStep();

                % 更新机器人显示
                obj.updateRobotDisplay();

                % 更新显示
                drawnow;

            catch ME
                fprintf('❌ 动画更新错误: %s\n', ME.message);
                obj.pauseAssembly();
            end
        end

        function executeCurrentStep(obj)
            % 执行当前拼接步骤
            step = obj.assembly_sequence(obj.current_step);

            switch obj.animation_phase
                case 'moving_to_pick'
                    if obj.motion_step == 1
                        % 开始移动到拾取位置
                        obj.planMotionToPosition(step.arm, step.pick_pos);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂移动到拾取位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateMotion()
                        obj.animation_phase = 'picking';
                        obj.motion_step = 1;
                    end

                case 'picking'
                    fprintf('夹爪控制: %s臂 close\n', step.arm);
                    obj.updateStatus(sprintf('步骤 %d/%d: %s臂夹取积木', ...
                                           obj.current_step, obj.total_steps, step.arm));
                    obj.animation_phase = 'moving_to_place';
                    obj.motion_step = 1;

                case 'moving_to_place'
                    if obj.motion_step == 1
                        % 开始移动到放置位置
                        obj.planMotionToPosition(step.arm, step.place_pos);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂移动到放置位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateMotion()
                        obj.animation_phase = 'placing';
                        obj.motion_step = 1;
                    end

                case 'placing'
                    fprintf('夹爪控制: %s臂 open\n', step.arm);
                    obj.placeBrick(obj.current_step);
                    obj.updateStatus(sprintf('步骤 %d/%d: %s臂放置积木', ...
                                           obj.current_step, obj.total_steps, step.arm));
                    obj.animation_phase = 'returning';
                    obj.motion_step = 1;

                case 'returning'
                    if obj.motion_step == 1
                        % 开始返回初始位置
                        obj.planMotionToHome(step.arm);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂返回初始位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateMotion()
                        obj.nextStep();
                    end
            end
        end

        function planMotionToPosition(obj, arm, target_pos)
            % 规划运动到指定位置
            obj.start_config = obj.current_config;
            obj.target_config = obj.current_config;
            obj.motion_progress = 0;

            % 简单的关节角度调整来模拟运动
            if strcmp(arm, 'left')
                % 左臂关节调整
                obj.target_config(1) = atan2(target_pos(2), target_pos(1)) + pi/6;
                obj.target_config(2) = -pi/4 + target_pos(3) * pi/2;
                obj.target_config(3) = pi/6 + target_pos(1) * pi/4;
                obj.target_config(4) = -pi/3;
                obj.target_config(5) = pi/6;
                obj.target_config(6) = 0;
                obj.target_config(7) = 0;
            else
                % 右臂关节调整
                obj.target_config(8) = atan2(target_pos(2), target_pos(1)) - pi/6;
                obj.target_config(9) = -pi/4 + target_pos(3) * pi/2;
                obj.target_config(10) = -pi/6 + target_pos(1) * pi/4;
                obj.target_config(11) = -pi/3;
                obj.target_config(12) = -pi/6;
                obj.target_config(13) = 0;
                obj.target_config(14) = 0;
            end
        end

        function planMotionToHome(obj, arm)
            % 规划返回初始位置
            obj.start_config = obj.current_config;
            obj.target_config = obj.home_config;
            obj.motion_progress = 0;
        end

        function completed = updateMotion(obj)
            % 更新运动
            obj.motion_progress = obj.motion_progress + 0.05; % 每次增加5%

            if obj.motion_progress >= 1.0
                % 运动完成
                obj.current_config = obj.target_config;
                completed = true;
                obj.motion_progress = 0;
                obj.motion_step = 1;
            else
                % 插值计算当前关节配置
                obj.current_config = obj.start_config + obj.motion_progress * ...
                                   (obj.target_config - obj.start_config);
                obj.motion_step = obj.motion_step + 1;
                completed = false;
            end
        end

        function updateRobotDisplay(obj)
            % 更新机器人显示
            try
                % 清除旧的机器人显示
                robot_patches = findobj(obj.axes_3d, 'Type', 'patch');
                delete(robot_patches);

                % 重新显示机器人
                show(obj.yumi_robot, obj.current_config, ...
                     'Parent', obj.axes_3d, ...
                     'PreservePlot', true, ...
                     'Frames', 'off', ...
                     'Visuals', 'on');

            catch ME
                % 忽略显示错误
            end
        end

        function testLeftArmMotion(obj)
            % 测试左臂运动
            fprintf('🤖 测试左臂运动...\n');
            obj.planMotionToPosition('left', [0.3, 0.3, 0.4]);

            for i = 1:20
                obj.updateMotion();
                obj.updateRobotDisplay();
                pause(0.1);
            end

            fprintf('✅ 左臂运动测试完成\n');
        end

        function testRightArmMotion(obj)
            % 测试右臂运动
            fprintf('🤖 测试右臂运动...\n');
            obj.planMotionToPosition('right', [0.3, -0.3, 0.4]);

            for i = 1:20
                obj.updateMotion();
                obj.updateRobotDisplay();
                pause(0.1);
            end

            fprintf('✅ 右臂运动测试完成\n');
        end

        function placeBrick(obj, step_id)
            % 放置积木
            if step_id <= length(obj.brick_objects) && ~isempty(obj.brick_objects{step_id})
                try
                    % 更新积木显示为已放置状态
                    set(obj.brick_objects{step_id}, 'MarkerFaceColor', [1, 0, 0], 'MarkerSize', 10);
                    obj.placed_bricks(step_id) = true;
                    fprintf('✅ 积木 %d 已放置\n', step_id);
                catch ME
                    fprintf('⚠️ 积木显示更新失败: %s\n', ME.message);
                end
            end
        end

        function nextStep(obj)
            % 进入下一步
            obj.current_step = obj.current_step + 1;
            obj.animation_phase = 'moving_to_pick';
            obj.motion_step = 1;
            obj.motion_progress = 0;

            % 更新进度条
            if obj.current_step <= obj.total_steps
                obj.progress_bar.Value = obj.current_step;
                obj.updateStatus(sprintf('拼接中 - 步骤 %d/%d', obj.current_step, obj.total_steps));
                fprintf('✅ 完成步骤 %d，开始步骤 %d\n', obj.current_step-1, obj.current_step);
            else
                obj.updateStatus('🎉 拼接完成！');
                obj.pauseAssembly();
                fprintf('🎉 === 拼接完成！=== 🎉\n');
            end
        end

        function updateStatus(obj, message)
            % 更新状态文本
            if isvalid(obj.status_text)
                obj.status_text.String = message;
            end
        end

        function pauseAssembly(obj)
            % 暂停拼接
            obj.is_playing = false;
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
            end
            fprintf('⏸️ 拼接已暂停\n');
        end

        function stopAssembly(obj)
            % 停止拼接
            obj.pauseAssembly();
            obj.current_step = 1;
            obj.animation_phase = 'idle';
            obj.motion_step = 1;
            obj.motion_progress = 0;
            obj.current_config = obj.home_config;
            obj.progress_bar.Value = 1;
            obj.updateStatus('准备开始拼接...');
            obj.updateRobotDisplay();
            fprintf('⏹️ 拼接已停止\n');
        end

        function seekToStep(obj, step)
            % 跳转到指定步骤
            obj.current_step = max(1, min(step, obj.total_steps));
            obj.updateStatus(sprintf('跳转到步骤 %d/%d', obj.current_step, obj.total_steps));
        end

        function closeSystem(obj)
            % 关闭系统
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end
            delete(obj.main_figure);
        end
    end
end
