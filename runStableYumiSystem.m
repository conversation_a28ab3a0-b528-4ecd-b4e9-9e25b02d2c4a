function runStableYumiSystem()
% 启动稳定版YuMi乐高拼接系统
% 解决图形对象句柄问题，确保动画稳定运行

fprintf('🛡️ === 启动稳定版YuMi乐高拼接系统 === 🛡️\n');
fprintf('专门解决图形对象句柄问题，确保动画稳定运行\n\n');

try
    %% 环境检查
    fprintf('🔍 环境检查...\n');
    
    % 检查必要文件
    required_files = {'mainbu.ldr', 'LDRParser.m', 'StableYumiAssembly.m'};
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            error('缺少必要文件: %s', required_files{i});
        end
        fprintf('  ✅ %s 存在\n', required_files{i});
    end
    
    % 检查工具箱
    if ~exist('loadrobot', 'file')
        error('需要Robotics System Toolbox');
    end
    fprintf('  ✅ Robotics System Toolbox 可用\n');
    
    %% 创建稳定系统
    fprintf('\n🚀 创建稳定版YuMi拼接系统...\n');
    
    assembly_system = StableYumiAssembly();
    
    %% 显示系统信息
    fprintf('\n📊 === 稳定版系统信息 === 📊\n');
    
    fprintf('🤖 YuMi机器人:\n');
    fprintf('   - 双臂结构: 7+7 自由度\n');
    fprintf('   - 稳定显示更新机制\n');
    fprintf('   - 避免频繁图形对象操作\n');
    
    fprintf('\n🏗️ 建筑信息:\n');
    fprintf('   - 设计文件: mainbu.ldr\n');
    fprintf('   - 积木总数: %d 个\n', length(assembly_system.target_bricks));
    fprintf('   - 拼接步骤: %d 步\n', assembly_system.total_steps);
    
    fprintf('\n🛡️ 稳定性改进:\n');
    fprintf('   ✅ 降低显示更新频率\n');
    fprintf('   ✅ 避免频繁删除重建图形对象\n');
    fprintf('   ✅ 增强错误处理机制\n');
    fprintf('   ✅ 简化动画逻辑\n');
    fprintf('   ✅ 稳定的定时器控制\n');
    
    fprintf('\n🎯 核心功能:\n');
    fprintf('   ✅ YuMi机器人完整显示\n');
    fprintf('   ✅ 左右臂分工协作\n');
    fprintf('   ✅ 积木逐步拼接动画\n');
    fprintf('   ✅ 实时状态反馈\n');
    fprintf('   ✅ 稳定的动画播放\n');
    
    fprintf('\n🎮 操作说明:\n');
    fprintf('   ▶️  点击"开始拼接" - 启动稳定拼接动画\n');
    fprintf('   ⏸️  点击"暂停" - 暂停当前动画\n');
    fprintf('   ⏹️  点击"停止" - 停止并重置\n');
    fprintf('   📊 拖动进度条 - 跳转到任意步骤\n');
    
    fprintf('\n🔧 动画流程:\n');
    fprintf('   每个拼接步骤包含5个阶段:\n');
    fprintf('   1. 移动到拾取位置\n');
    fprintf('   2. 夹爪关闭抓取\n');
    fprintf('   3. 移动到放置位置\n');
    fprintf('   4. 夹爪开启放置积木\n');
    fprintf('   5. 返回初始位置\n');
    
    fprintf('\n🛡️ 稳定性特点:\n');
    fprintf('   - 更长的定时器周期 (0.2秒)\n');
    fprintf('   - 减少图形更新频率\n');
    fprintf('   - 简化的动画逻辑\n');
    fprintf('   - 增强的错误恢复\n');
    fprintf('   - 避免复杂的逆运动学计算\n');
    
    fprintf('\n🎉 === 稳定版系统启动完成 === 🎉\n');
    fprintf('💡 现在你可以看到:\n');
    fprintf('   - 稳定的YuMi双臂机器人显示\n');
    fprintf('   - 不会出现图形对象句柄错误\n');
    fprintf('   - 流畅的拼接动画过程\n');
    fprintf('   - 可靠的左右臂分工协作\n');
    fprintf('   - 积木的逐步放置过程\n');
    
    fprintf('\n🚀 点击"开始拼接"按钮开始观看稳定的动画！\n');
    
    fprintf('\n📋 === 预期效果 === 📋\n');
    fprintf('✅ 不再出现"参数必须为现有图形对象的标量句柄"错误\n');
    fprintf('✅ 不再出现"对象无效或已删除"错误\n');
    fprintf('✅ 动画可以稳定运行到完成\n');
    fprintf('✅ 左右臂轮流工作动画\n');
    fprintf('✅ 积木逐个变红显示\n');
    fprintf('✅ 完整的50步拼接过程\n');
    
catch ME
    fprintf('❌ 稳定版系统启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n🔧 故障排除:\n');
    fprintf('1. 确保安装了Robotics System Toolbox\n');
    fprintf('2. 确保mainbu.ldr和LDRParser.m文件存在\n');
    fprintf('3. 检查MATLAB版本兼容性\n');
    fprintf('4. 尝试重启MATLAB\n');
    
    fprintf('\n💡 如果仍有问题，可以尝试:\n');
    fprintf('   - 关闭其他MATLAB图形窗口\n');
    fprintf('   - 清理工作空间: clear all; close all\n');
    fprintf('   - 重新运行: runStableYumiSystem\n');
end

end
