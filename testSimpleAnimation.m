function testSimpleAnimation()
% 测试简化的动画播放器

fprintf('🎬 === 测试简化动画播放器 === 🎬\n');

try
    %% 创建简单的动画播放器
    fprintf('创建简单动画播放器...\n');
    
    % 创建主窗口
    main_fig = figure('Name', '🏰 YuMi城堡拼接动画测试', ...
                      'Position', [100, 100, 1200, 800], ...
                      'Color', [0.1, 0.1, 0.1]);
    
    % 创建3D显示区域
    axes_3d = axes('Parent', main_fig, ...
                   'Position', [0.05, 0.25, 0.9, 0.7], ...
                   'Color', [0.05, 0.05, 0.05]);
    
    % 创建控制面板
    control_panel = uipanel('Parent', main_fig, ...
                            'Position', [0.05, 0.02, 0.9, 0.2], ...
                            'BackgroundColor', [0.2, 0.2, 0.2]);
    
    % 播放按钮
    play_btn = uicontrol('Parent', control_panel, ...
                         'Style', 'pushbutton', ...
                         'String', '▶️ 播放', ...
                         'Position', [20, 100, 80, 40], ...
                         'FontSize', 12);

    % 停止按钮
    stop_btn = uicontrol('Parent', control_panel, ...
                         'Style', 'pushbutton', ...
                         'String', '⏹️ 停止', ...
                         'Position', [110, 100, 80, 40], ...
                         'FontSize', 12);
    
    % 进度条
    progress_bar = uicontrol('Parent', control_panel, ...
                             'Style', 'slider', ...
                             'Position', [20, 60, 400, 20], ...
                             'Min', 1, ...
                             'Max', 100, ...
                             'Value', 1);
    
    % 时间显示
    time_text = uicontrol('Parent', control_panel, ...
                          'Style', 'text', ...
                          'String', '帧: 1/100', ...
                          'Position', [430, 60, 100, 20], ...
                          'BackgroundColor', [0.2, 0.2, 0.2], ...
                          'ForegroundColor', 'white', ...
                          'FontSize', 10);
    
    %% 初始化3D场景
    fprintf('初始化3D场景...\n');
    axes(axes_3d);
    hold on;
    
    % 设置坐标轴
    xlabel('X (mm)', 'Color', 'white');
    ylabel('Y (mm)', 'Color', 'white');
    zlabel('Z (mm)', 'Color', 'white');
    title('🏰 YuMi机器人城堡拼接动画', 'Color', 'white', 'FontSize', 14);
    
    % 设置视角
    view(45, 30);
    axis equal;
    grid on;
    
    % 设置坐标轴范围
    xlim([0, 400]);
    ylim([0, 300]);
    zlim([0, 200]);
    
    % 添加地面
    [X, Y] = meshgrid(0:50:400, 0:50:300);
    Z = zeros(size(X));
    surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
    
    % 添加机器人指示器
    robot_marker = plot3(200, 150, 50, 'ro', 'MarkerSize', 20, 'MarkerFaceColor', 'red');
    text(200, 150, 80, 'YuMi Robot', 'Color', 'white', 'FontSize', 12, 'HorizontalAlignment', 'center');
    
    %% 创建测试积木
    fprintf('创建测试积木...\n');
    brick_objects = {};
    
    % 积木位置和颜色
    brick_positions = [
        100, 100, 0;   % 第1个积木
        150, 100, 0;   % 第2个积木
        200, 100, 0;   % 第3个积木
        250, 100, 0;   % 第4个积木
        100, 150, 10;  % 第5个积木
        150, 150, 10;  % 第6个积木
        200, 150, 10;  % 第7个积木
        125, 125, 20;  % 第8个积木
        175, 125, 20;  % 第9个积木
        150, 125, 30   % 第10个积木
    ];
    
    brick_colors = {
        [1, 0, 0],     % 红色
        [0, 1, 0],     % 绿色
        [0, 0, 1],     % 蓝色
        [1, 1, 0],     % 黄色
        [1, 0, 1],     % 紫色
        [0, 1, 1],     % 青色
        [1, 0.5, 0],   % 橙色
        [0.5, 0, 1],   % 紫蓝色
        [1, 0.5, 0.5], % 粉色
        [0.5, 1, 0.5]  % 浅绿色
    };
    
    % 创建积木对象
    for i = 1:size(brick_positions, 1)
        brick_obj = createBrick(brick_positions(i, :), brick_colors{i}, axes_3d);
        brick_objects{i} = brick_obj;
        % 初始时隐藏积木
        set(brick_obj, 'Visible', 'off');
    end
    
    fprintf('✅ 创建了 %d 个积木\n', length(brick_objects));
    
    %% 动画变量
    current_frame = 1;
    total_frames = 100;
    is_playing = false;
    animation_timer = [];
    
    fprintf('✅ 动画播放器初始化完成\n');
    fprintf('\n🎮 点击播放按钮开始动画！\n');
    
    %% 动画函数
    startAnimation_handle = @(~, ~) startAnimation_func();
    stopAnimation_handle = @(~, ~) stopAnimation_func();

    % 设置回调函数
    set(play_btn, 'Callback', startAnimation_handle);
    set(stop_btn, 'Callback', stopAnimation_handle);

    function startAnimation_func()
        fprintf('🎬 开始播放动画...\n');
        is_playing = true;

        % 创建定时器
        if ~isempty(animation_timer) && isvalid(animation_timer)
            stop(animation_timer);
            delete(animation_timer);
        end

        animation_timer = timer('ExecutionMode', 'fixedRate', ...
                                'Period', 0.1, ...
                                'TimerFcn', @(~,~) updateAnimation_func());
        start(animation_timer);
    end

    function stopAnimation_func()
        fprintf('⏹️ 停止动画\n');
        is_playing = false;
        current_frame = 1;

        if ~isempty(animation_timer) && isvalid(animation_timer)
            stop(animation_timer);
            delete(animation_timer);
        end

        % 重置显示
        updateDisplay_func();
    end

    function updateAnimation_func()
        if is_playing && current_frame <= total_frames
            updateDisplay_func();
            current_frame = current_frame + 1;

            if current_frame > total_frames
                stopAnimation_func();
                fprintf('✅ 动画播放完成\n');
            end
        end
    end

    function updateDisplay_func()
        % 更新进度条
        progress_bar.Value = current_frame;

        % 更新时间显示
        time_text.String = sprintf('帧: %d/%d', current_frame, total_frames);

        % 更新机器人位置
        frame_ratio = current_frame / total_frames;
        x_pos = 200 + 50 * sin(frame_ratio * 4 * pi);
        y_pos = 150 + 30 * cos(frame_ratio * 4 * pi);

        set(robot_marker, 'XData', x_pos, 'YData', y_pos);

        % 显示积木（根据进度）
        bricks_to_show = round(frame_ratio * length(brick_objects));
        for i = 1:length(brick_objects)
            if i <= bricks_to_show
                set(brick_objects{i}, 'Visible', 'on');
            else
                set(brick_objects{i}, 'Visible', 'off');
            end
        end

        drawnow limitrate;
    end

    % 初始显示
    updateDisplay_func();
    
catch ME
    fprintf('❌ 测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end

function brick_obj = createBrick(position, color, parent_axes)
% 创建积木3D对象

brick_size = [32, 16, 9.6];

% 创建立方体的顶点
vertices = [
    0, 0, 0;
    brick_size(1), 0, 0;
    brick_size(1), brick_size(2), 0;
    0, brick_size(2), 0;
    0, 0, brick_size(3);
    brick_size(1), 0, brick_size(3);
    brick_size(1), brick_size(2), brick_size(3);
    0, brick_size(2), brick_size(3)
];

% 平移到正确位置
vertices = vertices + repmat(position, 8, 1);

% 定义面
faces = [
    1, 2, 3, 4;  % 底面
    5, 6, 7, 8;  % 顶面
    1, 2, 6, 5;  % 前面
    3, 4, 8, 7;  % 后面
    1, 4, 8, 5;  % 左面
    2, 3, 7, 6   % 右面
];

% 绘制积木
brick_obj = patch('Vertices', vertices, ...
                  'Faces', faces, ...
                  'FaceColor', color, ...
                  'EdgeColor', 'black', ...
                  'FaceAlpha', 0.8, ...
                  'LineWidth', 1, ...
                  'Parent', parent_axes);

end
