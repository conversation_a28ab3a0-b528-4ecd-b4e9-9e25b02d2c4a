function quickStart()
% YuMi乐高拼接系统快速启动脚本
% 确保显示与参考图片相同的效果

fprintf('🚀 === YuMi乐高拼接系统快速启动 === 🚀\n');
fprintf('目标：实现与参考图片相同的3D可视化效果\n\n');

%% 步骤1：清理环境
fprintf('🧹 清理MATLAB环境...\n');
clear all;
close all;
clc;

%% 步骤2：验证基本环境
fprintf('🔍 验证基本环境...\n');
if ~exist('loadrobot', 'file')
    error('❌ 需要安装 Robotics System Toolbox');
end

if ~exist('mainbu.ldr', 'file')
    error('❌ 找不到 mainbu.ldr 文件');
end

fprintf('✅ 基本环境正常\n');

%% 步骤3：测试YuMi机器人显示
fprintf('🤖 测试YuMi机器人显示...\n');
try
    robot = loadrobot('abbYumi', 'DataFormat', 'row');
    
    % 创建测试窗口
    test_fig = figure('Name', 'YuMi机器人测试', 'Position', [100, 100, 1000, 700]);
    
    % 显示机器人
    config = robot.homeConfiguration;
    show(robot, config, 'Visuals', 'on', 'Frames', 'off');
    
    % 设置最佳视角（与参考图片一致）
    view(45, 30);
    axis equal;
    grid on;
    
    % 设置坐标轴范围
    xlim([-0.5, 0.8]);
    ylim([-0.6, 0.6]);
    zlim([-0.3, 0.8]);
    
    % 设置标签
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');
    title('YuMi双臂机器人模型');
    
    % 设置高质量渲染
    set(gcf, 'Renderer', 'opengl');
    set(gca, 'Projection', 'perspective');
    
    fprintf('✅ YuMi机器人显示成功\n');
    fprintf('💡 应该看到：黄色基座 + 灰色双臂\n');
    
    % 用户确认
    response = input('❓ 是否看到完整的YuMi机器人？(y/n): ', 's');
    if ~strcmpi(response, 'y')
        fprintf('⚠️ 机器人显示异常，尝试调整...\n');
        
        % 尝试不同的视角
        view(30, 45);
        drawnow;
        pause(1);
        
        view(60, 20);
        drawnow;
        pause(1);
        
        % 回到最佳视角
        view(45, 30);
        drawnow;
    end
    
    close(test_fig);
    
catch ME
    error('❌ YuMi机器人显示失败: %s', ME.message);
end

%% 步骤4：启动完整系统
fprintf('🎬 启动完整拼接系统...\n');
try
    % 运行主系统
    runEnhancedYumiSystem;
    
    fprintf('✅ 系统启动成功\n');
    fprintf('\n🎯 === 使用指南 === 🎯\n');
    fprintf('1. 检查3D场景中的YuMi机器人模型\n');
    fprintf('2. 确认看到50个半透明积木轮廓\n');
    fprintf('3. 点击"▶️ 开始拼接"按钮\n');
    fprintf('4. 观察左右臂轮流工作\n');
    fprintf('5. 验证积木逐个变为红色\n');
    
    fprintf('\n📋 === 验收清单 === 📋\n');
    fprintf('✅ YuMi机器人完整显示（黄色基座+灰色双臂）\n');
    fprintf('✅ 左右臂结构清晰可见\n');
    fprintf('✅ 50个积木轮廓正确显示\n');
    fprintf('✅ 控制界面功能正常\n');
    fprintf('✅ 动画流畅连续\n');
    fprintf('✅ 左右臂轮流工作\n');
    fprintf('✅ 夹爪动作可视化\n');
    fprintf('✅ 拼接过程符合mainbu.ldr设计\n');
    
    fprintf('\n🔧 === 故障排除 === 🔧\n');
    fprintf('如果遇到问题，请运行：testYuMiSystem\n');
    
catch ME
    error('❌ 系统启动失败: %s', ME.message);
end

fprintf('\n🎉 === 启动完成 === 🎉\n');
fprintf('享受YuMi乐高拼接的精彩过程！\n');

end
