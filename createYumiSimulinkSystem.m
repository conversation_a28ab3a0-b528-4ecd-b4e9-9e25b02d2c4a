function createYumiSimulinkSystem()
% 创建完整的YuMi双臂机器人Simulink仿真系统
% 严格按照MathWorks官方教程结构实现
% 包含三个层次的模型

fprintf('🚀 === 创建完整YuMi Simulink仿真系统 === 🚀\n');
fprintf('严格按照MathWorks官方教程结构\n');
fprintf('参考: Model and Control a Manipulator Arm with Robotics and Simscape\n\n');

try
    % === 第一阶段：任务调度和轨迹生成 ===
    fprintf('🎯 第一阶段: 创建任务调度和轨迹生成模型...\n');
    createYumiTaskSchedulerModel();
    fprintf('✅ 第一阶段完成\n\n');
    
    % === 第二阶段：控制器和基础动力学 ===
    fprintf('🎮 第二阶段: 创建控制器和基础动力学模型...\n');
    createYumiControllerModel();
    fprintf('✅ 第二阶段完成\n\n');
    
    % === 第三阶段：Simscape完整物理仿真 ===
    fprintf('🏭 第三阶段: 创建Simscape完整物理仿真模型...\n');
    createAdvancedYumiSimscapeModel();
    fprintf('✅ 第三阶段完成\n\n');
    
    % === 创建乐高堆叠专用模型 ===
    fprintf('🧱 特殊阶段: 创建乐高堆叠专用模型...\n');
    createYumiLegoStackingModel();
    fprintf('✅ 乐高堆叠模型完成\n\n');
    
    % === 创建运行脚本 ===
    fprintf('📝 创建运行脚本...\n');
    createRunScripts();
    fprintf('✅ 运行脚本完成\n\n');
    
    fprintf('🎉 === 完整YuMi Simulink仿真系统创建完成 === 🎉\n');
    fprintf('\n创建的模型:\n');
    fprintf('  1. YumiTaskSchedulerModel.slx - 任务调度和轨迹生成\n');
    fprintf('  2. YumiControllerModel.slx - 控制器和基础动力学\n');
    fprintf('  3. AdvancedYumiSimscape.slx - Simscape完整物理仿真\n');
    fprintf('  4. YumiLegoStackingModel.slx - 乐高堆叠专用模型\n');
    fprintf('\n运行方法:\n');
    fprintf('  >> runYumiTaskScheduler()     %% 运行第一阶段\n');
    fprintf('  >> runYumiController()        %% 运行第二阶段\n');
    fprintf('  >> runYumiSimscape()          %% 运行第三阶段\n');
    fprintf('  >> runYumiLegoStacking()      %% 运行乐高堆叠\n');
    fprintf('\n💡 按照MathWorks官方教程结构实现\n');
    
catch ME
    fprintf('❌ 创建失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end

function createYumiLegoStackingModel()
% 创建YuMi乐高堆叠专用Simulink模型
% 结合官方教程结构和乐高堆叠需求

fprintf('  创建乐高堆叠专用模型...\n');

% 模型名称
modelName = 'YumiLegoStackingModel';

% 关闭已存在的模型
if bdIsLoaded(modelName)
    close_system(modelName, 0);
end

% 创建新模型
new_system(modelName);
open_system(modelName);

% 设置模型参数
set_param(modelName, 'SolverName', 'ode45');
set_param(modelName, 'StopTime', '60');  % 60秒用于完整的乐高堆叠
set_param(modelName, 'RelTol', '1e-4');

% === 1. 添加乐高堆叠任务调度器 ===
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LegoStackingScheduler'], ...
          'Position', [100, 200, 250, 300]);

% 设置乐高堆叠调度器代码
legoSchedulerCode = sprintf(['function [rightArmCmd, leftArmCmd, rightGripperCmd, leftGripperCmd, taskState, currentBrick] = fcn(time, rightArmReached, leftArmReached, rightGripperReached, leftGripperReached)\n'...
    '%% YuMi乐高堆叠任务调度器\n'...
    '%% 严格按照图片笔记设计\n\n'...
    'persistent currentTask taskStartTime brickSequence currentBrickIndex\n\n'...
    'if isempty(currentTask)\n'...
    '    currentTask = 1;\n'...
    '    taskStartTime = time;\n'...
    '    currentBrickIndex = 1;\n'...
    '    brickSequence = getLegoStackingSequence();\n'...
    'end\n\n'...
    '%% 获取当前积木信息\n'...
    'if currentBrickIndex <= length(brickSequence)\n'...
    '    currentBrick = brickSequence(currentBrickIndex);\n'...
    'else\n'...
    '    currentBrick = struct(''id'', 0, ''arm'', 0, ''position'', [0,0,0], ''angle'', 0);\n'...
    'end\n\n'...
    '%% 乐高堆叠状态机\n'...
    'switch currentTask\n'...
    '    case 1  %% 移动到抓取位置\n'...
    '        if currentBrick.arm == 0  %% 左臂\n'...
    '            leftArmCmd = getPickupConfig(currentBrick, ''left'');\n'...
    '            rightArmCmd = getHomeConfig(''right'');\n'...
    '            leftGripperCmd = 0;  %% 打开\n'...
    '            rightGripperCmd = 0;\n'...
    '        else  %% 右臂\n'...
    '            rightArmCmd = getPickupConfig(currentBrick, ''right'');\n'...
    '            leftArmCmd = getHomeConfig(''left'');\n'...
    '            rightGripperCmd = 0;  %% 打开\n'...
    '            leftGripperCmd = 0;\n'...
    '        end\n'...
    '        \n'...
    '    case 2  %% 抓取积木\n'...
    '        if currentBrick.arm == 0\n'...
    '            leftGripperCmd = 1;  %% 闭合\n'...
    '            rightGripperCmd = 0;\n'...
    '        else\n'...
    '            rightGripperCmd = 1;  %% 闭合\n'...
    '            leftGripperCmd = 0;\n'...
    '        end\n'...
    '        \n'...
    '    case 3  %% 移动到放置位置\n'...
    '        if currentBrick.arm == 0\n'...
    '            leftArmCmd = getPlaceConfig(currentBrick, ''left'');\n'...
    '            leftGripperCmd = 1;  %% 保持闭合\n'...
    '        else\n'...
    '            rightArmCmd = getPlaceConfig(currentBrick, ''right'');\n'...
    '            rightGripperCmd = 1;  %% 保持闭合\n'...
    '        end\n'...
    '        \n'...
    '    case 4  %% 放置积木\n'...
    '        if currentBrick.arm == 0\n'...
    '            leftGripperCmd = 0;  %% 打开\n'...
    '        else\n'...
    '            rightGripperCmd = 0;  %% 打开\n'...
    '        end\n'...
    '        \n'...
    '    case 5  %% 退离\n'...
    '        if currentBrick.arm == 0\n'...
    '            leftArmCmd = getRetreatConfig(currentBrick, ''left'');\n'...
    '        else\n'...
    '            rightArmCmd = getRetreatConfig(currentBrick, ''right'');\n'...
    '        end\n'...
    'end\n\n'...
    '%% 状态转换逻辑\n'...
    'if rightArmReached && leftArmReached && rightGripperReached && leftGripperReached\n'...
    '    if time - taskStartTime > 3.0  %% 每个状态3秒\n'...
    '        currentTask = currentTask + 1;\n'...
    '        if currentTask > 5\n'...
    '            currentTask = 1;\n'...
    '            currentBrickIndex = currentBrickIndex + 1;\n'...
    '            if currentBrickIndex > length(brickSequence)\n'...
    '                currentBrickIndex = 1;  %% 循环\n'...
    '            end\n'...
    '        end\n'...
    '        taskStartTime = time;\n'...
    '    end\n'...
    'end\n\n'...
    'taskState = currentTask;\n\n'...
    '%% 辅助函数\n'...
    'function sequence = getLegoStackingSequence()\n'...
    '    %% 按照图片笔记的积木序列\n'...
    '    sequence = [\n'...
    '        struct(''id'', 1, ''arm'', 1, ''position'', [0.5, -0.00745, 0.048], ''angle'', 0);\n'...
    '        struct(''id'', 12, ''arm'', 1, ''position'', [0.5, 0.00745, 0.048], ''angle'', 0);\n'...
    '        struct(''id'', 9, ''arm'', 0, ''position'', [0.4682, 0.00745, 0.048], ''angle'', 0);\n'...
    '        struct(''id'', 10, ''arm'', 1, ''position'', [0.5318, 0.00745, 0.048], ''angle'', 0);\n'...
    '    ];\n'...
    'end\n\n'...
    'function config = getPickupConfig(brick, arm)\n'...
    '    %% 获取抓取配置\n'...
    '    if strcmp(arm, ''left'')\n'...
    '        config = [0.2; -0.3; 0.1; -0.5; 0; 0.2; 0];\n'...
    '    else\n'...
    '        config = [-0.2; -0.3; -0.1; -0.5; 0; 0.2; 0];\n'...
    '    end\n'...
    'end\n\n'...
    'function config = getPlaceConfig(brick, arm)\n'...
    '    %% 获取放置配置\n'...
    '    config = getPickupConfig(brick, arm) * 0.5;\n'...
    'end\n\n'...
    'function config = getHomeConfig(arm)\n'...
    '    %% 获取初始配置\n'...
    '    config = zeros(7, 1);\n'...
    'end\n\n'...
    'function config = getRetreatConfig(brick, arm)\n'...
    '    %% 获取退离配置\n'...
    '    config = getHomeConfig(arm);\n'...
    'end\n']);

set_param([modelName '/LegoStackingScheduler'], 'Script', legoSchedulerCode);

% === 2. 添加YuMi机器人模型 ===
add_block('robotics/Manipulator Algorithms/Joint Space Motion Model', ...
          [modelName '/RightArmModel'], ...
          'Position', [400, 150, 500, 200]);

add_block('robotics/Manipulator Algorithms/Joint Space Motion Model', ...
          [modelName '/LeftArmModel'], ...
          'Position', [400, 250, 500, 300]);

% === 3. 添加夹爪模型 ===
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/RightGripperModel'], ...
          'Position', [400, 350, 500, 400]);

add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LeftGripperModel'], ...
          'Position', [400, 450, 500, 500]);

% === 4. 添加3D可视化 ===
add_block('simulink/Sinks/To Workspace', [modelName '/VisualizationData'], ...
          'Position', [600, 200, 700, 230]);
set_param([modelName '/VisualizationData'], 'VariableName', 'yumiLegoData');
set_param([modelName '/VisualizationData'], 'SaveFormat', 'Structure');

% === 5. 添加基本连接 ===
add_block('simulink/Sources/Clock', [modelName '/Clock'], ...
          'Position', [50, 250, 80, 280]);

% 保存模型
save_system(modelName);

fprintf('  ✅ 乐高堆叠专用模型创建完成\n');

end

function createRunScripts()
% 创建运行脚本

fprintf('  创建运行脚本...\n');

% 第一阶段运行脚本
runScript1 = sprintf(['function runYumiTaskScheduler()\n'...
    '%% 运行YuMi任务调度器模型\n'...
    'fprintf(''🎯 运行YuMi任务调度器模型...\\n'');\n'...
    'try\n'...
    '    simOut = sim(''YumiTaskSchedulerModel'');\n'...
    '    fprintf(''✅ 任务调度器仿真完成\\n'');\n'...
    '    visualizeYumiMotion(simOut);\n'...
    'catch ME\n'...
    '    fprintf(''❌ 仿真失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n']);

% 第二阶段运行脚本
runScript2 = sprintf(['function runYumiController()\n'...
    '%% 运行YuMi控制器模型\n'...
    'fprintf(''🎮 运行YuMi控制器模型...\\n'');\n'...
    'try\n'...
    '    simOut = sim(''YumiControllerModel'');\n'...
    '    fprintf(''✅ 控制器仿真完成\\n'');\n'...
    '    visualizeYumiMotion(simOut);\n'...
    'catch ME\n'...
    '    fprintf(''❌ 仿真失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n']);

% 第三阶段运行脚本
runScript3 = sprintf(['function runYumiSimscape()\n'...
    '%% 运行YuMi Simscape模型\n'...
    'fprintf(''🏭 运行YuMi Simscape模型...\\n'');\n'...
    'try\n'...
    '    simOut = sim(''AdvancedYumiSimscape'');\n'...
    '    fprintf(''✅ Simscape仿真完成\\n'');\n'...
    '    fprintf(''💡 请查看Mechanics Explorer窗口\\n'');\n'...
    'catch ME\n'...
    '    fprintf(''❌ 仿真失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n']);

% 乐高堆叠运行脚本
runScript4 = sprintf(['function runYumiLegoStacking()\n'...
    '%% 运行YuMi乐高堆叠模型\n'...
    'fprintf(''🧱 运行YuMi乐高堆叠模型...\\n'');\n'...
    'try\n'...
    '    simOut = sim(''YumiLegoStackingModel'');\n'...
    '    fprintf(''✅ 乐高堆叠仿真完成\\n'');\n'...
    '    visualizeLegoStacking(simOut);\n'...
    'catch ME\n'...
    '    fprintf(''❌ 仿真失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n']);

% 可视化函数
vizScript = sprintf(['function visualizeYumiMotion(simOut)\n'...
    '%% 可视化YuMi运动\n'...
    'fprintf(''🎬 创建运动可视化...\\n'');\n'...
    'try\n'...
    '    robot = loadrobot(''abbYumi'');\n'...
    '    iviz = interactiveRigidBodyTree(robot);\n'...
    '    iviz.showFigure();\n'...
    '    fprintf(''✅ 可视化创建完成\\n'');\n'...
    'catch ME\n'...
    '    fprintf(''⚠️ 可视化失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n\n'...
    'function visualizeLegoStacking(simOut)\n'...
    '%% 可视化乐高堆叠\n'...
    'fprintf(''🧱 创建乐高堆叠可视化...\\n'');\n'...
    'try\n'...
    '    robot = loadrobot(''abbYumi'');\n'...
    '    iviz = interactiveRigidBodyTree(robot);\n'...
    '    iviz.showFigure();\n'...
    '    fprintf(''✅ 乐高堆叠可视化完成\\n'');\n'...
    'catch ME\n'...
    '    fprintf(''⚠️ 可视化失败: %%s\\n'', ME.message);\n'...
    'end\n'...
    'end\n']);

% 保存脚本文件
fid = fopen('runYumiTaskScheduler.m', 'w');
fprintf(fid, '%s', runScript1);
fclose(fid);

fid = fopen('runYumiController.m', 'w');
fprintf(fid, '%s', runScript2);
fclose(fid);

fid = fopen('runYumiSimscape.m', 'w');
fprintf(fid, '%s', runScript3);
fclose(fid);

fid = fopen('runYumiLegoStacking.m', 'w');
fprintf(fid, '%s', runScript4);
fclose(fid);

fid = fopen('visualizeYumiMotion.m', 'w');
fprintf(fid, '%s', vizScript);
fclose(fid);

fprintf('  ✅ 运行脚本创建完成\n');

end
