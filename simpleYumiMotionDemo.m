function simpleYumiMotionDemo()
% 简单的YuMi机械臂运动演示
% 直接展示机械臂运动，不依赖复杂的系统

fprintf('🤖 === 简单YuMi机械臂运动演示 === 🤖\n');
fprintf('直接展示机械臂运动效果\n\n');

try
    %% 第一步：加载机器人
    fprintf('🔧 加载YuMi机器人...\n');
    robot = loadrobot('abbYumi', 'DataFormat', 'row');
    home_config = robot.homeConfiguration;
    
    %% 第二步：创建显示窗口
    fprintf('🖥️ 创建显示窗口...\n');
    fig = figure('Name', '简单YuMi运动演示', 'Position', [100, 100, 1000, 700]);
    
    % 显示初始机器人
    show(robot, home_config, 'Visuals', 'on', 'Frames', 'off');
    view(45, 30);
    axis equal;
    grid on;
    xlim([-0.5, 0.8]);
    ylim([-0.6, 0.6]);
    zlim([-0.3, 0.8]);
    title('🤖 YuMi机械臂运动演示', 'FontSize', 14);
    
    %% 第三步：定义运动序列
    fprintf('📋 定义运动序列...\n');
    
    % 创建不同的关节配置
    configs = [];
    
    % 配置1：初始位置
    configs(1,:) = home_config;
    
    % 配置2：左臂向前伸展
    config2 = home_config;
    config2(1) = pi/6;      % 左臂肩部旋转
    config2(2) = -pi/4;     % 左臂肩部俯仰
    config2(3) = pi/6;      % 左臂肘部
    configs(2,:) = config2;
    
    % 配置3：右臂向前伸展
    config3 = home_config;
    config3(8) = -pi/6;     % 右臂肩部旋转
    config3(9) = -pi/4;     % 右臂肩部俯仰
    config3(10) = -pi/6;    % 右臂肘部
    configs(3,:) = config3;
    
    % 配置4：双臂同时运动
    config4 = home_config;
    config4(1) = pi/4;      % 左臂
    config4(2) = -pi/3;
    config4(3) = pi/4;
    config4(8) = -pi/4;     % 右臂
    config4(9) = -pi/3;
    config4(10) = -pi/4;
    configs(4,:) = config4;
    
    % 配置5：返回初始位置
    configs(5,:) = home_config;
    
    %% 第四步：执行运动演示
    fprintf('🎬 开始运动演示...\n');
    
    for i = 1:size(configs, 1)
        fprintf('  执行配置 %d/%d\n', i, size(configs, 1));
        
        if i == 1
            current_config = configs(i,:);
        else
            % 从当前配置平滑过渡到目标配置
            start_config = current_config;
            target_config = configs(i,:);
            
            % 20步插值运动
            for step = 1:20
                progress = step / 20;
                current_config = start_config + progress * (target_config - start_config);
                
                % 更新机器人显示
                cla;
                show(robot, current_config, 'Visuals', 'on', 'Frames', 'off');
                view(45, 30);
                axis equal;
                grid on;
                xlim([-0.5, 0.8]);
                ylim([-0.6, 0.6]);
                zlim([-0.3, 0.8]);
                title(sprintf('🤖 YuMi运动演示 - 配置 %d/%d (步骤 %d/20)', ...
                             i, size(configs, 1), step), 'FontSize', 14);
                
                drawnow;
                pause(0.1);
            end
        end
        
        % 在每个配置停留一下
        pause(0.5);
    end
    
    %% 第五步：连续运动演示
    fprintf('🔄 连续运动演示...\n');
    
    % 创建连续的运动模式
    for cycle = 1:3
        fprintf('  运动周期 %d/3\n', cycle);
        
        % 左臂运动
        for angle = 0:0.1:pi/2
            config = home_config;
            config(1) = angle;
            config(2) = -angle/2;
            config(3) = angle/2;
            
            cla;
            show(robot, config, 'Visuals', 'on', 'Frames', 'off');
            view(45, 30);
            axis equal;
            grid on;
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            title(sprintf('🤖 连续运动演示 - 左臂运动 (周期 %d/3)', cycle), 'FontSize', 14);
            
            drawnow;
            pause(0.05);
        end
        
        % 右臂运动
        for angle = 0:0.1:pi/2
            config = home_config;
            config(8) = -angle;
            config(9) = -angle/2;
            config(10) = -angle/2;
            
            cla;
            show(robot, config, 'Visuals', 'on', 'Frames', 'off');
            view(45, 30);
            axis equal;
            grid on;
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            title(sprintf('🤖 连续运动演示 - 右臂运动 (周期 %d/3)', cycle), 'FontSize', 14);
            
            drawnow;
            pause(0.05);
        end
        
        % 返回初始位置
        for step = 1:10
            progress = step / 10;
            config = config + progress * (home_config - config);
            
            cla;
            show(robot, config, 'Visuals', 'on', 'Frames', 'off');
            view(45, 30);
            axis equal;
            grid on;
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            title(sprintf('🤖 连续运动演示 - 返回初始位置 (周期 %d/3)', cycle), 'FontSize', 14);
            
            drawnow;
            pause(0.1);
        end
    end
    
    %% 第六步：完成演示
    fprintf('🎉 运动演示完成！\n');
    
    % 显示最终状态
    cla;
    show(robot, home_config, 'Visuals', 'on', 'Frames', 'off');
    view(45, 30);
    axis equal;
    grid on;
    xlim([-0.5, 0.8]);
    ylim([-0.6, 0.6]);
    zlim([-0.3, 0.8]);
    title('🎉 YuMi运动演示完成！', 'FontSize', 14);
    
    fprintf('\n📊 === 演示总结 === 📊\n');
    fprintf('✅ 成功展示了YuMi机械臂运动\n');
    fprintf('✅ 左右臂分别运动\n');
    fprintf('✅ 平滑的运动插值\n');
    fprintf('✅ 实时的关节配置更新\n');
    fprintf('✅ 可视化的运动轨迹\n');
    
    fprintf('\n💡 如果你看到了机械臂运动，说明系统工作正常！\n');
    fprintf('现在可以尝试运行完整的拼接系统：runVisualYumiSystem\n');
    
catch ME
    fprintf('❌ 运动演示失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n🔧 故障排除:\n');
    fprintf('1. 确保安装了Robotics System Toolbox\n');
    fprintf('2. 检查MATLAB版本兼容性\n');
    fprintf('3. 尝试重启MATLAB\n');
end

end
