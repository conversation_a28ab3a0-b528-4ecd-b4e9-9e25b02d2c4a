function runSimulink(trajectories, T_total, options)
    % 改进的Simulink仿真函数，支持YuMi机器人3D可视化
    % 输入：
    %   trajectories - 轨迹序列
    %   T_total - 总仿真时间
    %   options - 仿真选项（可选）

    if nargin < 3
        options = struct();
    end

    % 仿真选项
    show_3d_animation = getfield_default(options, 'show_3d_animation', true);
    save_animation = getfield_default(options, 'save_animation', false);
    real_time_factor = getfield_default(options, 'real_time_factor', 1.0);

    modelName = 'YumiSimscape';

    % 检查并打开Simulink模型
    try
        if ~bdIsLoaded(modelName)
            open_system([modelName '.slx']); % 打開模型
            fprintf('✓ Simulink模型 %s 已加载\n', modelName);
        else
            fprintf('✓ Simulink模型 %s 已在运行\n', modelName);
        end
    catch ME
        fprintf('❌ 无法加载Simulink模型: %s\n', ME.message);
        return;
    end

    fprintf('\n=== 開始 YuMi Simulink 3D 仿真 ===\n');
    fprintf('轨迹数量: %d\n', length(trajectories));
    fprintf('仿真时间: %.1f秒\n', T_total);
    if show_3d_animation
        fprintf('3D动画: 启用\n');
    else
        fprintf('3D动画: 禁用\n');
    end
    fprintf('实时倍率: %.1fx\n', real_time_factor);

    % 设置Simulink模型参数
    try
        set_param(modelName, 'StopTime', num2str(T_total));
        set_param(modelName, 'SolverType', 'Variable-step');
        set_param(modelName, 'Solver', 'ode45');

        % 启用3D动画（如果模型支持）
        if show_3d_animation
            try
                set_param([modelName '/Mechanics Explorer'], 'Animation', 'on');
                fprintf('✓ 3D动画已启用\n');
            catch
                fprintf('⚠ 无法启用3D动画，可能模型不支持\n');
            end
        end

    catch ME
        fprintf('⚠ 设置模型参数时出错: %s\n', ME.message);
    end

    % 执行每个轨迹的仿真
    for i = 1:length(trajectories)
        traj = trajectories{i};

        % 使用平滑轨迹（如果存在）
        if isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
            Q = traj.Q_smooth;
        else
            Q = traj.Q;
        end

        N = size(Q, 1);

        % 调整时间以适应实时倍率
        actual_duration = T_total / real_time_factor;
        t_all = linspace(0, actual_duration, N)';
        Ts = t_all(2) - t_all(1);

        fprintf('\n--- 仿真任务 %d/%d ---\n', i, length(trajectories));
        fprintf('手臂: %s\n', traj.arm);
        fprintf('轨迹点数: %d\n', N);
        fprintf('采样时间: %.4f秒\n', Ts);
        fprintf('持续时间: %.2f秒\n', actual_duration);

        % 准备双臂轨迹数据
        if strcmp(traj.arm, 'right')
            % 右臂轨迹，左臂保持静止
            if size(Q, 2) >= 7
                qMatR = Q(:, 1:7);
            else
                qMatR = [Q, zeros(N, 7-size(Q,2))]; % 补齐到7个关节
            end
            qMatL = zeros(N, 7); % 左臂静止
        else
            % 左臂轨迹，右臂保持静止
            qMatR = zeros(N, 7); % 右臂静止
            if size(Q, 2) >= 7
                qMatL = Q(:, 1:7);
            else
                qMatL = [Q, zeros(N, 7-size(Q,2))]; % 补齐到7个关节
            end
        end

        % 组合轨迹数据 [时间, 关节角度]
        trajDataRight = [t_all, qMatR];
        trajDataLeft = [t_all, qMatL];

        % 传输数据到base workspace
        try
            assignin('base', 'Ts', Ts);
            assignin('base', 'trajDataRight', trajDataRight);
            assignin('base', 'trajDataLeft', trajDataLeft);
            assignin('base', 'currentTask', i);
            assignin('base', 'currentArm', traj.arm);

            % 如果有任务信息，也传输过去
            if isfield(traj, 'task')
                assignin('base', 'taskInfo', traj.task);
            end

            fprintf('✓ 轨迹数据已传输到workspace\n');

        catch ME
            fprintf('❌ 数据传输失败: %s\n', ME.message);
            continue;
        end

        % 执行Simulink仿真
        fprintf('🤖 开始Simulink仿真...\n');
        tic;

        try
            % 使用sim函数执行仿真
            simOut = sim(modelName, 'StopTime', num2str(actual_duration));

            elapsed_time = toc;
            fprintf('✓ 仿真完成 (用时: %.2f秒)\n', elapsed_time);

            % 可选：保存仿真结果
            if save_animation && i == 1
                try
                    % 保存仿真数据
                    save(sprintf('yumi_simulation_task_%d.mat', i), 'simOut', 'traj');
                    fprintf('✓ 仿真数据已保存\n');
                catch
                    fprintf('⚠ 无法保存仿真数据\n');
                end
            end

        catch ME
            fprintf('❌ 仿真失败: %s\n', ME.message);
            fprintf('   错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
            continue;
        end

        % 任务间暂停
        if i < length(trajectories)
            fprintf('⏸ 等待下个任务...\n');
            pause(1.0);
        end
    end

    fprintf('\n🎉 所有Simulink仿真完成！\n');

    % 清理workspace（可选）
    try
        evalin('base', 'clear Ts trajDataRight trajDataLeft currentTask currentArm taskInfo');
        fprintf('✓ Workspace已清理\n');
    catch
        % 不影响主要功能
    end
end

function value = getfield_default(s, field, default_value)
    % 获取结构体字段，如果不存在则返回默认值
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end



%  try
%         % 檢查模型是否存在
%         if ~bdIsLoaded(modelName)
%             open_system([modelName '.slx']); % 打開模型
%             fprintf('✓ Simulink 模型 %s 已載入\n', modelName);
%         end
%     catch
%         error('❌ 無法載入 Simulink 模型: %s.slx', modelName);
%     end
%  % 
%     % 初始化雙臂軌跡數據
%     fprintf('\n=== 開始 Simulink 雙臂軌跡模擬 ===\n');
%     fprintf('總軌跡數: %d\n', length(trajectories));
%     fprintf('模擬總時間: %.1f 秒\n\n', T_total);
% 
%     for i = 1:length(trajectories)
%         traj = trajectories{i};
% 
%         % 檢查軌跡數據完整性
%         if isempty(traj.Q)
%             fprintf('⚠ 軌跡 %d 數據為空，跳過\n', i);
%             continue;
%         end
% 
%         % 使用平滑後的軌跡（如果存在）
%         if isfield(traj, 'Q_smooth') && ~isempty(traj.Q_smooth)
%             Q = traj.Q_smooth;
%         else
%             Q = traj.Q;
%         end
% 
%         N = size(Q, 1);
%         fprintf('【任務 %d/%d】%s 手臂 → %d 個軌跡點\n', ...
%             i, length(trajectories), traj.arm, N);
% 
%         % ===== 關鍵：正確提取左右手關節角度 =====
%         if strcmp(traj.arm, 'right')
%             % 右手：YuMi 的前7個關節 (1-7)
%             qMat = Q(:, 1:7);
%             fprintf('  使用右手關節 1-7\n');
%         else
%             % 左手：YuMi 的後7個關節 (8-14)
%             qMat = Q(:, 8:14);
%             fprintf('  使用左手關節 8-14\n');
%         end
% 
%         % ===== 生成時間向量 =====
%         t_all = linspace(0, T_total, N)';
% 
%         % ===== 組合軌跡數據 [時間, 關節角度] =====
%         trajData = [t_all, qMat];
% 
%         % 顯示數據信息
%         fprintf('  軌跡數據維度: %dx%d (時間+7關節)\n', size(trajData));
%         fprintf('  時間範圍: %.2f - %.2f 秒\n', t_all(1), t_all(end));
%         fprintf('  關節角度範圍: [%.3f, %.3f] 弧度\n', ...
%             min(qMat(:)), max(qMat(:)));
% 
%         % ===== 傳輸到 Base Workspace =====
%         try
%             assignin('base', 'trajData', trajData);
%             assignin('base', 'currentArm', traj.arm);
%             assignin('base', 'T_total', T_total);
% 
%             % 額外的軌跡信息
%             assignin('base', 'armName', traj.arm);
%             assignin('base', 'eeFrame', traj.eeName);
%             assignin('base', 'taskNumber', i);
% 
%             fprintf('  ✓ 軌跡數據已傳輸到 Base Workspace\n');
% 
%         catch ME
%             fprintf('  ❌ 數據傳輸失敗: %s\n', ME.message);
%             continue;
%         end
% 
%         % ===== 構造 SimulationInput =====
%         try
%             simIn = Simulink.SimulationInput(modelName);
% 
%             % 設置模擬參數
%             simIn = simIn.setModelParameter('StopTime', num2str(T_total));
%             simIn = simIn.setVariable('trajData', trajData);
%             simIn = simIn.setVariable('currentArm', traj.arm);
% 
%             fprintf('  🤖 開始模擬...');
%             tic;
% 
%             % 執行模擬
%             simOut = sim(simIn);
% 
%             elapsed = toc;
%             fprintf(' 完成 (%.1f 秒)\n', elapsed);
% 
%             % 可選：保存模擬結果
%             if nargout > 0 || i == 1  % 僅為示例
%                 % 提取關節角度數據（如果需要）
%                 try
%                     if strcmp(traj.arm, 'right')
%                         jointData = simOut.get('rightArmJoints');
%                     else
%                         jointData = simOut.get('leftArmJoints');
%                     end
%                     fprintf('  📊 模擬數據已提取\n');
%                 catch
%                     fprintf('  ⚠ 無法提取模擬數據\n');
%                 end
%             end
% 
%         catch ME
%             fprintf('  ❌ 模擬失敗: %s\n', ME.message);
%             fprintf('    錯誤位置: %s (第 %d 行)\n', ...
%                 ME.stack(1).name, ME.stack(1).line);
%             continue;
%         end
% 
%         % 任務間暫停
%         if i < length(trajectories)
%             fprintf('  ⏸ 等待下個任務...\n\n');
%             pause(1.0);
%         end
%     end
% 
%     fprintf('\n🎉 所有軌跡模擬完成！\n');
% 
%     % 清理 Base Workspace（可選）
%     try
%         evalin('base', 'clear trajData currentArm armName eeFrame taskNumber T_total');
%         fprintf('✓ Base Workspace 已清理\n');
%     catch
%         % 不影響主要功能
%     end
% end