function runPrecisionYumiSystem()
% 启动精确YuMi乐高拼接系统
% 解决流畅度、协作和定位精度三大问题

fprintf('🎯 === 启动精确YuMi乐高拼接系统 === 🎯\n');
fprintf('解决流畅度、协作和定位精度三大关键问题\n\n');

try
    %% 环境检查
    fprintf('🔍 环境检查...\n');
    
    % 检查必要文件
    required_files = {'mainbu.ldr', 'LDRParser.m', 'PrecisionYumiAssembly.m'};
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            error('缺少必要文件: %s', required_files{i});
        end
        fprintf('  ✅ %s 存在\n', required_files{i});
    end
    
    % 检查工具箱
    if ~exist('loadrobot', 'file')
        error('需要Robotics System Toolbox');
    end
    fprintf('  ✅ Robotics System Toolbox 可用\n');
    
    %% 创建精确系统
    fprintf('\n🎯 创建精确YuMi拼接系统...\n');
    
    precision_system = PrecisionYumiAssembly();
    
    %% 显示系统信息
    fprintf('\n📊 === 精确系统信息 === 📊\n');
    
    fprintf('🎯 三大问题解决方案:\n');
    fprintf('   1. 运动流畅度: 50步插值 + 50Hz更新频率\n');
    fprintf('   2. 左右臂协作: Y≥-100mm用左臂，Y<-100mm用右臂\n');
    fprintf('   3. 定位精度: LDR坐标精确转换到世界坐标\n');
    
    fprintf('\n🎬 运动流畅度改进:\n');
    fprintf('   - 运动步数: 50步 (原来20步)\n');
    fprintf('   - 更新频率: 50Hz (原来10Hz)\n');
    fprintf('   - 插值算法: 三次样条平滑插值\n');
    fprintf('   - 显示优化: 每3步更新一次显示\n');
    
    fprintf('\n🤝 左右臂协作改进:\n');
    fprintf('   - 智能任务分配: 根据Y坐标自动分配\n');
    fprintf('   - 左臂任务: %d个积木 (Y ≥ -100mm)\n', length(precision_system.left_arm_tasks));
    fprintf('   - 右臂任务: %d个积木 (Y < -100mm)\n', length(precision_system.right_arm_tasks));
    fprintf('   - 交替工作: 避免冲突，提高效率\n');
    
    fprintf('\n🎯 定位精度改进:\n');
    fprintf('   - 坐标缩放: 1mm = 0.001m\n');
    fprintf('   - 位置偏移: [0.1, -0.12, -0.15]m\n');
    fprintf('   - 转换矩阵: 4x4齐次坐标变换\n');
    fprintf('   - 精度验证: 自动检查位置误差\n');
    
    fprintf('\n🏗️ 建筑信息:\n');
    fprintf('   - 设计文件: mainbu.ldr\n');
    fprintf('   - 积木总数: %d 个\n', length(precision_system.target_bricks));
    fprintf('   - 拼接步骤: %d 步\n', precision_system.total_steps);
    
    fprintf('\n🎮 操作说明:\n');
    fprintf('   ▶️  点击"精确拼接" - 启动精确拼接动画\n');
    fprintf('   ⏸️  点击"暂停" - 暂停当前动画\n');
    fprintf('   ⏹️  点击"停止" - 停止并重置\n');
    fprintf('   🎯 点击"测试精确运动" - 测试运动流畅度\n');
    fprintf('   🤝 点击"测试协作" - 测试左右臂协作\n');
    fprintf('   📊 拖动进度条 - 跳转到任意步骤\n');
    
    fprintf('\n🔧 精确运动原理:\n');
    fprintf('   - 50步平滑插值 (2%%增量)\n');
    fprintf('   - 三次样条插值函数: 3t²-2t³\n');
    fprintf('   - 关节角度限制: [-π, π]\n');
    fprintf('   - 50Hz高频更新\n');
    fprintf('   - 智能显示优化\n');
    
    fprintf('\n🤝 协作工作原理:\n');
    fprintf('   - Y坐标分析: 自动分配机械臂\n');
    fprintf('   - 序列优化: 左右臂交替工作\n');
    fprintf('   - 冲突避免: 确保安全距离\n');
    fprintf('   - 效率最大化: 并行工作策略\n');
    
    fprintf('\n🎯 定位精度原理:\n');
    fprintf('   - LDR解析: 提取原始坐标\n');
    fprintf('   - 坐标转换: mm -> m 单位转换\n');
    fprintf('   - 空间变换: 4x4变换矩阵\n');
    fprintf('   - 精度验证: 1mm误差检测\n');
    
    fprintf('\n🎉 === 精确系统启动完成 === 🎉\n');
    fprintf('💡 现在你可以看到:\n');
    fprintf('   - 🎬 超流畅的机械臂运动 (50步插值)\n');
    fprintf('   - 🤝 智能的左右臂协作 (按Y坐标分配)\n');
    fprintf('   - 🎯 精确的积木定位 (按LDR设计)\n');
    fprintf('   - 🔄 50Hz高频动画更新\n');
    fprintf('   - 📊 详细的状态反馈\n');
    
    fprintf('\n🚀 使用建议:\n');
    fprintf('1. 先点击"测试精确运动"看看流畅度改进\n');
    fprintf('2. 再点击"测试协作"看看左右臂分工\n');
    fprintf('3. 最后点击"精确拼接"开始完整动画\n');
    fprintf('4. 观察积木按LDR设计精确放置\n');
    
    fprintf('\n📋 === 预期效果 === 📋\n');
    fprintf('✅ 运动流畅度: 丝般顺滑的机械臂运动\n');
    fprintf('✅ 左右臂协作: 智能分工，交替工作\n');
    fprintf('✅ 定位精度: 积木精确放置到LDR指定位置\n');
    fprintf('✅ 动画质量: 50Hz高频更新，无卡顿\n');
    fprintf('✅ 状态反馈: 详细的LDR坐标和世界坐标信息\n');
    fprintf('✅ 完整拼接: 50个步骤全部完成\n');
    
    fprintf('\n🎯 立即测试精确功能:\n');
    fprintf('点击"测试精确运动"或"测试协作"按钮！\n');
    
catch ME
    fprintf('❌ 精确系统启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n🔧 故障排除:\n');
    fprintf('1. 确保安装了Robotics System Toolbox\n');
    fprintf('2. 确保mainbu.ldr和LDRParser.m文件存在\n');
    fprintf('3. 检查MATLAB版本兼容性\n');
    fprintf('4. 尝试重启MATLAB\n');
    
    fprintf('\n💡 如果仍有问题，可以尝试:\n');
    fprintf('   - 关闭其他MATLAB图形窗口\n');
    fprintf('   - 清理工作空间: clear all; close all\n');
    fprintf('   - 重新运行: runPrecisionYumiSystem\n');
end

end
