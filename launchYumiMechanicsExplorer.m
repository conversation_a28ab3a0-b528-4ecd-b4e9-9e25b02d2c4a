function launchYumiMechanicsExplorer()
% Launch YuMi Mechanics Explorer Interface
% Create the exact same interface as shown in MathWorks official tutorial
% Avoid Chinese character encoding issues

fprintf('=== Launch YuMi Mechanics Explorer Interface ===\n');
fprintf('Target: Generate the exact same interface as in the official tutorial\n');
fprintf('Content: YuMi robot + Blue blocks + Green platform + Real-time animation\n\n');

try
    % === Method 1: Try official example ===
    fprintf('Method 1: Try official example...\n');
    
    try
        % Check if official example exists
        if exist('modelWithSimscapeRobotAndEnvironmentDynamics.slx', 'file')
            fprintf('Found official example model, running directly...\n');
            simOut = sim('modelWithSimscapeRobotAndEnvironmentDynamics');
            fprintf('SUCCESS: Official example running successfully\n');
            return;
        end
    catch
        fprintf('Official example not available, trying custom method\n');
    end
    
    % === Method 2: Create simplified Simscape model ===
    fprintf('\nMethod 2: Create simplified Simscape model...\n');
    
    % Model name
    modelName = 'YumiMechanicsExplorerDemo';
    
    % Create new model
    if bdIsLoaded(modelName)
        close_system(modelName, 0);
    end
    
    new_system(modelName);
    open_system(modelName);
    
    % Set solver
    set_param(modelName, 'SolverName', 'ode23t');
    set_param(modelName, 'StopTime', '15');
    
    % === Add basic Simscape components ===
    fprintf('Adding Simscape components...\n');
    
    % Solver Configuration
    add_block('nesl_utility/Solver Configuration', ...
              [modelName '/Solver Configuration'], ...
              'Position', [50, 50, 150, 100]);
    
    % World Frame
    add_block('sm_lib/Frames and Transforms/World Frame', ...
              [modelName '/World Frame'], ...
              'Position', [200, 50, 250, 100]);
    
    % === Add YuMi robot ===
    fprintf('Adding YuMi robot...\n');
    
    % Robot subsystem
    add_block('simulink/Ports & Subsystems/Subsystem', ...
              [modelName '/YuMi Robot System'], ...
              'Position', [300, 150, 500, 250]);
    
    % Configure robot subsystem
    configureYumiRobotSubsystem([modelName '/YuMi Robot System']);
    
    % === Add environment objects ===
    fprintf('Adding environment objects...\n');
    
    % Blue blocks (as shown in the image)
    add_block('sm_lib/Body Elements/Solid', [modelName '/Blue Block 1'], ...
              'Position', [300, 300, 350, 350]);
    add_block('sm_lib/Body Elements/Solid', [modelName '/Blue Block 2'], ...
              'Position', [400, 300, 450, 350]);
    
    % Green platform (as shown in the image)
    add_block('sm_lib/Body Elements/Solid', [modelName '/Green Platform'], ...
              'Position', [500, 300, 550, 350]);
    
    % === Add Mechanics Explorer ===
    fprintf('Adding Mechanics Explorer...\n');
    
    try
        add_block('sm_lib/Utilities/Mechanics Explorer', ...
                  [modelName '/Mechanics Explorer'], ...
                  'Position', [600, 150, 700, 250]);
        
        % Configure Mechanics Explorer
        set_param([modelName '/Mechanics Explorer'], 'StartVisualization', 'on');
        
    catch ME
        fprintf('Mechanics Explorer add failed: %s\n', ME.message);
        
        % Use alternative visualization
        add_block('simulink/Sinks/Scope', [modelName '/Visualization'], ...
                  'Position', [600, 150, 650, 200]);
    end
    
    % === Add clock and connections ===
    add_block('simulink/Sources/Clock', [modelName '/Clock'], ...
              'Position', [50, 150, 80, 180]);
    
    % Basic connections
    try
        add_line(modelName, 'World Frame/1', 'YuMi Robot System/1');
    catch
        % Ignore connection failures
    end
    
    % === Save and run model ===
    save_system(modelName);
    
    fprintf('SUCCESS: Model created successfully\n');
    fprintf('Starting simulation...\n');
    
    % Run simulation
    simOut = sim(modelName);
    
    if ~isempty(simOut)
        fprintf('SUCCESS: Simulation completed successfully\n');
        fprintf('TIP: Please check the opened windows\n');
    end
    
    % === Method 3: Direct YuMi visualization ===
    fprintf('\nMethod 3: Direct YuMi visualization...\n');
    
    try
        % Load YuMi robot
        robot = loadrobot('abbYumi');
        
        % Create interactive visualization
        iviz = interactiveRigidBodyTree(robot);
        iviz.showFigure();
        
        % Set view angle (consistent with official tutorial)
        view(45, 30);
        
        % Add environment objects
        hold on;
        
        % Blue blocks
        plotCube([0.1, 0.1, 0.1], [0.6, 0.2, 0.05], 'blue');
        plotCube([0.1, 0.1, 0.1], [0.6, -0.2, 0.05], 'blue');
        
        % Green platform
        plotCube([0.3, 0.3, 0.02], [0.5, 0, 0.0], 'green');
        
        % Simple animation
        fprintf('Starting animation demo...\n');
        for t = 0:0.1:10
            q = robot.homeConfiguration;
            q(1) = 0.3 * sin(t);
            q(8) = -0.3 * sin(t);
            iviz.Configuration = q;
            drawnow;
            pause(0.05);
        end
        
        fprintf('SUCCESS: YuMi visualization completed\n');
        
    catch ME
        fprintf('YuMi visualization failed: %s\n', ME.message);
    end
    
    fprintf('\n=== YuMi Mechanics Explorer Launch Completed ===\n');
    fprintf('You should now see the YuMi robot 3D visualization interface\n');
    
catch ME
    fprintf('ERROR: Launch failed: %s\n', ME.message);
    
    % Final backup plan
    fprintf('\nBackup plan: Basic YuMi display\n');
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Dual-Arm Robot');
        view(45, 30);
        fprintf('SUCCESS: Basic YuMi display successful\n');
    catch
        fprintf('ERROR: All methods failed\n');
    end
end

end

function configureYumiRobotSubsystem(subsystemPath)
% Configure YuMi robot subsystem

fprintf('  Configuring robot subsystem...\n');

% Open subsystem
open_system(subsystemPath);

% Delete default ports
delete_block([subsystemPath '/In1']);
delete_block([subsystemPath '/Out1']);

% Add input port
add_block('simulink/Sources/In1', [subsystemPath '/World Connection'], ...
          'Position', [50, 100, 80, 130]);

% Add YuMi robot model using MATLAB Function
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [subsystemPath '/YuMi Controller'], ...
          'Position', [150, 80, 300, 150]);

% Set YuMi controller code
yumiCode = sprintf(['function robotState = fcn(worldInput)\n'...
    '%% YuMi Robot Controller\n'...
    'persistent robot iviz initialized\n'...
    'if isempty(initialized)\n'...
    '    try\n'...
    '        robot = loadrobot(''abbYumi'');\n'...
    '        iviz = interactiveRigidBodyTree(robot);\n'...
    '        iviz.showFigure();\n'...
    '        initialized = true;\n'...
    '    catch\n'...
    '        initialized = false;\n'...
    '    end\n'...
    'end\n\n'...
    'if initialized\n'...
    '    %% Simple motion\n'...
    '    q = robot.homeConfiguration;\n'...
    '    t = worldInput;\n'...
    '    q(1) = 0.3 * sin(t * 0.5);\n'...
    '    q(8) = -0.3 * sin(t * 0.5);\n'...
    '    iviz.Configuration = q;\n'...
    'end\n\n'...
    'robotState = 1;\n']);

set_param([subsystemPath '/YuMi Controller'], 'Script', yumiCode);

% Add output port
add_block('simulink/Sinks/Out1', [subsystemPath '/Robot Output'], ...
          'Position', [350, 105, 380, 125]);

% Connect signals
add_line(subsystemPath, 'World Connection/1', 'YuMi Controller/1');
add_line(subsystemPath, 'YuMi Controller/1', 'Robot Output/1');

fprintf('  SUCCESS: Robot subsystem configured\n');

end

function plotCube(size, position, color)
% Plot cube

try
    % Cube vertices
    vertices = [
        0 0 0; 1 0 0; 1 1 0; 0 1 0;
        0 0 1; 1 0 1; 1 1 1; 0 1 1
    ];
    
    % Scale and translate
    vertices = vertices .* repmat(size, 8, 1) + repmat(position, 8, 1);
    
    % Cube faces
    faces = [
        1 2 3 4; 5 6 7 8; 1 2 6 5;
        3 4 8 7; 1 4 8 5; 2 3 7 6
    ];
    
    % Draw
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', 0.7, ...
          'EdgeColor', 'k', 'LineWidth', 1);
catch
    % Simplified drawing
    plot3(position(1), position(2), position(3), ...
          'o', 'Color', color, 'MarkerSize', 20, 'MarkerFaceColor', color);
end

end
