classdef RRTPlanner < handle
    % RRT路径规划器，专为双臂机器人设计
    % 支持避障和双臂协调约束
    
    properties
        robot           % 机器人模型
        collision_checker % 碰撞检测器
        max_iterations  % 最大迭代次数
        step_size      % 步长
        goal_bias      % 目标偏置概率
        goal_tolerance % 目标容差
        joint_limits   % 关节限制
        tree_nodes     % RRT树节点
        tree_parents   % 父节点索引
        tree_costs     % 到根节点的代价
    end
    
    methods
        function obj = RRTPlanner(robot, collision_checker, options)
            % 构造函数
            % 输入：
            %   robot - 机器人模型
            %   collision_checker - 碰撞检测器对象
            %   options - 规划参数结构体
            
            obj.robot = robot;
            obj.collision_checker = collision_checker;
            
            % 设置默认参数
            if nargin < 3
                options = struct();
            end
            
            obj.max_iterations = getfield_default(options, 'max_iterations', 5000);
            obj.step_size = getfield_default(options, 'step_size', 0.1);
            obj.goal_bias = getfield_default(options, 'goal_bias', 0.1);
            obj.goal_tolerance = getfield_default(options, 'goal_tolerance', 0.05);
            
            % 获取关节限制
            if isfield(robot, 'PositionLimits')
                obj.joint_limits = robot.PositionLimits;
            else
                % 默认关节限制（基于YuMi机器人）
                obj.joint_limits = repmat([-pi, pi], robot.NumBodies, 1);
            end
        end
        
        function [path, success, info] = planPath(obj, q_start, q_goal, arm_name)
            % 规划从起始到目标的路径
            % 输入：
            %   q_start - 起始关节角配置
            %   q_goal - 目标关节角配置  
            %   arm_name - 手臂名称 ('left' 或 'right')
            % 输出：
            %   path - 路径点序列
            %   success - 是否成功
            %   info - 规划信息
            
            fprintf('开始RRT路径规划 (%s臂)...\n', arm_name);
            
            % 初始化
            obj.tree_nodes = q_start;
            obj.tree_parents = 0;
            obj.tree_costs = 0;
            
            success = false;
            path = [];
            info = struct();
            info.iterations = 0;
            info.nodes_explored = 1;
            
            % 检查起始和目标配置的有效性
            if ~obj.isValidConfiguration(q_start, arm_name)
                fprintf('起始配置无效\n');
                return;
            end
            
            if ~obj.isValidConfiguration(q_goal, arm_name)
                fprintf('目标配置无效\n');
                return;
            end
            
            % RRT主循环
            for iter = 1:obj.max_iterations
                info.iterations = iter;
                
                % 采样随机配置
                if rand < obj.goal_bias
                    q_rand = q_goal;
                else
                    q_rand = obj.sampleRandomConfiguration(arm_name); % Pass arm_name
                end
                
                % 找到最近的树节点
                [q_near, near_idx] = obj.findNearestNode(q_rand);
                
                % 向随机点扩展
                q_new = obj.steer(q_near, q_rand);
                
                % 检查新配置的有效性
                if obj.isValidConfiguration(q_new, arm_name) && ...
                   obj.isValidEdge(q_near, q_new, arm_name)
                    
                    % 添加到树中
                    obj.tree_nodes = [obj.tree_nodes; q_new];
                    obj.tree_parents = [obj.tree_parents; near_idx];
                    new_cost = obj.tree_costs(near_idx) + norm(q_new - q_near);
                    obj.tree_costs = [obj.tree_costs; new_cost];
                    
                    info.nodes_explored = info.nodes_explored + 1;
                    
                    % 检查是否到达目标
                    if norm(q_new - q_goal) < obj.goal_tolerance
                        fprintf('找到路径！迭代次数: %d\n', iter);
                        success = true;
                        path = obj.extractPath(size(obj.tree_nodes, 1));
                        break;
                    end
                end
                
                % 进度显示
                if mod(iter, 1000) == 0
                    fprintf('迭代 %d/%d, 树节点数: %d\n', iter, obj.max_iterations, size(obj.tree_nodes, 1));
                end
            end
            
            if ~success
                fprintf('RRT规划失败，达到最大迭代次数\n');
            end
            
            info.final_tree_size = size(obj.tree_nodes, 1);
            info.path_length = size(path, 1);
        end
        
        function q_rand = sampleRandomConfiguration(obj, arm_name)
            % 在指定手臂的关节空间中随机采样配置
            if strcmp(arm_name, 'right')
                joint_indices = 1:7;
            elseif strcmp(arm_name, 'left')
                joint_indices = 8:14;
            else % 'both' or other cases, sample full body
                joint_indices = 1:size(obj.joint_limits, 1);
            end
            
            num_joints_to_sample = length(joint_indices);
            q_rand = zeros(1, num_joints_to_sample);
            
            for i = 1:num_joints_to_sample
                joint_idx = joint_indices(i);
                q_rand(i) = obj.joint_limits(joint_idx,1) + ...
                           (obj.joint_limits(joint_idx,2) - obj.joint_limits(joint_idx,1)) * rand;
            end
        end
        
        function [q_near, near_idx] = findNearestNode(obj, q_target)
            % 找到树中最接近目标点的节点
            distances = sqrt(sum((obj.tree_nodes - q_target).^2, 2));
            [~, near_idx] = min(distances);
            q_near = obj.tree_nodes(near_idx, :);
        end
        
        function q_new = steer(obj, q_from, q_to)
            % 从q_from向q_to方向扩展固定步长
            direction = q_to - q_from;
            distance = norm(direction);
            
            if distance <= obj.step_size
                q_new = q_to;
            else
                q_new = q_from + obj.step_size * (direction / distance);
            end
            
            % 确保在关节限制内
            q_new = obj.clipToJointLimits(q_new);
        end
        
        function q_clipped = clipToJointLimits(obj, q)
            % 将关节角限制在有效范围内
            q_clipped = q;
            for i = 1:length(q)
                q_clipped(i) = max(obj.joint_limits(i,1), ...
                                  min(obj.joint_limits(i,2), q(i)));
            end
        end
        
        function valid = isValidConfiguration(obj, q, arm_name)
            % 检查配置是否有效（无碰撞，在关节限制内）
            
            % 检查关节限制
            for i = 1:length(q)
                if q(i) < obj.joint_limits(i,1) || q(i) > obj.joint_limits(i,2)
                    valid = false;
                    return;
                end
            end
            
            % 检查碰撞
            if ~isempty(obj.collision_checker)
                % Create full q for collision checker
                q_full = obj.robot.homeConfiguration;
                if strcmp(arm_name, 'right')
                    q_full(1:7) = q;
                elseif strcmp(arm_name, 'left')
                    q_full(8:14) = q;
                else
                    q_full = q; % Assume q is already full for 'both'
                end
                valid = ~obj.collision_checker.checkCollision(q_full);
            else
                valid = true;
            end
        end
        
        function valid = isValidEdge(obj, q1, q2, arm_name)
            % 检查两个配置之间的路径是否有效
            % 使用线性插值检查中间点
            
            num_checks = ceil(norm(q2 - q1) / (obj.step_size * 0.5));
            if num_checks < 2
                num_checks = 2;
            end
            
            for i = 1:num_checks
                alpha = (i-1) / (num_checks-1);
                q_check = (1-alpha) * q1 + alpha * q2;
                
                if ~obj.isValidConfiguration(q_check, arm_name)
                    valid = false;
                    return;
                end
            end
            
            valid = true;
        end
        
        function path = extractPath(obj, goal_idx)
            % 从目标节点回溯提取路径
            path = [];
            current_idx = goal_idx;
            
            while current_idx > 0
                path = [obj.tree_nodes(current_idx, :); path];
                current_idx = obj.tree_parents(current_idx);
            end
        end
    end
end

function value = getfield_default(s, field, default_value)
    % 获取结构体字段，如果不存在则返回默认值
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end
