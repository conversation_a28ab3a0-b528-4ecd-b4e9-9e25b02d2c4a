# YuMi机器人乐高堆叠演示指南

## 🎯 系统概述

本系统实现了YuMi双臂机器人的乐高积木精确堆叠功能，包含完整的3D可视化和仿真演示。系统支持：

- **YuMi机器人3D模型显示**
- **乐高积木抓取、移动、放置的完整动画**
- **Simulink 3D仿真**
- **实时轨迹可视化**
- **双臂协调避碰**

## 🚀 快速开始

### 方法1：完整演示（推荐）

```matlab
% 运行完整的交互式演示
yumiLegoDemo()
```

这将启动一个交互式演示，包含：
- 轨迹规划方法选择
- 可视化方式选择
- 完整的3D动画演示
- 性能分析报告

### 方法2：主程序

```matlab
% 运行主程序
main
```

选择不同的模式：
- 模式1：原始轨迹规划
- 模式2：改进轨迹规划（推荐）
- 模式3：性能对比测试
- 模式4：完整系统演示

### 方法3：性能测试

```matlab
% 运行性能对比测试
testImprovedPlanner()
```

## 🎬 可视化功能

### 1. MATLAB 3D动画（重点功能！）

**特点**：
- ✅ **YuMi机器人3D模型实时显示**
- ✅ **乐高积木3D可视化**
- ✅ **抓取和放置动作动画**
- ✅ **轨迹路径实时绘制**
- ✅ **双臂协调演示**

**启动方式**：
```matlab
% 方法1：通过演示程序
yumiLegoDemo()  % 选择选项1

% 方法2：直接调用
[yumi, qHome, ~, ~] = setupRobotEnv();
brick_config = lego_config();
trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome);
animateTrajectory(yumi, qHome, trajectories, brick_config);
```

### 2. Simulink 3D仿真

**特点**：
- ✅ **YuMi机器人Simscape模型**
- ✅ **物理仿真**
- ✅ **实时控制**

**启动方式**：
```matlab
% 通过主程序选择Simulink仿真
main  % 选择可视化方式2

% 或直接调用
runSimulink(trajectories, 15);
```

## 🎮 演示流程

### 完整演示步骤

1. **系统初始化**
   - 加载YuMi机器人模型
   - 设置工作环境（桌面、工作区域）

2. **乐高配置**
   - 定义积木初始位置
   - 设置目标堆叠位置
   - 生成任务序列

3. **轨迹规划**
   - 选择规划算法（原始/改进）
   - 生成双臂协调轨迹
   - 优化轨迹平滑度

4. **3D可视化**
   - 显示YuMi机器人模型
   - 创建乐高积木3D对象
   - 执行抓取放置动画

5. **结果分析**
   - 轨迹质量评估
   - 性能统计报告

## 🔧 关键功能说明

### YuMi机器人可视化

系统使用MATLAB Robotics Toolbox显示YuMi机器人：

```matlab
% 显示机器人
show(yumi, qHome, 'PreservePlot', true, 'Frames', 'off');

% 更新机器人配置
show(yumi, new_joint_angles, 'PreservePlot', true, 'Frames', 'off');
```

### 乐高积木3D对象

每个乐高积木都是独立的3D对象：

```matlab
% 创建乐高积木
lego_obj = createSingleLego(lego_type, position, orientation, arm, index);

% 更新积木位置
updateLegoPosition(lego_obj, new_position, new_orientation);
```

### 抓取放置动画

完整的抓取放置过程包含5个阶段：

1. **移动到抓取位置**
2. **执行抓取动作**（积木变色表示抓取）
3. **移动到放置位置**（积木跟随机器人）
4. **执行放置动作**（积木放置到目标位置）
5. **退离**

### 双臂协调

系统支持三种协调模式：

- **顺序模式**：安全但效率较低
- **并行模式**：高效但需要精确避碰
- **自适应模式**：平衡安全性和效率（推荐）

## 📊 可视化元素

### 颜色编码

- **红色区域**：左手工作区域
- **蓝色区域**：右手工作区域  
- **绿色区域**：城堡建造区域
- **红色积木**：左手初始积木
- **蓝色积木**：右手初始积木
- **红色轨迹线**：左手运动轨迹
- **蓝色轨迹线**：右手运动轨迹

### 动画效果

- **积木边框变红**：正在抓取
- **积木边框变绿**：抓取成功
- **积木跟随机器人**：运输过程
- **积木边框变蓝**：放置成功
- **积木边框变黑**：任务完成

## 🎛️ 参数配置

### 动画参数

```matlab
visualization_options = struct();
visualization_options.animation_speed = 0.05;  % 动画速度
visualization_options.show_trajectory = true;  % 显示轨迹
visualization_options.show_labels = true;      % 显示标签
```

### 轨迹规划参数

```matlab
options = struct();
options.planning_mode = 'advanced';           % 规划模式
options.coordination_mode = 'adaptive';       % 协调模式
options.safety_margin = 0.08;                 % 安全距离
options.rrt_max_iterations = 3000;            % RRT迭代次数
options.max_velocity = 1.2;                   % 最大速度
options.max_acceleration = 2.5;               % 最大加速度
```

## 🔍 故障排除

### 常见问题

1. **YuMi模型加载失败**
   ```matlab
   % 检查机器人工具箱
   ver robotics
   
   % 重新加载模型
   yumi = loadrobot('abbYumi','DataFormat','row','Gravity',[0 0 -9.81]);
   ```

2. **动画显示异常**
   ```matlab
   % 关闭所有图形窗口
   close all;
   
   % 重新运行演示
   yumiLegoDemo();
   ```

3. **Simulink模型问题**
   ```matlab
   % 检查模型是否存在
   if exist('YumiSimscape.slx', 'file')
       open_system('YumiSimscape.slx');
   else
       fprintf('Simulink模型文件不存在\n');
   end
   ```

### 性能优化

1. **减少动画点数**
   ```matlab
   % 在lego_config.m中减少任务数量
   brick_config.task_sequence = brick_config.task_sequence(1:4);
   ```

2. **调整动画速度**
   ```matlab
   % 加快动画速度
   visualization_options.animation_speed = 0.02;
   ```

3. **简化可视化**
   ```matlab
   % 禁用某些视觉效果
   options.show_trajectory = false;
   options.show_labels = false;
   ```

## 📈 系统特点

### 技术亮点

- **真实YuMi机器人模型**：基于ABB YuMi的精确3D模型
- **物理仿真**：支持Simulink Simscape物理仿真
- **实时可视化**：流畅的3D动画显示
- **智能规划**：RRT路径规划 + B-spline优化
- **双臂协调**：避碰检测和任务调度

### 应用价值

- **教学演示**：直观展示机器人运动学和轨迹规划
- **算法验证**：可视化验证轨迹规划算法效果
- **系统开发**：为实际机器人系统开发提供仿真平台
- **研究工具**：支持机器人学相关研究

## 🎯 使用建议

1. **首次使用**：建议运行 `yumiLegoDemo()` 体验完整功能
2. **性能测试**：使用 `testImprovedPlanner()` 对比不同算法
3. **自定义开发**：参考 `LegoVisualization.m` 开发自定义可视化
4. **参数调优**：根据需要调整动画速度和规划参数

---

**注意**：确保MATLAB Robotics Toolbox已正确安装，并且YuMi机器人模型可用。系统在MATLAB R2020b及以上版本测试通过。
