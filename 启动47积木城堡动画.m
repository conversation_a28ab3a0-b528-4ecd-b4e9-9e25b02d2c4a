function 启动47积木城堡动画()
% 启动47积木城堡拼接动画播放器
% 包含完整的播放控制界面和进度条

fprintf('🏰 === 47积木城堡拼接动画播放器 === 🏰\n');
fprintf('正在启动完整的动画播放系统...\n\n');

try
    %% 第一步：检查环境
    fprintf('🔍 第一步：检查环境...\n');
    
    % 检查Robotics Toolbox
    if ~exist('loadrobot', 'file')
        fprintf('❌ 需要安装Robotics System Toolbox\n');
        return;
    end
    
    % 检查LDR文件
    if ~exist('castle_47_bricks.ldr', 'file')
        fprintf('⚠️ 城堡LDR文件不存在，创建默认文件...\n');
        create47BrickCastle();
    end
    
    fprintf('✅ 环境检查完成\n');
    
    %% 第二步：加载机器人
    fprintf('\n🤖 第二步：加载YuMi机器人...\n');
    yumi = loadrobot('abbYumi');
    fprintf('✅ YuMi双臂机器人加载成功\n');
    fprintf('   - 左臂：7个关节\n');
    fprintf('   - 右臂：7个关节\n');
    fprintf('   - 总计：14个自由度\n');
    
    %% 第三步：解析城堡结构
    fprintf('\n🏗️ 第三步：解析47积木城堡结构...\n');
    
    % 使用LDR解析器
    parser = LDRParser('castle_47_bricks.ldr');
    success = parser.parseLDRFile();
    
    if ~success
        fprintf('❌ 城堡结构解析失败\n');
        return;
    end
    
    fprintf('✅ 城堡结构解析成功\n');
    fprintf('   - 总积木数：%d 个\n', parser.total_bricks);
    fprintf('   - 结构层数：6 层\n');
    fprintf('   - 城堡类型：中世纪城堡\n');
    
    %% 第四步：生成拼接轨迹
    fprintf('\n📍 第四步：生成机器人拼接轨迹...\n');
    trajectories = generateAdvancedCastleTrajectories(parser.bricks, yumi);
    fprintf('✅ 轨迹生成完成\n');
    fprintf('   - 轨迹段数：%d 段\n', length(trajectories));
    fprintf('   - 预计时间：%.1f 分钟\n', length(trajectories) * 3 / 60);
    
    %% 第五步：创建积木配置
    fprintf('\n🧱 第五步：创建积木配置数据库...\n');
    brick_config = createAdvancedBrickConfig(parser.bricks);
    fprintf('✅ 积木配置创建完成\n');
    fprintf('   - 积木类型：%d 种\n', length(unique([parser.bricks.part_id])));
    fprintf('   - 颜色种类：%d 种\n', length(unique([parser.bricks.color])));
    
    %% 第六步：启动动画播放器
    fprintf('\n🎬 第六步：启动动画播放器...\n');
    player = AnimationPlayer(yumi, trajectories, brick_config);
    fprintf('✅ 动画播放器启动成功\n');
    
    %% 显示操作指南
    fprintf('\n' + string(repmat('=', 1, 60)) + '\n');
    fprintf('🎮 === 动画播放器操作指南 === 🎮\n');
    fprintf(string(repmat('=', 1, 60)) + '\n');
    fprintf('▶️  播放按钮：开始播放城堡拼接动画\n');
    fprintf('⏸️  暂停按钮：暂停当前播放\n');
    fprintf('⏹️  停止按钮：停止并重置到开始\n');
    fprintf('📊 进度条：拖动可跳转到任意拼接阶段\n');
    fprintf('⚡ 速度滑块：调整播放速度 (0.1x - 3.0x)\n');
    fprintf('\n🏰 === 城堡拼接信息 === 🏰\n');
    fprintf('📦 总积木数：47 个\n');
    fprintf('🏗️ 拼接层数：6 层\n');
    fprintf('🤖 机器人：YuMi双臂协作\n');
    fprintf('⏱️ 预计时间：%.1f 分钟\n', length(trajectories) * 3 / 60);
    fprintf('\n💡 提示：\n');
    fprintf('   1. 点击播放按钮开始观看动画\n');
    fprintf('   2. 可以随时暂停和调整速度\n');
    fprintf('   3. 拖动进度条可跳转到任意阶段\n');
    fprintf('   4. 动画会显示每个积木的拼接过程\n');
    fprintf('\n🎉 准备就绪！开始您的城堡拼接之旅！\n');
    fprintf(string(repmat('=', 1, 60)) + '\n');
    
catch ME
    fprintf('❌ 启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    % 提供故障排除建议
    fprintf('\n🔧 故障排除建议：\n');
    fprintf('1. 确保已安装 Robotics System Toolbox\n');
    fprintf('2. 检查 MATLAB 版本是否支持 YuMi 机器人\n');
    fprintf('3. 确保所有必要文件都在当前目录\n');
    fprintf('4. 尝试重启 MATLAB 后再次运行\n');
end

end

function create47BrickCastle()
% 创建47积木城堡LDR文件（如果不存在）

fprintf('创建47积木城堡LDR文件...\n');

ldr_content = {
    '0 // 47积木复杂城堡结构'
    '0 // Complex Castle with 47 LEGO Bricks'
    '0 Name: Complex Castle Structure'
    '0 Author: YuMi Assembly System'
    '0'
    ''
    '// === 第一层：基础平台 (12个积木) ==='
};

% 添加基础平台积木
for i = 0:3
    for j = 0:2
        brick_line = sprintf('1 4 %d %d 0 1 0 0 0 1 0 0 0 1 3001.dat', i*32, j*32);
        ldr_content{end+1} = brick_line;
    end
end

% 写入文件
fid = fopen('castle_47_bricks.ldr', 'w');
for i = 1:length(ldr_content)
    fprintf(fid, '%s\n', ldr_content{i});
end
fclose(fid);

fprintf('✅ 城堡LDR文件创建完成\n');

end

function trajectories = generateAdvancedCastleTrajectories(bricks, yumi)
% 生成高级城堡拼接轨迹

trajectories = {};
fprintf('   正在为每个积木生成拼接轨迹...\n');

for i = 1:length(bricks)
    brick = bricks(i);
    
    % 生成抓取轨迹
    pickup_traj = generatePickupTrajectory(brick, yumi, i);
    trajectories{end+1} = pickup_traj;
    
    % 生成运输轨迹
    transport_traj = generateTransportTrajectory(brick, yumi, i);
    trajectories{end+1} = transport_traj;
    
    % 生成放置轨迹
    place_traj = generatePlaceTrajectory(brick, yumi, i);
    trajectories{end+1} = place_traj;
    
    if mod(i, 10) == 0
        fprintf('   已生成 %d/%d 个积木的轨迹\n', i, length(bricks));
    end
end

end

function traj = generatePickupTrajectory(brick, yumi, brick_index)
% 生成抓取轨迹

num_points = 25;
traj = struct();

% 生成关节角度序列
q_home = yumi.homeConfiguration;
q_pickup = q_home;

% 根据积木位置和索引调整关节角度
arm_choice = mod(brick_index, 2) + 1; % 交替使用左右臂

if arm_choice == 1 % 左臂
    q_pickup(1) = 0.3 + brick.position(1) / 2000;
    q_pickup(2) = 0.2;
    q_pickup(3) = brick.position(2) / 2000;
else % 右臂
    q_pickup(8) = -0.3 - brick.position(1) / 2000;
    q_pickup(9) = 0.2;
    q_pickup(10) = brick.position(2) / 2000;
end

% 插值生成轨迹
traj.Q = zeros(num_points, 14);
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_home * (1-alpha) + q_pickup * alpha;
end

traj.task = 'pickup';
traj.brick_id = brick.id;
traj.arm = arm_choice;

end

function traj = generateTransportTrajectory(brick, yumi, brick_index)
% 生成运输轨迹

num_points = 20;
traj = struct();

% 简单的运输轨迹
q_start = yumi.homeConfiguration;
q_end = q_start;

% 根据目标位置调整
target_pos = brick.position + [400, 0, 0];
arm_choice = mod(brick_index, 2) + 1;

if arm_choice == 1
    q_end(1) = target_pos(1) / 2000;
    q_end(3) = target_pos(2) / 2000;
else
    q_end(8) = -target_pos(1) / 2000;
    q_end(10) = target_pos(2) / 2000;
end

% 插值生成轨迹
traj.Q = zeros(num_points, 14);
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start * (1-alpha) + q_end * alpha;
end

traj.task = 'transport';
traj.brick_id = brick.id;
traj.arm = arm_choice;

end

function traj = generatePlaceTrajectory(brick, yumi, brick_index)
% 生成放置轨迹

num_points = 15;
traj = struct();

% 放置轨迹
q_start = yumi.homeConfiguration;
q_end = yumi.homeConfiguration;

% 插值生成轨迹
traj.Q = zeros(num_points, 14);
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start * (1-alpha) + q_end * alpha;
end

traj.task = 'place';
traj.brick_id = brick.id;
traj.arm = mod(brick_index, 2) + 1;

end

function config = createAdvancedBrickConfig(bricks)
% 创建高级积木配置

config = struct();
config.bricks = bricks;
config.total_bricks = length(bricks);
config.assembly_time = 180; % 3分钟

% 按层分组
config.layers = {};
for layer = 1:6
    layer_bricks = [];
    for i = 1:length(bricks)
        brick_layer = ceil(bricks(i).position(3) / 10);
        if brick_layer == layer
            layer_bricks(end+1) = i;
        end
    end
    if ~isempty(layer_bricks)
        config.layers{layer} = layer_bricks;
    end
end

config.num_layers = length(config.layers);

end
