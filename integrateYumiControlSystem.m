function integrateYumiControlSystem()
% 集成YuMi机器人控制系统
% 包括轨迹跟踪、力/位混合控制、双臂协调控制

fprintf('🎮 === 集成YuMi控制系统 === 🎮\n');

modelName = 'AdvancedYumiSimscape';

% 检查模型是否存在
if ~bdIsLoaded(modelName)
    fprintf('❌ 模型 %s 未加载\n', modelName);
    return;
end

% === 1. 创建轨迹跟踪控制器 ===
fprintf('步骤1: 创建轨迹跟踪控制器...\n');

% 右臂轨迹跟踪控制器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/RightArmController'], ...
          'Position', [200, 550, 300, 600]);

% 设置右臂控制器代码
rightControllerCode = sprintf(['function [torque, gripper_cmd] = fcn(q_ref, q_actual, qd_ref, qd_actual)\n'...
    '%% YuMi右臂轨迹跟踪控制器\n'...
    '%% 输入: q_ref(7x1), q_actual(7x1), qd_ref(7x1), qd_actual(7x1)\n'...
    '%% 输出: torque(7x1), gripper_cmd(1x1)\n\n'...
    '%% PID控制参数\n'...
    'Kp = diag([100, 100, 80, 80, 60, 60, 40]);  %% 位置增益\n'...
    'Kd = diag([10, 10, 8, 8, 6, 6, 4]);         %% 速度增益\n\n'...
    '%% 计算误差\n'...
    'e_pos = q_ref - q_actual;\n'...
    'e_vel = qd_ref - qd_actual;\n\n'...
    '%% PID控制律\n'...
    'torque = Kp * e_pos + Kd * e_vel;\n\n'...
    '%% 夹爪控制 (简化)\n'...
    'gripper_cmd = 0.01;  %% 10mm开口\n']);

set_param([modelName '/RightArmController'], 'Script', rightControllerCode);

% 左臂轨迹跟踪控制器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LeftArmController'], ...
          'Position', [200, 650, 300, 700]);

% 设置左臂控制器代码
leftControllerCode = sprintf(['function [torque, gripper_cmd] = fcn(q_ref, q_actual, qd_ref, qd_actual)\n'...
    '%% YuMi左臂轨迹跟踪控制器\n'...
    'Kp = diag([100, 100, 80, 80, 60, 60, 40]);\n'...
    'Kd = diag([10, 10, 8, 8, 6, 6, 4]);\n'...
    'e_pos = q_ref - q_actual;\n'...
    'e_vel = qd_ref - qd_actual;\n'...
    'torque = Kp * e_pos + Kd * e_vel;\n'...
    'gripper_cmd = 0.01;\n']);

set_param([modelName '/LeftArmController'], 'Script', leftControllerCode);

fprintf('  ✅ 右臂PID控制器创建完成\n');
fprintf('  ✅ 左臂PID控制器创建完成\n');

% === 2. 创建力/位混合控制器 ===
fprintf('步骤2: 创建力/位混合控制器...\n');

% 力控制器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/ForceController'], ...
          'Position', [400, 550, 500, 600]);

% 设置力控制器代码
forceControllerCode = sprintf(['function [force_cmd] = fcn(force_ref, force_actual, contact_detected)\n'...
    '%% 力/位混合控制器\n'...
    '%% 当检测到接触时切换到力控制模式\n\n'...
    'if contact_detected > 0.5\n'...
    '    %% 力控制模式\n'...
    '    Kf = 0.1;  %% 力控制增益\n'...
    '    force_error = force_ref - force_actual;\n'...
    '    force_cmd = Kf * force_error;\n'...
    'else\n'...
    '    %% 位置控制模式\n'...
    '    force_cmd = 0;\n'...
    'end\n']);

set_param([modelName '/ForceController'], 'Script', forceControllerCode);

fprintf('  ✅ 力/位混合控制器创建完成\n');

% === 3. 创建双臂协调控制器 ===
fprintf('步骤3: 创建双臂协调控制器...\n');

% 协调控制器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/CoordinationController'], ...
          'Position', [600, 550, 700, 650]);

% 设置协调控制器代码
coordControllerCode = sprintf(['function [right_cmd, left_cmd, sync_error] = fcn(right_state, left_state, task_mode)\n'...
    '%% 双臂协调控制器\n'...
    '%% 输入: right_state(7x1), left_state(7x1), task_mode(1x1)\n'...
    '%% 输出: right_cmd(7x1), left_cmd(7x1), sync_error(1x1)\n\n'...
    'if task_mode == 1\n'...
    '    %% 协调模式: 双臂同步运动\n'...
    '    sync_error = norm(right_state - left_state);\n'...
    '    \n'...
    '    %% 同步控制律\n'...
    '    Ks = 10;  %% 同步增益\n'...
    '    sync_correction = Ks * (right_state - left_state);\n'...
    '    \n'...
    '    right_cmd = right_state - 0.5 * sync_correction;\n'...
    '    left_cmd = left_state + 0.5 * sync_correction;\n'...
    'else\n'...
    '    %% 独立模式: 双臂独立运动\n'...
    '    right_cmd = right_state;\n'...
    '    left_cmd = left_state;\n'...
    '    sync_error = 0;\n'...
    'end\n']);

set_param([modelName '/CoordinationController'], 'Script', coordControllerCode);

fprintf('  ✅ 双臂协调控制器创建完成\n');

% === 4. 创建轨迹生成器 ===
fprintf('步骤4: 创建轨迹生成器...\n');

% 轨迹生成器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/TrajectoryGenerator'], ...
          'Position', [50, 750, 150, 800]);

% 设置轨迹生成器代码
trajGeneratorCode = sprintf(['function [q_ref, qd_ref, task_phase] = fcn(t)\n'...
    '%% 轨迹生成器\n'...
    '%% 生成乐高堆叠任务轨迹\n\n'...
    'persistent traj_data;\n'...
    'if isempty(traj_data)\n'...
    '    %% 初始化轨迹数据\n'...
    '    traj_data = generateLegoStackingTrajectory();\n'...
    'end\n\n'...
    '%% 根据时间插值轨迹\n'...
    '[q_ref, qd_ref, task_phase] = interpolateTrajectory(traj_data, t);\n\n'...
    'function traj = generateLegoStackingTrajectory()\n'...
    '    %% 生成乐高堆叠轨迹\n'...
    '    traj.time = [0, 2, 4, 6, 8, 10];\n'...
    '    traj.q = [\n'...
    '        0, 0, 0, 0, 0, 0, 0;      %% 初始位置\n'...
    '        0.2, -0.3, 0.1, -0.5, 0, 0.2, 0;  %% 抓取准备\n'...
    '        0.3, -0.5, 0.2, -0.8, 0.1, 0.3, 0; %% 抓取位置\n'...
    '        0.2, -0.3, 0.1, -0.5, 0, 0.2, 0;  %% 提升\n'...
    '        -0.1, 0.2, -0.1, 0.3, -0.1, -0.2, 0; %% 运输\n'...
    '        0, 0, 0, 0, 0, 0, 0;      %% 放置完成\n'...
    '    ];\n'...
    'end\n\n'...
    'function [q, qd, phase] = interpolateTrajectory(traj, t)\n'...
    '    %% 轨迹插值\n'...
    '    if t <= traj.time(1)\n'...
    '        q = traj.q(1, :)'';\n'...
    '        qd = zeros(7, 1);\n'...
    '        phase = 1;\n'...
    '    elseif t >= traj.time(end)\n'...
    '        q = traj.q(end, :)'';\n'...
    '        qd = zeros(7, 1);\n'...
    '        phase = 6;\n'...
    '    else\n'...
    '        %% 线性插值\n'...
    '        idx = find(traj.time <= t, 1, ''last'');\n'...
    '        t1 = traj.time(idx);\n'...
    '        t2 = traj.time(idx + 1);\n'...
    '        alpha = (t - t1) / (t2 - t1);\n'...
    '        \n'...
    '        q = ((1 - alpha) * traj.q(idx, :) + alpha * traj.q(idx + 1, :))'';\n'...
    '        qd = (traj.q(idx + 1, :) - traj.q(idx, :))'' / (t2 - t1);\n'...
    '        phase = idx;\n'...
    '    end\n'...
    'end\n']);

set_param([modelName '/TrajectoryGenerator'], 'Script', trajGeneratorCode);

fprintf('  ✅ 轨迹生成器创建完成\n');

% === 5. 添加信号路由和接口 ===
fprintf('步骤5: 添加信号路由和接口...\n');

% 信号复用器
add_block('simulink/Signal Routing/Mux', [modelName '/RightStateMux'], ...
          'Position', [800, 450, 850, 500]);
add_block('simulink/Signal Routing/Mux', [modelName '/LeftStateMux'], ...
          'Position', [800, 550, 850, 600]);

% 信号分离器
add_block('simulink/Signal Routing/Demux', [modelName '/RightCmdDemux'], ...
          'Position', [350, 450, 400, 500]);
add_block('simulink/Signal Routing/Demux', [modelName '/LeftCmdDemux'], ...
          'Position', [350, 550, 400, 600]);

% 显示器
add_block('simulink/Sinks/Display', [modelName '/SyncErrorDisplay'], ...
          'Position', [750, 650, 800, 680]);

fprintf('  ✅ 信号路由组件创建完成\n');

% === 6. 添加数据记录 ===
fprintf('步骤6: 添加数据记录...\n');

% 数据记录器
add_block('simulink/Sinks/To Workspace', [modelName '/JointDataLogger'], ...
          'Position', [900, 450, 950, 480]);
set_param([modelName '/JointDataLogger'], 'VariableName', 'joint_data');
set_param([modelName '/JointDataLogger'], 'SaveFormat', 'Timeseries');

add_block('simulink/Sinks/To Workspace', [modelName '/ForceDataLogger'], ...
          'Position', [900, 500, 950, 530]);
set_param([modelName '/ForceDataLogger'], 'VariableName', 'force_data');
set_param([modelName '/ForceDataLogger'], 'SaveFormat', 'Timeseries');

fprintf('  ✅ 数据记录器创建完成\n');

% === 7. 保存模型 ===
save_system(modelName);

fprintf('\n🎉 === YuMi控制系统集成完成 === 🎉\n');
fprintf('控制系统组件:\n');
fprintf('  ✅ 双臂PID轨迹跟踪控制器\n');
fprintf('  ✅ 力/位混合控制器\n');
fprintf('  ✅ 双臂协调控制器\n');
fprintf('  ✅ 乐高堆叠轨迹生成器\n');
fprintf('  ✅ 信号路由和接口\n');
fprintf('  ✅ 数据记录系统\n');

fprintf('\n控制特性:\n');
fprintf('  🎯 7自由度关节位置控制\n');
fprintf('  🤏 夹爪力控制\n');
fprintf('  🤝 双臂协调同步\n');
fprintf('  📊 实时数据记录\n');
fprintf('  🧱 乐高堆叠任务优化\n');

end
