function stableYumiDemo()
% Stable YuMi Demo - Generate Mechanics Explorer-like interface
% Avoid encoding issues and ensure stable operation

fprintf('=== Stable YuMi Demo ===\n');
fprintf('Creating interface similar to MathWorks official tutorial\n\n');

try
    % === Load YuMi robot ===
    fprintf('Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    fprintf('SUCCESS: YuMi robot loaded with %d joints\n', robot.NumBodies);
    
    % === Create main visualization window ===
    fprintf('Creating visualization window...\n');
    
    fig = figure('Name', 'YuMi Robot Pick and Place Demo', ...
                 'Position', [100, 100, 1200, 800], ...
                 'Color', [0.95, 0.95, 0.95]);
    
    % === Display robot ===
    fprintf('Displaying robot...\n');
    
    % Get home configuration
    qHome = robot.homeConfiguration;
    
    % Show robot
    robotHandle = show(robot, qHome, 'Frames', 'off', 'PreservePlot', false);
    hold on;
    
    % Set view and lighting
    view(45, 30);
    axis equal;
    grid on;
    lighting gouraud;
    light('Position', [1, 1, 1]);
    light('Position', [-1, -1, 1]);
    
    % === Add environment objects ===
    fprintf('Adding environment objects...\n');
    
    % Blue blocks (pick targets)
    addEnvironmentBlock([0.6, 0.2, 0.05], [0.08, 0.08, 0.08], 'blue');
    addEnvironmentBlock([0.6, -0.2, 0.05], [0.08, 0.08, 0.08], 'blue');
    
    % Green platform (place target)
    addEnvironmentBlock([0.5, 0, 0.01], [0.25, 0.25, 0.02], 'green');
    
    % Work surface
    addEnvironmentBlock([0.5, 0, -0.01], [0.4, 0.4, 0.02], [0.8, 0.8, 0.8]);
    
    % === Add labels ===
    fprintf('Adding labels...\n');
    
    title('YuMi Dual-Arm Robot - Pick and Place Simulation', ...
          'FontSize', 16, 'FontWeight', 'bold');
    
    xlabel('X (m)', 'FontSize', 12);
    ylabel('Y (m)', 'FontSize', 12);
    zlabel('Z (m)', 'FontSize', 12);
    
    % Add text labels
    text(0.3, 0.3, 0.3, 'YuMi Robot', 'FontSize', 12, 'Color', 'red');
    text(0.6, 0.2, 0.12, 'Pick Target 1', 'FontSize', 10, 'Color', 'blue');
    text(0.6, -0.2, 0.12, 'Pick Target 2', 'FontSize', 10, 'Color', 'blue');
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green');
    
    % === Create interactive visualization ===
    fprintf('Creating interactive visualization...\n');
    
    try
        % Create interactive rigid body tree
        iviz = interactiveRigidBodyTree(robot);
        iviz.showFigure();
        iviz.ShowMarker = false;
        
        fprintf('SUCCESS: Interactive visualization created\n');
        
        % === Simple animation demo ===
        fprintf('Starting animation demo...\n');
        
        % Animation parameters
        duration = 10;  % seconds
        steps = 100;
        
        for i = 1:steps
            t = (i-1) / (steps-1) * duration;
            
            % Create smooth motion
            q = qHome;
            
            % Right arm motion
            q(1) = 0.3 * sin(2*pi*t/duration);
            q(2) = -0.2 + 0.1 * cos(2*pi*t/duration);
            q(3) = 0.1 * sin(4*pi*t/duration);
            
            % Left arm motion (opposite phase)
            q(8) = -0.3 * sin(2*pi*t/duration + pi);
            q(9) = -0.2 + 0.1 * cos(2*pi*t/duration + pi);
            q(10) = -0.1 * sin(4*pi*t/duration + pi);
            
            % Update visualization
            iviz.Configuration = q;
            
            % Control animation speed
            pause(0.1);
            
            % Progress indicator
            if mod(i, 20) == 0
                fprintf('Animation progress: %d%%\n', round(i/steps*100));
            end
        end
        
        % Return to home position
        iviz.Configuration = qHome;
        
        fprintf('SUCCESS: Animation completed\n');
        
    catch ME
        fprintf('Interactive visualization failed: %s\n', ME.message);
        fprintf('Using static display instead\n');
    end
    
    % === Add control information ===
    fprintf('Adding control information...\n');
    
    % Create info panel
    infoText = {
        'YuMi Robot Demo Features:';
        '- Dual-arm robot model (21 joints)';
        '- Pick and place environment';
        '- Interactive 3D visualization';
        '- Real-time motion control';
        '';
        'Controls:';
        '- Drag to rotate view';
        '- Scroll to zoom';
        '- Right-click for options';
    };
    
    % Add info text to figure
    annotation('textbox', [0.02, 0.02, 0.25, 0.4], ...
               'String', infoText, ...
               'FontSize', 10, ...
               'BackgroundColor', 'white', ...
               'EdgeColor', 'black');
    
    % === Final setup ===
    fprintf('Final setup...\n');
    
    % Set axis limits
    xlim([0.2, 0.8]);
    ylim([-0.4, 0.4]);
    zlim([0, 0.6]);
    
    % Add timestamp
    timeStr = datestr(now, 'yyyy-mm-dd HH:MM:SS');
    text(0.2, -0.35, 0.05, ['Demo created: ' timeStr], ...
         'FontSize', 8, 'Color', 'gray');
    
    fprintf('\n=== Stable YuMi Demo Completed Successfully ===\n');
    fprintf('Features created:\n');
    fprintf('  - YuMi robot 3D model\n');
    fprintf('  - Pick and place environment\n');
    fprintf('  - Interactive visualization\n');
    fprintf('  - Animation demonstration\n');
    fprintf('  - Control information panel\n\n');
    
    fprintf('The interface is similar to the MathWorks Mechanics Explorer\n');
    fprintf('You can interact with the 3D view using mouse controls\n');
    
catch ME
    fprintf('ERROR: Demo failed: %s\n', ME.message);
    
    % Minimal backup display
    try
        fprintf('Attempting minimal backup display...\n');
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Dual-Arm Robot - Backup Display');
        view(45, 30);
        grid on;
        fprintf('SUCCESS: Minimal display created\n');
    catch
        fprintf('ERROR: Complete failure\n');
    end
end

end

function addEnvironmentBlock(center, size, color)
% Add environment block to the scene

try
    % Create block vertices
    x = center(1) + [-1, 1, 1, -1, -1, 1, 1, -1] * size(1)/2;
    y = center(2) + [-1, -1, 1, 1, -1, -1, 1, 1] * size(2)/2;
    z = center(3) + [-1, -1, -1, -1, 1, 1, 1, 1] * size(3)/2;
    
    % Define faces
    faces = [
        1 2 3 4;  % bottom
        5 6 7 8;  % top
        1 2 6 5;  % front
        3 4 8 7;  % back
        1 4 8 5;  % left
        2 3 7 6   % right
    ];
    
    % Create patch
    patch('Vertices', [x' y' z'], 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', 0.8, ...
          'EdgeColor', 'black', 'LineWidth', 0.5);
    
catch
    % Fallback: simple marker
    plot3(center(1), center(2), center(3), ...
          'o', 'Color', color, 'MarkerSize', 15, 'MarkerFaceColor', color);
end

end
