classdef RealYumiLegoAssembly < handle
    % 真正的YuMi乐高拼接系统
    % 使用YumiSimscape.slx模型和mainbu.ldr文件
    
    properties
        % 核心组件
        yumi_robot           % YuMi机器人模型
        simulink_model       % Simulink模型句柄
        ldr_parser          % LDR文件解析器
        
        % 建筑数据
        target_structure    % 目标建筑结构
        assembly_sequence   % 拼接序列
        
        % 可视化
        main_figure         % 主窗口
        axes_3d            % 3D坐标轴
        progress_bar       % 进度条
        
        % 机器人状态
        current_config     % 当前关节配置
        gripper_states     % 夹爪状态
        
        % 动画控制
        is_playing         % 播放状态
        current_step       % 当前步骤
        total_steps        % 总步骤数
    end
    
    methods
        function obj = RealYumiLegoAssembly()
            % 构造函数
            fprintf('🏗️ === 初始化真正的YuMi乐高拼接系统 === 🏗️\n');
            
            % 初始化组件
            obj.initializeComponents();
            
            % 加载模型文件
            obj.loadModels();
            
            % 解析建筑设计
            obj.parseTargetStructure();
            
            % 创建界面
            obj.createInterface();
            
            fprintf('✅ YuMi乐高拼接系统初始化完成\n');
        end
        
        function initializeComponents(obj)
            % 初始化核心组件
            fprintf('初始化核心组件...\n');
            
            try
                % 加载YuMi机器人
                obj.yumi_robot = loadrobot('abbYumi');
                obj.current_config = obj.yumi_robot.homeConfiguration;
                
                % 初始化夹爪状态
                obj.gripper_states = struct();
                obj.gripper_states.left = struct('position', 0, 'is_open', true);
                obj.gripper_states.right = struct('position', 0, 'is_open', true);
                
                fprintf('✅ YuMi机器人加载成功\n');
                
            catch ME
                fprintf('❌ 机器人加载失败: %s\n', ME.message);
                error('无法加载YuMi机器人模型');
            end
        end
        
        function loadModels(obj)
            % 加载Simulink模型和LDR文件
            fprintf('加载模型文件...\n');
            
            % 检查YumiSimscape.slx
            if exist('YumiSimscape.slx', 'file')
                try
                    % 加载Simulink模型
                    if bdIsLoaded('YumiSimscape')
                        close_system('YumiSimscape', 0);
                    end
                    
                    load_system('YumiSimscape');
                    obj.simulink_model = 'YumiSimscape';
                    fprintf('✅ YumiSimscape.slx 模型加载成功\n');
                    
                catch ME
                    fprintf('⚠️ Simulink模型加载失败: %s\n', ME.message);
                    obj.simulink_model = [];
                end
            else
                fprintf('⚠️ YumiSimscape.slx 文件不存在\n');
                obj.simulink_model = [];
            end
            
            % 检查mainbu.ldr
            if exist('mainbu.ldr', 'file')
                try
                    obj.ldr_parser = LDRParser('mainbu.ldr');
                    success = obj.ldr_parser.parseLDRFile();
                    
                    if success
                        fprintf('✅ mainbu.ldr 文件解析成功\n');
                        fprintf('   - 积木数量: %d 个\n', obj.ldr_parser.total_bricks);
                    else
                        error('LDR文件解析失败');
                    end
                    
                catch ME
                    fprintf('❌ LDR文件解析失败: %s\n', ME.message);
                    error('无法解析mainbu.ldr文件');
                end
            else
                fprintf('❌ mainbu.ldr 文件不存在\n');
                error('找不到mainbu.ldr文件');
            end
        end
        
        function parseTargetStructure(obj)
            % 解析目标建筑结构
            fprintf('解析目标建筑结构...\n');
            
            if isempty(obj.ldr_parser)
                error('LDR解析器未初始化');
            end
            
            % 获取积木数据
            obj.target_structure = obj.ldr_parser.bricks;
            
            % 生成拼接序列
            obj.generateAssemblySequence();
            
            fprintf('✅ 目标结构解析完成\n');
            fprintf('   - 建筑名称: %s\n', 'Main Building');
            fprintf('   - 积木总数: %d 个\n', length(obj.target_structure));
            fprintf('   - 拼接步骤: %d 步\n', obj.total_steps);
        end
        
        function generateAssemblySequence(obj)
            % 生成拼接序列
            fprintf('生成拼接序列...\n');

            bricks = obj.target_structure;
            sequence = struct('step_id', {}, 'brick_id', {}, 'brick', {}, 'arm', {}, 'action', {}, 'pick_position', {}, 'place_position', {});

            % 按Z坐标排序（从下到上）
            z_coords = zeros(length(bricks), 1);
            for i = 1:length(bricks)
                % 确保position是数值数组
                if isstruct(bricks(i).position)
                    pos = [bricks(i).position.x, bricks(i).position.y, bricks(i).position.z];
                else
                    pos = bricks(i).position;
                end
                z_coords(i) = pos(3);
            end

            [~, sort_idx] = sort(z_coords);

            % 为每个积木分配手臂
            for i = 1:length(sort_idx)
                brick_idx = sort_idx(i);
                brick = bricks(brick_idx);

                % 确保position是数值数组
                if isstruct(brick.position)
                    brick_pos = [brick.position.x, brick.position.y, brick.position.z];
                else
                    brick_pos = brick.position;
                end

                % 根据位置分配手臂
                if brick_pos(2) < 0  % Y坐标负值用右臂
                    arm = 'right';
                else  % Y坐标正值用左臂
                    arm = 'left';
                end

                step = struct();
                step.step_id = i;
                step.brick_id = brick.id;
                step.brick = brick;
                step.arm = arm;
                step.action = 'pick_and_place';
                step.pick_position = [0, 0, 100];  % 积木供应位置
                step.place_position = brick_pos;  % 使用转换后的数值位置

                sequence(end+1) = step;
            end

            obj.assembly_sequence = sequence;
            obj.total_steps = length(sequence);
            obj.current_step = 1;

            fprintf('✅ 拼接序列生成完成: %d 步\n', obj.total_steps);
        end
        
        function createInterface(obj)
            % 创建用户界面
            fprintf('创建用户界面...\n');
            
            % 创建主窗口
            obj.main_figure = figure('Name', '🤖 YuMi乐高拼接系统 - 真实模型', ...
                                     'Position', [100, 100, 1400, 900], ...
                                     'Color', [0.1, 0.1, 0.1], ...
                                     'CloseRequestFcn', @(~,~) obj.closeSystem());
            
            % 创建3D显示区域
            obj.axes_3d = axes('Parent', obj.main_figure, ...
                               'Position', [0.05, 0.3, 0.9, 0.65], ...
                               'Color', [0.05, 0.05, 0.05]);
            
            % 创建控制面板
            obj.createControlPanel();
            
            % 初始化3D场景
            obj.initialize3DScene();
            
            fprintf('✅ 用户界面创建完成\n');
        end
        
        function createControlPanel(obj)
            % 创建控制面板
            control_panel = uipanel('Parent', obj.main_figure, ...
                                    'Position', [0.05, 0.02, 0.9, 0.25], ...
                                    'BackgroundColor', [0.2, 0.2, 0.2], ...
                                    'Title', '控制面板', ...
                                    'ForegroundColor', 'white', ...
                                    'FontSize', 12);
            
            % 播放控制按钮
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '▶️ 开始拼接', ...
                      'Position', [20, 150, 120, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.startAssembly());
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏸️ 暂停', ...
                      'Position', [150, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.pauseAssembly());
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏹️ 停止', ...
                      'Position', [240, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.stopAssembly());
            
            % 进度显示
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', '拼接进度:', ...
                      'Position', [20, 110, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10);
            
            obj.progress_bar = uicontrol('Parent', control_panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 110, 400, 20], ...
                                         'Min', 1, ...
                                         'Max', obj.total_steps, ...
                                         'Value', 1, ...
                                         'Callback', @(src,~) obj.seekToStep(round(src.Value)));
            
            % 状态信息
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', sprintf('步骤: 1/%d | 积木: %d个 | 使用: YumiSimscape.slx + mainbu.ldr', ...
                                        obj.total_steps, length(obj.target_structure)), ...
                      'Position', [20, 70, 600, 30], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10, ...
                      'HorizontalAlignment', 'left', ...
                      'Tag', 'status_text');
            
            % 夹爪控制
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', '夹爪控制:', ...
                      'Position', [20, 30, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10);
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪开', ...
                      'Position', [110, 30, 60, 25], ...
                      'FontSize', 9, ...
                      'Callback', @(~,~) obj.controlGripper('left', 'open'));
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪关', ...
                      'Position', [175, 30, 60, 25], ...
                      'FontSize', 9, ...
                      'Callback', @(~,~) obj.controlGripper('left', 'close'));
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪开', ...
                      'Position', [245, 30, 60, 25], ...
                      'FontSize', 9, ...
                      'Callback', @(~,~) obj.controlGripper('right', 'open'));
            
            uicontrol('Parent', control_panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪关', ...
                      'Position', [310, 30, 60, 25], ...
                      'FontSize', 9, ...
                      'Callback', @(~,~) obj.controlGripper('right', 'close'));
        end
        
        function initialize3DScene(obj)
            % 初始化3D场景
            axes(obj.axes_3d);
            hold on;
            
            % 设置坐标轴
            xlabel('X (mm)', 'Color', 'white');
            ylabel('Y (mm)', 'Color', 'white');
            zlabel('Z (mm)', 'Color', 'white');
            title('🤖 YuMi双臂机器人乐高拼接 - 真实模型', 'Color', 'white', 'FontSize', 14);
            
            % 设置视角
            view(45, 30);
            axis equal;
            grid on;
            
            % 设置坐标轴范围（根据mainbu.ldr的实际范围）
            xlim([-200, 400]);
            ylim([-300, 100]);
            zlim([-200, 100]);
            
            % 添加地面
            [X, Y] = meshgrid(-200:50:400, -300:50:100);
            Z = -200 * ones(size(X));
            surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
            
            % 显示YuMi机器人
            obj.displayYumiRobot();
            
            % 显示目标建筑轮廓
            obj.displayTargetStructure();
            
            fprintf('✅ 3D场景初始化完成\n');
        end
        
        function displayYumiRobot(obj)
            % 显示YuMi机器人
            try
                % 显示完整的YuMi机器人模型
                show(obj.yumi_robot, obj.current_config, ...
                     'Parent', obj.axes_3d, ...
                     'PreservePlot', true, ...
                     'Frames', 'off', ...
                     'Visuals', 'on');
                
                fprintf('✅ YuMi机器人显示成功\n');
                
            catch ME
                fprintf('⚠️ YuMi机器人显示失败: %s\n', ME.message);
                
                % 显示简化的机器人表示
                plot3(0, 0, 0, 'ro', 'MarkerSize', 15, 'MarkerFaceColor', 'red');
                text(0, 0, 50, 'YuMi Base', 'Color', 'white', 'FontSize', 12);
                
                % 显示左右臂位置
                plot3(-100, 0, 200, 'bo', 'MarkerSize', 10, 'MarkerFaceColor', 'blue');
                text(-100, 0, 220, 'Left Arm', 'Color', 'white', 'FontSize', 10);
                
                plot3(100, 0, 200, 'go', 'MarkerSize', 10, 'MarkerFaceColor', 'green');
                text(100, 0, 220, 'Right Arm', 'Color', 'white', 'FontSize', 10);
            end
        end
        
        function displayTargetStructure(obj)
            % 显示目标建筑结构轮廓
            fprintf('显示目标建筑结构...\n');

            for i = 1:length(obj.target_structure)
                brick = obj.target_structure(i);

                % 确保position是数值数组
                if isstruct(brick.position)
                    brick_pos = [brick.position.x, brick.position.y, brick.position.z];
                else
                    brick_pos = brick.position;
                end

                % 创建积木轮廓（半透明）
                obj.createBrickOutline(brick_pos, [0.5, 0.5, 0.5], 0.3);
            end

            fprintf('✅ 目标结构显示完成\n');
        end
        
        function brick_obj = createBrickOutline(obj, position, color, alpha)
            % 创建积木轮廓
            brick_size = [40, 20, 24];  % LEGO标准尺寸
            
            % 创建立方体顶点
            vertices = [
                0, 0, 0;
                brick_size(1), 0, 0;
                brick_size(1), brick_size(2), 0;
                0, brick_size(2), 0;
                0, 0, brick_size(3);
                brick_size(1), 0, brick_size(3);
                brick_size(1), brick_size(2), brick_size(3);
                0, brick_size(2), brick_size(3)
            ];
            
            % 平移到正确位置
            vertices = vertices + repmat(position, 8, 1);
            
            % 定义面
            faces = [
                1, 2, 3, 4;  % 底面
                5, 6, 7, 8;  % 顶面
                1, 2, 6, 5;  % 前面
                3, 4, 8, 7;  % 后面
                1, 4, 8, 5;  % 左面
                2, 3, 7, 6   % 右面
            ];
            
            % 绘制积木
            brick_obj = patch('Vertices', vertices, ...
                              'Faces', faces, ...
                              'FaceColor', color, ...
                              'EdgeColor', 'white', ...
                              'FaceAlpha', alpha, ...
                              'LineWidth', 0.5, ...
                              'Parent', obj.axes_3d);
        end
        
        function startAssembly(obj)
            % 开始拼接
            fprintf('🚀 开始乐高拼接...\n');
            obj.is_playing = true;
            
            % 如果有Simulink模型，启动仿真
            if ~isempty(obj.simulink_model)
                obj.runSimulinkSimulation();
            else
                obj.runMATLABAnimation();
            end
        end
        
        function runSimulinkSimulation(obj)
            % 运行Simulink仿真
            fprintf('启动Simulink仿真...\n');
            
            try
                % 设置仿真参数
                set_param(obj.simulink_model, 'StopTime', '30');
                
                % 启动仿真
                simOut = sim(obj.simulink_model);
                
                fprintf('✅ Simulink仿真完成\n');
                
            catch ME
                fprintf('❌ Simulink仿真失败: %s\n', ME.message);
                fprintf('切换到MATLAB动画模式...\n');
                obj.runMATLABAnimation();
            end
        end
        
        function runMATLABAnimation(obj)
            % 运行MATLAB动画
            fprintf('启动MATLAB动画...\n');
            
            for step = 1:obj.total_steps
                if ~obj.is_playing
                    break;
                end
                
                obj.current_step = step;
                obj.executeAssemblyStep(step);
                
                % 更新进度条
                obj.progress_bar.Value = step;
                
                % 更新状态文本
                status_text = findobj(obj.main_figure, 'Tag', 'status_text');
                if ~isempty(status_text)
                    status_text.String = sprintf('步骤: %d/%d | 当前: %s臂拼接积木%d', ...
                                                  step, obj.total_steps, ...
                                                  obj.assembly_sequence(step).arm, ...
                                                  obj.assembly_sequence(step).brick_id);
                end
                
                drawnow;
                pause(2); % 每步暂停2秒
            end
            
            fprintf('✅ 拼接完成！\n');
        end
        
        function executeAssemblyStep(obj, step_id)
            % 执行单个拼接步骤
            step = obj.assembly_sequence(step_id);
            
            fprintf('执行步骤 %d: %s臂拼接积木 %d\n', step_id, step.arm, step.brick_id);
            
            % 模拟机器人运动
            obj.simulateRobotMotion(step);
            
            % 放置积木
            obj.placeBrick(step.brick);
        end
        
        function simulateRobotMotion(obj, step)
            % 模拟机器人运动
            
            % 简化的关节角度变化
            if strcmp(step.arm, 'left')
                % 左臂运动
                obj.current_config(1).JointPosition = 0.5;
                obj.current_config(2).JointPosition = 0.3;
            else
                % 右臂运动
                obj.current_config(8).JointPosition = -0.5;
                obj.current_config(9).JointPosition = 0.3;
            end
            
            % 更新机器人显示
            try
                show(obj.yumi_robot, obj.current_config, ...
                     'Parent', obj.axes_3d, ...
                     'PreservePlot', true, ...
                     'Frames', 'off');
            catch
                % 如果显示失败，忽略
            end
        end
        
        function placeBrick(obj, brick)
            % 放置积木
            % 确保position是数值数组
            if isstruct(brick.position)
                brick_pos = [brick.position.x, brick.position.y, brick.position.z];
            else
                brick_pos = brick.position;
            end

            obj.createBrickOutline(brick_pos, [1, 0, 0], 0.8);
        end
        
        function controlGripper(obj, arm, action)
            % 控制夹爪
            fprintf('控制%s臂夹爪: %s\n', arm, action);
            
            if strcmp(action, 'open')
                obj.gripper_states.(arm).is_open = true;
                obj.gripper_states.(arm).position = 25; % mm
            else
                obj.gripper_states.(arm).is_open = false;
                obj.gripper_states.(arm).position = 0;
            end
        end
        
        function pauseAssembly(obj)
            % 暂停拼接
            obj.is_playing = false;
            fprintf('⏸️ 拼接已暂停\n');
        end
        
        function stopAssembly(obj)
            % 停止拼接
            obj.is_playing = false;
            obj.current_step = 1;
            fprintf('⏹️ 拼接已停止\n');
        end
        
        function seekToStep(obj, step)
            % 跳转到指定步骤
            obj.current_step = max(1, min(step, obj.total_steps));
            fprintf('跳转到步骤 %d\n', obj.current_step);
        end
        
        function closeSystem(obj)
            % 关闭系统
            if ~isempty(obj.simulink_model) && bdIsLoaded(obj.simulink_model)
                close_system(obj.simulink_model, 0);
            end
            delete(obj.main_figure);
        end
    end
end
