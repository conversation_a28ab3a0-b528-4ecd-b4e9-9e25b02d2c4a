function runCastleAnimationPlayer()
% 启动47积木城堡拼接动画播放器
% 包含播放进度条、播放控制等功能

fprintf('🏰 === 启动YuMi城堡拼接动画播放器 === 🏰\n');

try
    %% 第一步：加载YuMi机器人
    fprintf('第一步：加载YuMi机器人...\n');
    yumi = loadrobot('abbYumi');
    fprintf('✅ YuMi机器人加载成功\n');
    
    %% 第二步：解析47积木城堡LDR文件
    fprintf('\n第二步：解析城堡LDR文件...\n');
    ldr_filename = 'castle_47_bricks.ldr';
    
    if ~exist(ldr_filename, 'file')
        fprintf('❌ 文件不存在: %s\n', ldr_filename);
        return;
    end
    
    % 使用LDR解析器
    parser = LDRParser(ldr_filename);
    success = parser.parseLDRFile();
    
    if ~success
        fprintf('❌ LDR文件解析失败\n');
        return;
    end
    
    fprintf('✅ 成功解析 %d 个积木\n', parser.total_bricks);
    
    %% 第三步：生成轨迹数据
    fprintf('\n第三步：生成机器人轨迹...\n');
    trajectories = generateCastleTrajectories(parser.bricks);
    fprintf('✅ 生成 %d 条轨迹\n', length(trajectories));
    
    %% 第四步：创建积木配置
    fprintf('\n第四步：创建积木配置...\n');
    brick_config = createCastleBrickConfig(parser.bricks);
    fprintf('✅ 积木配置创建完成\n');
    
    %% 第五步：启动动画播放器
    fprintf('\n第五步：启动动画播放器...\n');
    player = AnimationPlayer(yumi, trajectories, brick_config);
    fprintf('✅ 动画播放器启动成功\n');
    
    %% 显示使用说明
    fprintf('\n🎮 === 播放器控制说明 === 🎮\n');
    fprintf('▶️  播放按钮：开始播放动画\n');
    fprintf('⏸️  暂停按钮：暂停动画播放\n');
    fprintf('⏹️  停止按钮：停止并重置动画\n');
    fprintf('📊 进度条：拖动可跳转到任意位置\n');
    fprintf('⚡ 速度滑块：调整播放速度 (0.1x - 3.0x)\n');
    fprintf('\n🏰 城堡包含47个积木，分6层拼接\n');
    fprintf('🤖 机器人将按顺序拼接每个积木\n');
    fprintf('\n💡 提示：点击播放按钮开始观看动画！\n');
    
catch ME
    fprintf('❌ 启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end

function trajectories = generateCastleTrajectories(bricks)
% 为城堡积木生成机器人轨迹

trajectories = {};

for i = 1:length(bricks)
    brick = bricks(i);
    
    % 生成抓取轨迹
    pickup_traj = generatePickupTrajectory(brick);
    trajectories{end+1} = pickup_traj;
    
    % 生成放置轨迹
    place_traj = generatePlaceTrajectory(brick);
    trajectories{end+1} = place_traj;
end

fprintf('为 %d 个积木生成了 %d 条轨迹\n', length(bricks), length(trajectories));

end

function traj = generatePickupTrajectory(brick)
% 生成抓取轨迹

% 简化的轨迹生成
num_points = 20;
traj = struct();

% 生成关节角度序列
q_start = zeros(1, 14); % YuMi有14个关节
q_end = q_start;

% 根据积木位置调整关节角度
q_end(1) = brick.position(1) / 1000; % 转换为米
q_end(8) = brick.position(2) / 1000;

% 插值生成轨迹
traj.Q = zeros(num_points, 14);
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start * (1-alpha) + q_end * alpha;
end

traj.task = 'pickup';
traj.brick_id = brick.id;

end

function traj = generatePlaceTrajectory(brick)
% 生成放置轨迹

% 简化的轨迹生成
num_points = 15;
traj = struct();

% 生成关节角度序列
q_start = zeros(1, 14);
q_end = q_start;

% 根据积木目标位置调整关节角度
target_pos = brick.position + [400, 0, 0]; % 移动到拼接位置
q_end(1) = target_pos(1) / 1000;
q_end(8) = target_pos(2) / 1000;

% 插值生成轨迹
traj.Q = zeros(num_points, 14);
for i = 1:num_points
    alpha = (i-1) / (num_points-1);
    traj.Q(i, :) = q_start * (1-alpha) + q_end * alpha;
end

traj.task = 'place';
traj.brick_id = brick.id;

end

function config = createCastleBrickConfig(bricks)
% 创建城堡积木配置

config = struct();
config.bricks = bricks;
config.total_bricks = length(bricks);
config.assembly_time = 120; % 预计拼接时间（秒）

% 按层分组
config.layers = {};
current_layer = 1;
layer_bricks = [];

for i = 1:length(bricks)
    brick_layer = ceil(bricks(i).position(3) / 10); % 根据Z坐标确定层数
    
    if brick_layer == current_layer
        layer_bricks(end+1) = i;
    else
        config.layers{current_layer} = layer_bricks;
        current_layer = brick_layer;
        layer_bricks = i;
    end
end

% 添加最后一层
if ~isempty(layer_bricks)
    config.layers{current_layer} = layer_bricks;
end

config.num_layers = length(config.layers);

fprintf('城堡配置：%d层，共%d个积木\n', config.num_layers, config.total_bricks);

end
