function fixTrajectoryGhost()
% 修复轨迹残影问题的快速工具
% 提供多种解决方案

fprintf('🔧 === 轨迹残影修复工具 === 🔧\n\n');

fprintf('检测到轨迹残影问题，提供以下解决方案：\n\n');

fprintf('1. 🧹 清理当前图形窗口\n');
fprintf('2. 🎬 使用清洁版动画\n');
fprintf('3. ⚙️ 调整动画设置\n');
fprintf('4. 🔄 重新启动演示\n');
fprintf('5. 📊 显示解决方案说明\n');

choice = input('请选择解决方案 (1-5): ');

switch choice
    case 1
        % 清理当前图形窗口
        fprintf('\n🧹 清理图形窗口...\n');
        clearCurrentFigures();
        
    case 2
        % 使用清洁版动画
        fprintf('\n🎬 启动清洁版动画...\n');
        runCleanAnimation();
        
    case 3
        % 调整动画设置
        fprintf('\n⚙️ 调整动画设置...\n');
        adjustAnimationSettings();
        
    case 4
        % 重新启动演示
        fprintf('\n🔄 重新启动演示...\n');
        restartDemo();
        
    case 5
        % 显示解决方案说明
        fprintf('\n📊 解决方案说明...\n');
        showSolutionExplanation();
        
    otherwise
        fprintf('无效选择，使用默认清理方案\n');
        clearCurrentFigures();
end

end

function clearCurrentFigures()
% 清理当前图形窗口

try
    % 获取所有图形窗口
    figs = findall(0, 'Type', 'figure');
    
    if isempty(figs)
        fprintf('✓ 没有发现打开的图形窗口\n');
        return;
    end
    
    fprintf('发现 %d 个图形窗口\n', length(figs));
    
    for i = 1:length(figs)
        fig = figs(i);
        fig_name = get(fig, 'Name');
        
        if isempty(fig_name)
            fig_name = sprintf('Figure %d', fig.Number);
        end
        
        fprintf('  处理窗口: %s\n', fig_name);
        
        % 清理坐标轴
        axes_handles = findall(fig, 'Type', 'axes');
        for j = 1:length(axes_handles)
            ax = axes_handles(j);
            cla(ax); % 清除坐标轴内容
            hold(ax, 'off'); % 关闭hold状态
            hold(ax, 'on'); % 重新开启hold
        end
    end
    
    fprintf('✓ 图形窗口清理完成\n');
    fprintf('💡 提示: 现在可以重新运行动画\n');
    
catch ME
    fprintf('❌ 清理过程出错: %s\n', ME.message);
end

end

function runCleanAnimation()
% 运行清洁版动画

try
    fprintf('正在准备清洁版动画...\n');
    
    % 加载系统
    [yumi, qHome, ~, ~] = setupRobotEnv();
    brick_config = lego_config();
    
    % 简化任务以便快速演示
    brick_config.task_sequence = brick_config.task_sequence(1:2);
    
    fprintf('生成轨迹...\n');
    trajectories = planTrajectory(yumi, brick_config, qHome);
    
    fprintf('启动清洁版动画...\n');
    cleanTrajectoryAnimation(yumi, qHome, trajectories, brick_config);
    
    fprintf('✓ 清洁版动画完成\n');
    
catch ME
    fprintf('❌ 清洁版动画失败: %s\n', ME.message);
    fprintf('建议尝试其他解决方案\n');
end

end

function adjustAnimationSettings()
% 调整动画设置

fprintf('当前可调整的动画设置：\n\n');

fprintf('1. 动画速度 (暂停时间)\n');
fprintf('2. 轨迹线显示设置\n');
fprintf('3. 机器人显示设置\n');
fprintf('4. 图形窗口设置\n');

setting_choice = input('选择要调整的设置 (1-4): ');

switch setting_choice
    case 1
        fprintf('\n调整动画速度：\n');
        fprintf('当前建议值：\n');
        fprintf('  - 快速: 0.02秒\n');
        fprintf('  - 正常: 0.05秒\n');
        fprintf('  - 慢速: 0.1秒\n');
        
        new_speed = input('输入新的暂停时间 (秒): ');
        if ~isempty(new_speed) && isnumeric(new_speed) && new_speed > 0
            fprintf('✓ 动画速度设置为: %.3f秒\n', new_speed);
            fprintf('💡 在动画函数中使用: pause(%.3f)\n', new_speed);
        end
        
    case 2
        fprintf('\n轨迹线显示设置：\n');
        fprintf('建议设置：\n');
        fprintf('  - 减少轨迹点数显示\n');
        fprintf('  - 使用较细的线条\n');
        fprintf('  - 限制轨迹历史长度\n');
        
    case 3
        fprintf('\n机器人显示设置：\n');
        fprintf('建议设置：\n');
        fprintf('  - 使用 cla() 清除残影\n');
        fprintf('  - 关闭不必要的框架显示\n');
        fprintf('  - 优化更新频率\n');
        
    case 4
        fprintf('\n图形窗口设置：\n');
        fprintf('建议设置：\n');
        fprintf('  - 设置合适的窗口大小\n');
        fprintf('  - 优化渲染器设置\n');
        fprintf('  - 调整视角和光照\n');
        
    otherwise
        fprintf('无效选择\n');
end

end

function restartDemo()
% 重新启动演示

fprintf('选择重启方式：\n\n');
fprintf('1. 🎬 清洁版MATLAB动画\n');
fprintf('2. 🤖 完整演示程序\n');
fprintf('3. 📊 性能测试\n');

restart_choice = input('选择重启方式 (1-3): ');

try
    switch restart_choice
        case 1
            fprintf('\n启动清洁版动画...\n');
            runCleanAnimation();
            
        case 2
            fprintf('\n启动完整演示...\n');
            yumiLegoDemo();
            
        case 3
            fprintf('\n启动性能测试...\n');
            testImprovedPlanner();
            
        otherwise
            fprintf('无效选择，启动默认演示\n');
            runCleanAnimation();
    end
    
catch ME
    fprintf('❌ 重启失败: %s\n', ME.message);
end

end

function showSolutionExplanation()
% 显示解决方案说明

fprintf('\n📚 === 轨迹残影问题解决方案说明 === 📚\n\n');

fprintf('🔍 问题原因：\n');
fprintf('轨迹残影通常由以下原因造成：\n');
fprintf('1. 动画过程中图形对象累积显示\n');
fprintf('2. 没有正确清理之前的绘图内容\n');
fprintf('3. hold on 状态下重复绘制\n');
fprintf('4. 机器人模型重叠显示\n\n');

fprintf('💡 解决方案：\n\n');

fprintf('方案1 - 使用cla()清理：\n');
fprintf('  在每次更新前调用 cla(ax) 清除坐标轴内容\n');
fprintf('  优点: 彻底清除残影\n');
fprintf('  缺点: 需要重新绘制所有内容\n\n');

fprintf('方案2 - 控制绘图对象：\n');
fprintf('  使用句柄控制，删除旧对象再创建新对象\n');
fprintf('  优点: 精确控制\n');
fprintf('  缺点: 代码复杂度较高\n\n');

fprintf('方案3 - 优化更新频率：\n');
fprintf('  减少绘图更新频率，每隔几个点更新一次\n');
fprintf('  优点: 提高性能，减少残影\n');
fprintf('  缺点: 动画可能不够流畅\n\n');

fprintf('方案4 - 使用专门的清洁版动画：\n');
fprintf('  使用 cleanTrajectoryAnimation() 函数\n');
fprintf('  优点: 专门优化，效果最好\n');
fprintf('  缺点: 需要额外的函数\n\n');

fprintf('🎯 推荐使用顺序：\n');
fprintf('1. 首先尝试清洁版动画 (cleanTrajectoryAnimation)\n');
fprintf('2. 如果仍有问题，清理图形窗口\n');
fprintf('3. 调整动画参数设置\n');
fprintf('4. 重新启动演示程序\n\n');

fprintf('🔧 快速修复命令：\n');
fprintf('  清理残影: fixTrajectoryGhost()\n');
fprintf('  清洁动画: cleanTrajectoryAnimation(yumi, qHome, trajectories)\n');
fprintf('  重启演示: yumiLegoDemo()\n\n');

fprintf('💡 预防措施：\n');
fprintf('1. 在动画开始前关闭所有图形窗口\n');
fprintf('2. 使用适当的暂停时间\n');
fprintf('3. 限制轨迹点显示数量\n');
fprintf('4. 定期清理图形内容\n\n');

end
