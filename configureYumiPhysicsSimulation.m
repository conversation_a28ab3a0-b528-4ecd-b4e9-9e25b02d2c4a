function configureYumiPhysicsSimulation()
% 配置YuMi机器人物理仿真参数
% 包括重力、惯性、摩擦力、碰撞检测等

fprintf('⚙️ === 配置YuMi物理仿真参数 === ⚙️\n');

modelName = 'AdvancedYumiSimscape';

% 检查模型是否存在
if ~bdIsLoaded(modelName)
    fprintf('❌ 模型 %s 未加载\n', modelName);
    return;
end

% === 1. 配置求解器参数 ===
fprintf('步骤1: 配置求解器参数...\n');

% 设置适合多体动力学的求解器
set_param(modelName, 'SolverName', 'ode23t');  % 刚体系统专用求解器
set_param(modelName, 'RelTol', '1e-4');        % 相对误差容限
set_param(modelName, 'AbsTol', '1e-6');        % 绝对误差容限
set_param(modelName, 'MaxStep', '0.01');       % 最大步长
set_param(modelName, 'MinStep', '1e-6');       % 最小步长

fprintf('  ✅ 求解器: ode23t (多体动力学优化)\n');
fprintf('  ✅ 相对误差: 1e-4\n');
fprintf('  ✅ 绝对误差: 1e-6\n');

% === 2. 配置重力环境 ===
fprintf('步骤2: 配置重力环境...\n');

% 设置重力加速度
try
    set_param([modelName '/MechConfig'], 'GravityVector', '[0 0 -9.81]');
    fprintf('  ✅ 重力加速度: [0 0 -9.81] m/s²\n');
catch
    fprintf('  ⚠️ 重力配置跳过 (组件不存在)\n');
end

% === 3. 配置关节物理属性 ===
fprintf('步骤3: 配置关节物理属性...\n');

% 右臂关节配置
for i = 1:7
    jointName = sprintf('RightJoint_%d', i);
    jointPath = [modelName '/' jointName];
    
    if getSimulinkBlockHandle(jointPath) ~= -1
        % 设置关节限位
        jointLimits = getRightArmJointLimits(i);
        set_param(jointPath, 'PositionLimits', 'on');
        set_param(jointPath, 'LowerLimit', num2str(jointLimits(1)));
        set_param(jointPath, 'UpperLimit', num2str(jointLimits(2)));
        
        % 设置关节摩擦
        set_param(jointPath, 'InternalMechanics', 'on');
        set_param(jointPath, 'FrictionModel', 'Coulomb and Viscous Friction');
        set_param(jointPath, 'CoulombFriction', '0.1');  % 库仑摩擦
        set_param(jointPath, 'ViscousFriction', '0.01');  % 粘性摩擦
        
        fprintf('  ✅ 右臂关节%d: 限位[%.1f, %.1f]°, 摩擦配置完成\n', ...
                i, rad2deg(jointLimits(1)), rad2deg(jointLimits(2)));
    end
end

% 左臂关节配置
for i = 1:7
    jointName = sprintf('LeftJoint_%d', i);
    jointPath = [modelName '/' jointName];
    
    if getSimulinkBlockHandle(jointPath) ~= -1
        % 设置关节限位
        jointLimits = getLeftArmJointLimits(i);
        set_param(jointPath, 'PositionLimits', 'on');
        set_param(jointPath, 'LowerLimit', num2str(jointLimits(1)));
        set_param(jointPath, 'UpperLimit', num2str(jointLimits(2)));
        
        % 设置关节摩擦
        set_param(jointPath, 'InternalMechanics', 'on');
        set_param(jointPath, 'FrictionModel', 'Coulomb and Viscous Friction');
        set_param(jointPath, 'CoulombFriction', '0.1');
        set_param(jointPath, 'ViscousFriction', '0.01');
        
        fprintf('  ✅ 左臂关节%d: 限位[%.1f, %.1f]°, 摩擦配置完成\n', ...
                i, rad2deg(jointLimits(1)), rad2deg(jointLimits(2)));
    end
end

% === 4. 配置连杆惯性属性 ===
fprintf('步骤4: 配置连杆惯性属性...\n');

% 右臂连杆配置
for i = 1:7
    linkName = sprintf('RightLink_%d', i);
    linkPath = [modelName '/' linkName];
    
    if getSimulinkBlockHandle(linkPath) ~= -1
        % 设置连杆质量
        linkMass = getRightArmLinkMass(i);
        set_param(linkPath, 'MassType', 'Custom');
        set_param(linkPath, 'Mass', num2str(linkMass));
        
        % 设置惯性张量
        inertia = getRightArmLinkInertia(i);
        set_param(linkPath, 'InertiaType', 'Custom');
        set_param(linkPath, 'Inertia', mat2str(inertia));
        
        fprintf('  ✅ 右臂连杆%d: 质量%.1fkg, 惯性配置完成\n', i, linkMass);
    end
end

% 左臂连杆配置
for i = 1:7
    linkName = sprintf('LeftLink_%d', i);
    linkPath = [modelName '/' linkName];
    
    if getSimulinkBlockHandle(linkPath) ~= -1
        % 设置连杆质量
        linkMass = getLeftArmLinkMass(i);
        set_param(linkPath, 'MassType', 'Custom');
        set_param(linkPath, 'Mass', num2str(linkMass));
        
        % 设置惯性张量
        inertia = getLeftArmLinkInertia(i);
        set_param(linkPath, 'InertiaType', 'Custom');
        set_param(linkPath, 'Inertia', mat2str(inertia));
        
        fprintf('  ✅ 左臂连杆%d: 质量%.1fkg, 惯性配置完成\n', i, linkMass);
    end
end

% === 5. 配置夹爪物理属性 ===
fprintf('步骤5: 配置夹爪物理属性...\n');

% 右手夹爪
if getSimulinkBlockHandle([modelName '/RightGripper']) ~= -1
    set_param([modelName '/RightGripper'], 'PositionLimits', 'on');
    set_param([modelName '/RightGripper'], 'LowerLimit', '0');      % 完全闭合
    set_param([modelName '/RightGripper'], 'UpperLimit', '0.025');  % 最大开口25mm
    set_param([modelName '/RightGripper'], 'InternalMechanics', 'on');
    set_param([modelName '/RightGripper'], 'FrictionModel', 'Coulomb and Viscous Friction');
    set_param([modelName '/RightGripper'], 'CoulombFriction', '0.2');
    fprintf('  ✅ 右手夹爪: 开口0-25mm, 摩擦配置完成\n');
end

% 左手夹爪
if getSimulinkBlockHandle([modelName '/LeftGripper']) ~= -1
    set_param([modelName '/LeftGripper'], 'PositionLimits', 'on');
    set_param([modelName '/LeftGripper'], 'LowerLimit', '0');
    set_param([modelName '/LeftGripper'], 'UpperLimit', '0.025');
    set_param([modelName '/LeftGripper'], 'InternalMechanics', 'on');
    set_param([modelName '/LeftGripper'], 'FrictionModel', 'Coulomb and Viscous Friction');
    set_param([modelName '/LeftGripper'], 'CoulombFriction', '0.2');
    fprintf('  ✅ 左手夹爪: 开口0-25mm, 摩擦配置完成\n');
end

% === 6. 配置碰撞检测 ===
fprintf('步骤6: 配置碰撞检测...\n');

% 启用碰撞检测
try
    set_param([modelName '/MechConfig'], 'ContactForceModel', 'on');
    set_param([modelName '/MechConfig'], 'ContactStiffness', '1e6');
    set_param([modelName '/MechConfig'], 'ContactDamping', '1e3');
    fprintf('  ✅ 碰撞检测: 启用\n');
    fprintf('  ✅ 接触刚度: 1e6 N/m\n');
    fprintf('  ✅ 接触阻尼: 1e3 N·s/m\n');
catch
    fprintf('  ⚠️ 碰撞检测配置跳过\n');
end

% === 7. 配置仿真性能 ===
fprintf('步骤7: 配置仿真性能...\n');

% 设置仿真时间
set_param(modelName, 'StartTime', '0');
set_param(modelName, 'StopTime', '10');

% 设置数据记录
set_param(modelName, 'SaveOutput', 'on');
set_param(modelName, 'OutputSaveName', 'yumi_sim_out');
set_param(modelName, 'SaveTime', 'on');
set_param(modelName, 'TimeSaveName', 'yumi_sim_time');

fprintf('  ✅ 仿真时间: 0-10秒\n');
fprintf('  ✅ 数据记录: 启用\n');

% === 8. 保存配置 ===
save_system(modelName);

fprintf('\n🎉 === YuMi物理仿真配置完成 === 🎉\n');
fprintf('配置内容:\n');
fprintf('  ✅ 多体动力学求解器\n');
fprintf('  ✅ 重力环境 (9.81 m/s²)\n');
fprintf('  ✅ 14个关节限位和摩擦\n');
fprintf('  ✅ 14个连杆质量和惯性\n');
fprintf('  ✅ 双臂夹爪物理属性\n');
fprintf('  ✅ 碰撞检测系统\n');
fprintf('  ✅ 仿真性能优化\n');

end

% === 辅助函数 ===

function limits = getRightArmJointLimits(jointIndex)
% 获取YuMi右臂关节限位 (弧度)
% 基于ABB YuMi IRB 14000技术规格

limitsTable = [
    -2.94, 2.94;   % Joint 1: ±168.5°
    -2.50, 0.76;   % Joint 2: -143.5° to +43.5°
    -2.94, 2.94;   % Joint 3: ±168.5°
    -2.16, 1.40;   % Joint 4: -123.5° to +80°
    -5.06, 5.06;   % Joint 5: ±290°
    -1.53, 2.41;   % Joint 6: -88° to +138°
    -3.99, 3.99;   % Joint 7: ±229°
];

limits = limitsTable(jointIndex, :);
end

function limits = getLeftArmJointLimits(jointIndex)
% 获取YuMi左臂关节限位 (弧度)
limits = getRightArmJointLimits(jointIndex);  % 左右臂限位相同
end

function inertia = getRightArmLinkInertia(linkIndex)
% 获取YuMi右臂连杆惯性张量 (kg·m²)

inertiaTable = [
    [0.1, 0.1, 0.05];  % Link 1
    [0.08, 0.08, 0.04]; % Link 2
    [0.06, 0.06, 0.03]; % Link 3
    [0.04, 0.04, 0.02]; % Link 4
    [0.03, 0.03, 0.015]; % Link 5
    [0.02, 0.02, 0.01]; % Link 6
    [0.01, 0.01, 0.005]; % Link 7
];

inertia = diag(inertiaTable(linkIndex, :));
end

function inertia = getLeftArmLinkInertia(linkIndex)
% 获取YuMi左臂连杆惯性张量 (kg·m²)
inertia = getRightArmLinkInertia(linkIndex);  % 左右臂惯性相同
end
