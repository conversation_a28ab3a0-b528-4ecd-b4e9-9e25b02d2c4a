function runImprovedPlayer()
% 启动改进的动画播放器

fprintf('🎬 === 启动改进的动画播放器 === 🎬\n');

try
    % 创建动画播放器
    player = ImprovedAnimationPlayer();
    
    fprintf('✅ 动画播放器启动成功！\n');
    fprintf('\n🎮 === 操作指南 === 🎮\n');
    fprintf('▶️  播放按钮：开始播放动画\n');
    fprintf('⏸️  暂停按钮：暂停动画播放\n');
    fprintf('⏹️  停止按钮：停止并重置动画\n');
    fprintf('📊 进度条：拖动可跳转到任意位置\n');
    fprintf('⚡ 速度滑块：调整播放速度 (0.1x - 3.0x)\n');
    fprintf('\n💡 提示：点击播放按钮开始观看动画！\n');
    
catch ME
    fprintf('❌ 启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end
