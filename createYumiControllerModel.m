function createYumiControllerModel()
% 创建YuMi双臂机器人控制器和动力学模型
% 严格按照MathWorks官方教程结构 - 第二阶段

fprintf('🎮 === 创建YuMi控制器模型 === 🎮\n');
fprintf('按照MathWorks官方教程结构实现 - 第二阶段\n\n');

% 模型名称
modelName = 'YumiControllerModel';

% 关闭已存在的模型
if bdIsLoaded(modelName)
    close_system(modelName, 0);
    fprintf('关闭已存在的模型: %s\n', modelName);
end

% 创建新模型
new_system(modelName);
open_system(modelName);
fprintf('✅ 创建新模型: %s\n', modelName);

% 设置模型参数
set_param(modelName, 'SolverName', 'ode45');
set_param(modelName, 'StopTime', '30');
set_param(modelName, 'RelTol', '1e-4');

% === 1. 复制任务调度器部分 ===
fprintf('\n步骤1: 添加任务调度器...\n');

% Command Logic (与第一阶段相同)
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/CommandLogic'], ...
          'Position', [100, 200, 200, 250]);

% 使用相同的CommandLogic代码
commandLogicCode = getCommandLogicCode();
set_param([modelName '/CommandLogic'], 'Script', commandLogicCode);

% 轨迹生成器
add_block('robotics/Trajectory Generation/Trapezoidal Velocity Profile Trajectory', ...
          [modelName '/RightArmTrajectory'], ...
          'Position', [300, 150, 400, 200]);

add_block('robotics/Trajectory Generation/Trapezoidal Velocity Profile Trajectory', ...
          [modelName '/LeftArmTrajectory'], ...
          'Position', [300, 250, 400, 300]);

% === 2. 添加关节空间控制器 (按照官方教程) ===
fprintf('步骤2: 添加关节空间控制器...\n');

% 右臂控制器子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/RightArmController'], ...
          'Position', [500, 100, 650, 200]);

% 左臂控制器子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/LeftArmController'], ...
          'Position', [500, 250, 650, 350]);

% 创建右臂控制器内部结构
createArmControllerSubsystem([modelName '/RightArmController'], 'right');
createArmControllerSubsystem([modelName '/LeftArmController'], 'left');

% === 3. 添加机器人动力学 (按照官方教程) ===
fprintf('步骤3: 添加机器人动力学...\n');

% 右臂动力学子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/RightArmDynamics'], ...
          'Position', [750, 100, 900, 200]);

% 左臂动力学子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/LeftArmDynamics'], ...
          'Position', [750, 250, 900, 350]);

% 创建动力学子系统内部结构
createArmDynamicsSubsystem([modelName '/RightArmDynamics'], 'right');
createArmDynamicsSubsystem([modelName '/LeftArmDynamics'], 'left');

% === 4. 添加改进的夹爪控制 (按照官方教程) ===
fprintf('步骤4: 添加改进的夹爪控制...\n');

% 右手夹爪控制子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/RightGripperControl'], ...
          'Position', [500, 400, 650, 500]);

% 左手夹爪控制子系统
add_block('simulink/Ports & Subsystems/Subsystem', ...
          [modelName '/LeftGripperControl'], ...
          'Position', [500, 550, 650, 650]);

% 创建夹爪控制子系统
createGripperControlSubsystem([modelName '/RightGripperControl']);
createGripperControlSubsystem([modelName '/LeftGripperControl']);

% === 5. 添加夹爪传感器 (按照官方教程) ===
fprintf('步骤5: 添加夹爪传感器...\n');

% 右手夹爪传感器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/RightGripperSensor'], ...
          'Position', [950, 400, 1050, 450]);

% 左手夹爪传感器
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LeftGripperSensor'], ...
          'Position', [950, 550, 1050, 600]);

% 设置夹爪传感器代码 (按照官方教程)
gripperSensorCode = sprintf(['function [gripperStatus] = fcn(jointConfig, closeGripper)\n'...
    '%% 夹爪传感器逻辑\n'...
    '%% 按照官方教程结构实现\n\n'...
    '%% 提取夹爪关节 (最后两个关节)\n'...
    'gripperJoints = jointConfig(end-1:end);\n\n'...
    '%% 定义夹爪位置\n'...
    'openPosition = [0.025, 0.025];   %% 打开位置\n'...
    'closePosition = [0.0, 0.0];      %% 闭合位置\n\n'...
    '%% 确定目标位置\n'...
    'if closeGripper\n'...
    '    targetPosition = closePosition;\n'...
    'else\n'...
    '    targetPosition = openPosition;\n'...
    'end\n\n'...
    '%% 检查是否到达目标\n'...
    'threshold = 0.005;  %% 5mm阈值\n'...
    'error = abs(gripperJoints - targetPosition);\n'...
    'gripperStatus = all(error < threshold);\n']);

set_param([modelName '/RightGripperSensor'], 'Script', gripperSensorCode);
set_param([modelName '/LeftGripperSensor'], 'Script', gripperSensorCode);

% === 6. 添加输入输出 ===
fprintf('步骤6: 添加输入输出...\n');

% 时钟
add_block('simulink/Sources/Clock', [modelName '/Clock'], ...
          'Position', [50, 225, 80, 255]);

% 输出
add_block('simulink/Sinks/Out1', [modelName '/RightArmOut'], ...
          'Position', [1100, 150, 1130, 180]);
add_block('simulink/Sinks/Out1', [modelName '/LeftArmOut'], ...
          'Position', [1100, 300, 1130, 330]);

% 数据记录
add_block('simulink/Sinks/To Workspace', [modelName '/SimData'], ...
          'Position', [1100, 450, 1200, 480]);
set_param([modelName '/SimData'], 'VariableName', 'yumiSimData');
set_param([modelName '/SimData'], 'SaveFormat', 'Structure');

% === 7. 连接信号线 ===
fprintf('步骤7: 连接信号线...\n');

% 基本连接 (简化版本，实际需要更多连接)
add_line(modelName, 'Clock/1', 'CommandLogic/1');
add_line(modelName, 'CommandLogic/1', 'RightArmTrajectory/1');
add_line(modelName, 'CommandLogic/2', 'LeftArmTrajectory/1');

% === 8. 保存模型 ===
save_system(modelName);

fprintf('\n🎉 === YuMi控制器模型创建完成 === 🎉\n');
fprintf('模型名称: %s\n', modelName);
fprintf('模型结构:\n');
fprintf('  ✅ 任务调度器\n');
fprintf('  ✅ 关节空间控制器\n');
fprintf('  ✅ 机器人动力学\n');
fprintf('  ✅ 夹爪控制系统\n');
fprintf('  ✅ 夹爪传感器\n');
fprintf('\n💡 按照MathWorks官方教程结构实现 - 第二阶段\n');

end

% === 辅助函数 ===

function createArmControllerSubsystem(subsystemPath, armSide)
% 创建手臂控制器子系统 (按照官方教程)

% 打开子系统
open_system(subsystemPath);

% 删除默认端口
delete_block([subsystemPath '/In1']);
delete_block([subsystemPath '/Out1']);

% 添加输入端口
add_block('simulink/Sources/In1', [subsystemPath '/DesiredConfig'], ...
          'Position', [50, 50, 80, 80]);
add_block('simulink/Sources/In1', [subsystemPath '/ActualConfig'], ...
          'Position', [50, 150, 80, 180]);

% 添加计算力矩控制器 (按照官方教程)
add_block('robotics/Manipulator Algorithms/Computed Torque Controller', ...
          [subsystemPath '/ComputedTorqueController'], ...
          'Position', [200, 100, 350, 200]);

% 添加输出端口
add_block('simulink/Sinks/Out1', [subsystemPath '/TorqueOut'], ...
          'Position', [400, 130, 430, 160]);

% 连接信号线
add_line(subsystemPath, 'DesiredConfig/1', 'ComputedTorqueController/1');
add_line(subsystemPath, 'ActualConfig/1', 'ComputedTorqueController/2');
add_line(subsystemPath, 'ComputedTorqueController/1', 'TorqueOut/1');

end

function createArmDynamicsSubsystem(subsystemPath, armSide)
% 创建手臂动力学子系统 (按照官方教程)

% 打开子系统
open_system(subsystemPath);

% 删除默认端口
delete_block([subsystemPath '/In1']);
delete_block([subsystemPath '/Out1']);

% 添加输入端口
add_block('simulink/Sources/In1', [subsystemPath '/TorqueIn'], ...
          'Position', [50, 100, 80, 130]);

% 添加前向动力学 (按照官方教程)
add_block('robotics/Manipulator Algorithms/Forward Dynamics', ...
          [subsystemPath '/ForwardDynamics'], ...
          'Position', [150, 80, 250, 150]);

% 添加积分器
add_block('simulink/Continuous/Integrator', [subsystemPath '/VelocityIntegrator'], ...
          'Position', [300, 80, 350, 120]);
add_block('simulink/Continuous/Integrator', [subsystemPath '/PositionIntegrator'], ...
          'Position', [400, 80, 450, 120]);

% 设置积分器初始条件
set_param([subsystemPath '/VelocityIntegrator'], 'InitialCondition', 'zeros(7,1)');
set_param([subsystemPath '/PositionIntegrator'], 'InitialCondition', 'zeros(7,1)');

% 添加关节限位 (按照官方教程)
add_block('simulink/Discontinuities/Saturation', [subsystemPath '/JointLimits'], ...
          'Position', [500, 80, 550, 120]);
set_param([subsystemPath '/JointLimits'], 'UpperLimit', 'pi*ones(7,1)');
set_param([subsystemPath '/JointLimits'], 'LowerLimit', '-pi*ones(7,1)');

% 添加输出端口
add_block('simulink/Sinks/Out1', [subsystemPath '/ConfigOut'], ...
          'Position', [600, 90, 630, 110]);

% 连接信号线
add_line(subsystemPath, 'TorqueIn/1', 'ForwardDynamics/1');
add_line(subsystemPath, 'ForwardDynamics/1', 'VelocityIntegrator/1');
add_line(subsystemPath, 'VelocityIntegrator/1', 'PositionIntegrator/1');
add_line(subsystemPath, 'PositionIntegrator/1', 'JointLimits/1');
add_line(subsystemPath, 'JointLimits/1', 'ConfigOut/1');

% 反馈连接
add_line(subsystemPath, 'JointLimits/1', 'ForwardDynamics/2');
add_line(subsystemPath, 'VelocityIntegrator/1', 'ForwardDynamics/3');

end

function createGripperControlSubsystem(subsystemPath)
% 创建夹爪控制子系统 (按照官方教程)

% 打开子系统
open_system(subsystemPath);

% 删除默认端口
delete_block([subsystemPath '/In1']);
delete_block([subsystemPath '/Out1']);

% 添加输入端口
add_block('simulink/Sources/In1', [subsystemPath '/GripperCmd'], ...
          'Position', [50, 100, 80, 130]);

% 添加夹爪控制逻辑
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [subsystemPath '/GripperLogic'], ...
          'Position', [150, 80, 250, 150]);

% 设置夹爪控制代码
gripperControlCode = sprintf(['function [gripperForce] = fcn(closeGripper)\n'...
    '%% 夹爪控制逻辑\n'...
    '%% 按照官方教程结构\n\n'...
    'if closeGripper\n'...
    '    gripperForce = 10;  %% 10N闭合力\n'...
    'else\n'...
    '    gripperForce = -10; %% 10N打开力\n'...
    'end\n']);

set_param([subsystemPath '/GripperLogic'], 'Script', gripperControlCode);

% 添加输出端口
add_block('simulink/Sinks/Out1', [subsystemPath '/ForceOut'], ...
          'Position', [300, 105, 330, 125]);

% 连接信号线
add_line(subsystemPath, 'GripperCmd/1', 'GripperLogic/1');
add_line(subsystemPath, 'GripperLogic/1', 'ForceOut/1');

end

function code = getCommandLogicCode()
% 获取CommandLogic代码 (与第一阶段相同)

code = sprintf(['function [rightArmCmd, leftArmCmd, rightGripperCmd, leftGripperCmd, taskState] = fcn(time, rightArmReached, leftArmReached, rightGripperReached, leftGripperReached)\n'...
    '%% YuMi双臂任务调度器\n'...
    '%% 按照MathWorks官方教程结构实现\n\n'...
    'persistent currentTask taskStartTime configSequence\n\n'...
    'if isempty(currentTask)\n'...
    '    currentTask = 1;\n'...
    '    taskStartTime = time;\n'...
    '    configSequence = getYumiConfigSequence();\n'...
    'end\n\n'...
    '%% 任务状态机 (8个状态)\n'...
    'switch currentTask\n'...
    '    case 1\n'...
    '        rightArmCmd = configSequence.right(:,1);\n'...
    '        leftArmCmd = configSequence.left(:,1);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    '    case 2\n'...
    '        rightArmCmd = configSequence.right(:,2);\n'...
    '        leftArmCmd = configSequence.left(:,2);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    '    otherwise\n'...
    '        rightArmCmd = configSequence.right(:,1);\n'...
    '        leftArmCmd = configSequence.left(:,1);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    'end\n\n'...
    'if rightArmReached && leftArmReached && rightGripperReached && leftGripperReached\n'...
    '    if time - taskStartTime > 2.0\n'...
    '        currentTask = currentTask + 1;\n'...
    '        if currentTask > 8\n'...
    '            currentTask = 1;\n'...
    '        end\n'...
    '        taskStartTime = time;\n'...
    '    end\n'...
    'end\n\n'...
    'taskState = currentTask;\n\n'...
    'function configs = getYumiConfigSequence()\n'...
    '    configs.right = zeros(7,8);\n'...
    '    configs.left = zeros(7,8);\n'...
    'end\n']);

end
