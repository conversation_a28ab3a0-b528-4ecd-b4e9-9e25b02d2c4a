function testImprovedPlanner()
% 测试改进的轨迹规划系统
% 对比原始方法和改进方法的性能

clc; clear; close all;

fprintf('=== 双臂机器人轨迹规划系统测试 ===\n');

try
    % 1. 初始化环境
    fprintf('\n1. 初始化机器人环境...\n');
    [yumi, qHome, table, ax] = setupRobotEnv();
    
    % 2. 加载配置
    fprintf('2. 加载乐高配置...\n');
    brick_config = lego_config();
    
    % 限制任务数量以便测试
    brick_config.task_sequence = brick_config.task_sequence(1:min(4, end));
    fprintf('测试任务数量: %d\n', length(brick_config.task_sequence));
    
    % 3. 测试原始方法
    fprintf('\n3. 测试原始轨迹规划方法...\n');
    tic;
    original_trajectories = planTrajectory(yumi, brick_config, qHome);
    original_time = toc;
    
    fprintf('原始方法完成时间: %.2f 秒\n', original_time);
    fprintf('生成轨迹数: %d\n', length(original_trajectories));
    
    % 4. 测试改进方法 - 基础模式
    fprintf('\n4. 测试改进方法 - 基础模式...\n');
    options_basic = struct();
    options_basic.planning_mode = 'basic';
    options_basic.analyze_current = false;
    
    tic;
    basic_trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options_basic);
    basic_time = toc;
    
    fprintf('基础改进方法完成时间: %.2f 秒\n', basic_time);
    fprintf('生成轨迹数: %d\n', length(basic_trajectories));
    
    % 5. 测试改进方法 - 高级模式
    fprintf('\n5. 测试改进方法 - 高级模式...\n');
    options_advanced = struct();
    options_advanced.planning_mode = 'advanced';
    options_advanced.coordination_mode = 'adaptive';
    options_advanced.safety_margin = 0.08;
    options_advanced.rrt_max_iterations = 2000; % 减少迭代次数以加快测试
    options_advanced.max_velocity = 1.2;
    options_advanced.max_acceleration = 2.5;
    
    tic;
    try
        advanced_trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options_advanced);
        advanced_time = toc;
        
        fprintf('高级改进方法完成时间: %.2f 秒\n', advanced_time);
        fprintf('生成轨迹数: %d\n', length(advanced_trajectories));
    catch ME
        fprintf('高级方法测试失败: %s\n', ME.message);
        advanced_trajectories = {};
        advanced_time = inf;
    end
    
    % 6. 性能对比分析
    fprintf('\n6. 性能对比分析...\n');
    performanceComparison(original_trajectories, basic_trajectories, advanced_trajectories, ...
                         original_time, basic_time, advanced_time);
    
    % 7. 可视化结果
    fprintf('\n7. 可视化轨迹对比...\n');
    visualizeTrajectoryComparison(original_trajectories, basic_trajectories, advanced_trajectories);
    
    % 8. 生成测试报告
    fprintf('\n8. 生成测试报告...\n');
    generateTestReport(original_trajectories, basic_trajectories, advanced_trajectories, ...
                      original_time, basic_time, advanced_time);
    
    fprintf('\n=== 测试完成 ===\n');
    
catch ME
    fprintf('测试过程中发生错误: %s\n', ME.message);
    fprintf('错误位置: %s\n', ME.stack(1).name);
    rethrow(ME);
end

end

function performanceComparison(original_traj, basic_traj, advanced_traj, ...
                              original_time, basic_time, advanced_time)
% 性能对比分析

fprintf('\n--- 性能对比结果 ---\n');

% 计算各方法的性能指标
methods = {'原始方法', '基础改进', '高级改进'};
trajectories = {original_traj, basic_traj, advanced_traj};
planning_times = [original_time, basic_time, advanced_time];

fprintf('%-12s %-10s %-12s %-12s %-12s\n', '方法', '规划时间(s)', '轨迹数量', '平均平滑度', '最大速度');
fprintf('%-12s %-10s %-12s %-12s %-12s\n', '----', '--------', '------', '--------', '------');

for i = 1:length(methods)
    if ~isempty(trajectories{i})
        metrics = calculateTrajectoryMetrics(trajectories{i});
        fprintf('%-12s %-10.2f %-12d %-12.4f %-12.4f\n', ...
                methods{i}, planning_times(i), length(trajectories{i}), ...
                metrics.avg_smoothness, metrics.max_velocity);
    else
        fprintf('%-12s %-10.2f %-12s %-12s %-12s\n', ...
                methods{i}, planning_times(i), '失败', '失败', '失败');
    end
end

% 计算改进百分比
if ~isempty(basic_traj) && ~isempty(original_traj)
    basic_metrics = calculateTrajectoryMetrics(basic_traj);
    original_metrics = calculateTrajectoryMetrics(original_traj);
    
    smoothness_improvement = (original_metrics.avg_smoothness - basic_metrics.avg_smoothness) / original_metrics.avg_smoothness * 100;
    fprintf('\n基础改进平滑度提升: %.1f%%\n', smoothness_improvement);
end

if ~isempty(advanced_traj) && ~isempty(original_traj)
    advanced_metrics = calculateTrajectoryMetrics(advanced_traj);
    original_metrics = calculateTrajectoryMetrics(original_traj);
    
    smoothness_improvement = (original_metrics.avg_smoothness - advanced_metrics.avg_smoothness) / original_metrics.avg_smoothness * 100;
    fprintf('高级改进平滑度提升: %.1f%%\n', smoothness_improvement);
end

end

function metrics = calculateTrajectoryMetrics(trajectories)
% 计算轨迹性能指标

metrics = struct();

if isempty(trajectories)
    metrics.avg_smoothness = inf;
    metrics.max_velocity = inf;
    metrics.total_time = inf;
    return;
end

smoothness_values = [];
max_velocities = [];
total_time = 0;

for i = 1:length(trajectories)
    traj = trajectories{i};
    
    % 计算平滑度（基于关节角变化）
    if isfield(traj, 'Q') && size(traj.Q, 1) > 2
        % 计算二阶差分作为平滑度指标
        second_diff = diff(traj.Q, 2);
        smoothness = sqrt(mean(sum(second_diff.^2, 2)));
        smoothness_values = [smoothness_values; smoothness];
    end
    
    % 计算最大速度
    if isfield(traj, 'velocity')
        max_vel = max(sqrt(sum(traj.velocity.^2, 2)));
        max_velocities = [max_velocities; max_vel];
    elseif isfield(traj, 'Q') && size(traj.Q, 1) > 1
        % 估算速度
        dt = 0.1; % 假设时间步长
        vel = diff(traj.Q) / dt;
        max_vel = max(sqrt(sum(vel.^2, 2)));
        max_velocities = [max_velocities; max_vel];
    end
    
    % 计算总时间
    if isfield(traj, 'time')
        total_time = total_time + traj.time(end);
    else
        total_time = total_time + size(traj.Q, 1) * 0.1; % 估算
    end
end

metrics.avg_smoothness = mean(smoothness_values);
metrics.max_velocity = max(max_velocities);
metrics.total_time = total_time;

end

function visualizeTrajectoryComparison(original_traj, basic_traj, advanced_traj)
% 可视化轨迹对比

if isempty(original_traj)
    fprintf('无原始轨迹数据，跳过可视化\n');
    return;
end

figure('Name', '轨迹对比分析', 'Position', [100, 100, 1200, 800]);

% 选择第一个轨迹进行对比
traj_idx = 1;

if length(original_traj) >= traj_idx
    original_Q = original_traj{traj_idx}.Q;
    
    % 子图1：关节角轨迹对比
    subplot(2, 3, 1);
    plot(original_Q(:, 1:3), 'LineWidth', 1.5);
    title('原始方法 - 前3个关节');
    xlabel('时间步');
    ylabel('关节角 (rad)');
    legend('关节1', '关节2', '关节3');
    grid on;
    
    % 子图2：平滑度对比
    subplot(2, 3, 2);
    if size(original_Q, 1) > 2
        smoothness_original = sqrt(sum(diff(original_Q, 2).^2, 2));
        plot(smoothness_original, 'r-', 'LineWidth', 1.5);
        hold on;
        
        if ~isempty(basic_traj) && length(basic_traj) >= traj_idx
            basic_Q = basic_traj{traj_idx}.Q;
            if size(basic_Q, 1) > 2
                smoothness_basic = sqrt(sum(diff(basic_Q, 2).^2, 2));
                plot(smoothness_basic, 'g-', 'LineWidth', 1.5);
            end
        end
        
        if ~isempty(advanced_traj) && length(advanced_traj) >= traj_idx
            advanced_Q = advanced_traj{traj_idx}.Q;
            if size(advanced_Q, 1) > 2
                smoothness_advanced = sqrt(sum(diff(advanced_Q, 2).^2, 2));
                plot(smoothness_advanced, 'b-', 'LineWidth', 1.5);
            end
        end
        
        title('轨迹平滑度对比');
        xlabel('时间步');
        ylabel('平滑度指标');
        legend('原始', '基础改进', '高级改进');
        grid on;
    end
    
    % 子图3：速度对比
    subplot(2, 3, 3);
    if size(original_Q, 1) > 1
        vel_original = sqrt(sum(diff(original_Q).^2, 2));
        plot(vel_original, 'r-', 'LineWidth', 1.5);
        hold on;
        
        if ~isempty(basic_traj) && length(basic_traj) >= traj_idx
            basic_Q = basic_traj{traj_idx}.Q;
            if size(basic_Q, 1) > 1
                vel_basic = sqrt(sum(diff(basic_Q).^2, 2));
                plot(vel_basic, 'g-', 'LineWidth', 1.5);
            end
        end
        
        if ~isempty(advanced_traj) && length(advanced_traj) >= traj_idx
            advanced_Q = advanced_traj{traj_idx}.Q;
            if size(advanced_Q, 1) > 1
                vel_advanced = sqrt(sum(diff(advanced_Q).^2, 2));
                plot(vel_advanced, 'b-', 'LineWidth', 1.5);
            end
        end
        
        title('速度对比');
        xlabel('时间步');
        ylabel('关节速度 (rad/step)');
        legend('原始', '基础改进', '高级改进');
        grid on;
    end
    
    % 子图4-6：显示改进方法的轨迹
    if ~isempty(basic_traj) && length(basic_traj) >= traj_idx
        subplot(2, 3, 4);
        basic_Q = basic_traj{traj_idx}.Q;
        plot(basic_Q(:, 1:3), 'LineWidth', 1.5);
        title('基础改进方法 - 前3个关节');
        xlabel('时间步');
        ylabel('关节角 (rad)');
        legend('关节1', '关节2', '关节3');
        grid on;
    end
    
    if ~isempty(advanced_traj) && length(advanced_traj) >= traj_idx
        subplot(2, 3, 5);
        advanced_Q = advanced_traj{traj_idx}.Q;
        plot(advanced_Q(:, 1:3), 'LineWidth', 1.5);
        title('高级改进方法 - 前3个关节');
        xlabel('时间步');
        ylabel('关节角 (rad)');
        legend('关节1', '关节2', '关节3');
        grid on;
    end
    
    % 性能汇总
    subplot(2, 3, 6);
    methods = {'原始', '基础', '高级'};
    trajectories = {original_traj, basic_traj, advanced_traj};
    smoothness_scores = [];
    
    for i = 1:length(trajectories)
        if ~isempty(trajectories{i}) && length(trajectories{i}) >= traj_idx
            Q = trajectories{i}{traj_idx}.Q;
            if size(Q, 1) > 2
                smoothness = mean(sqrt(sum(diff(Q, 2).^2, 2)));
                smoothness_scores = [smoothness_scores; smoothness];
            else
                smoothness_scores = [smoothness_scores; 0];
            end
        else
            smoothness_scores = [smoothness_scores; 0];
        end
    end
    
    bar(smoothness_scores);
    set(gca, 'XTickLabel', methods);
    title('平滑度对比');
    ylabel('平滑度指标');
    grid on;
end

end

function generateTestReport(original_traj, basic_traj, advanced_traj, ...
                           original_time, basic_time, advanced_time)
% 生成测试报告

report_filename = sprintf('trajectory_test_report_%s.txt', datestr(now, 'yyyymmdd_HHMMSS'));

fid = fopen(report_filename, 'w');

fprintf(fid, '双臂机器人轨迹规划系统测试报告\n');
fprintf(fid, '生成时间: %s\n', datestr(now));
fprintf(fid, '========================================\n\n');

fprintf(fid, '测试配置:\n');
fprintf(fid, '- 测试任务数量: 4\n');
fprintf(fid, '- 测试方法: 原始方法, 基础改进, 高级改进\n\n');

fprintf(fid, '性能结果:\n');
fprintf(fid, '%-12s %-12s %-12s\n', '方法', '规划时间(s)', '轨迹数量');
fprintf(fid, '%-12s %-12s %-12s\n', '----', '----------', '--------');

methods = {'原始方法', '基础改进', '高级改进'};
trajectories = {original_traj, basic_traj, advanced_traj};
times = [original_time, basic_time, advanced_time];

for i = 1:length(methods)
    if ~isempty(trajectories{i})
        fprintf(fid, '%-12s %-12.2f %-12d\n', methods{i}, times(i), length(trajectories{i}));
    else
        fprintf(fid, '%-12s %-12.2f %-12s\n', methods{i}, times(i), '失败');
    end
end

fprintf(fid, '\n详细性能指标:\n');
for i = 1:length(methods)
    if ~isempty(trajectories{i})
        metrics = calculateTrajectoryMetrics(trajectories{i});
        fprintf(fid, '%s:\n', methods{i});
        fprintf(fid, '  平均平滑度: %.4f\n', metrics.avg_smoothness);
        fprintf(fid, '  最大速度: %.4f\n', metrics.max_velocity);
        fprintf(fid, '  总执行时间: %.2f\n', metrics.total_time);
        fprintf(fid, '\n');
    end
end

fprintf(fid, '结论:\n');
if ~isempty(basic_traj) && ~isempty(original_traj)
    fprintf(fid, '- 基础改进方法成功实现了轨迹平滑化优化\n');
end

if ~isempty(advanced_traj)
    fprintf(fid, '- 高级改进方法集成了RRT路径规划和B-spline优化\n');
else
    fprintf(fid, '- 高级改进方法需要进一步调试和优化\n');
end

fprintf(fid, '- 建议继续优化算法参数以提高性能\n');

fclose(fid);

fprintf('测试报告已保存到: %s\n', report_filename);

end
