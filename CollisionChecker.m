classdef CollisionChecker < handle
    % 双臂机器人碰撞检测器
    % 检测自碰撞、环境碰撞和双臂间碰撞
    
    properties
        robot               % 机器人模型
        environment_objects % 环境障碍物
        safety_margin      % 安全距离
        left_arm_joints    % 左臂关节索引
        right_arm_joints   % 右臂关节索引
        collision_meshes   % 碰撞检测网格
    end
    
    methods
        function obj = CollisionChecker(robot, options)
            % 构造函数
            % 输入：
            %   robot - 机器人模型
            %   options - 配置选项
            
            obj.robot = robot;
            
            if nargin < 2
                options = struct();
            end
            
            obj.safety_margin = getfield_default(options, 'safety_margin', 0.05); % 5cm安全距离
            
            % 初始化双臂关节索引（基于YuMi机器人）
            obj.initializeArmJoints();
            
            % 初始化环境障碍物
            obj.initializeEnvironment();
            
            fprintf('碰撞检测器初始化完成\n');
            fprintf('安全距离: %.3f m\n', obj.safety_margin);
        end
        
        function initializeArmJoints(obj)
            % 初始化左右臂关节索引
            % 基于YuMi机器人的关节配置
            
            % YuMi机器人关节分配（假设）
            % 关节1-7: 右臂, 关节8-14: 左臂
            obj.right_arm_joints = 1:7;
            obj.left_arm_joints = 8:14;
            
            fprintf('右臂关节: %s\n', mat2str(obj.right_arm_joints));
            fprintf('左臂关节: %s\n', mat2str(obj.left_arm_joints));
        end
        
        function initializeEnvironment(obj)
            % 初始化环境障碍物
            obj.environment_objects = {};
            
            % 添加桌面作为障碍物
            table_size = [0.72, 0.6, 0.02];
            table_center = [0.5, 0, 0.05];
            table_box = collisionBox(table_size(1), table_size(2), table_size(3));
            table_box.Pose = trvec2tform(table_center);
            
            obj.environment_objects{end+1} = table_box;
            
            fprintf('环境障碍物数量: %d\n', length(obj.environment_objects));
        end
        
        function is_colliding = checkCollision(obj, q)
            % 检查给定配置是否存在碰撞

            % 确保q是正确的维度
            if length(q) ~= obj.robot.NumBodies
                is_colliding = true; % 维度不匹配视为碰撞
                return;
            end

            is_colliding = false;
            
            % 1. 自碰撞检测
            if obj.check_self_collision
                is_self_colliding = any(checkCollision(obj.robot, q, 'SkippedSelfCollisions', 'off'));
                if is_self_colliding
                    is_colliding = true;
                    % fprintf('自碰撞 detected\n');
                    return;
                end
            end
            
            % 2. 环境碰撞检测
            if obj.check_env_collision
                for i = 1:length(obj.environment_obstacles)
                    obstacle = obj.environment_obstacles{i};
                    is_env_colliding = checkCollision(obj.robot, q, {obstacle});
                    if any(is_env_colliding)
                        is_colliding = true;
                        % fprintf('环境碰撞 detected\n');
                        return;
                    end
                end
            end
        end
        
        function collision = checkSelfCollision(obj, q, arm_name)
            % 检查自碰撞
            collision = false;
            
            try
                % 获取当前配置下的机器人状态
                config = obj.robot.homeConfiguration;
                
                % 更新相应手臂的关节角
                if strcmp(arm_name, 'right')
                    config(obj.right_arm_joints) = q;
                elseif strcmp(arm_name, 'left')
                    config(obj.left_arm_joints) = q;
                elseif strcmp(arm_name, 'both')
                    config = q; % 假设q包含所有关节角
                end
                
                % 使用机器人工具箱的碰撞检测（如果可用）
                if hasMethod(obj.robot, 'checkCollision')
                    collision = checkCollision(obj.robot, config);
                else
                    % 简化的自碰撞检测：检查关节限制
                    collision = obj.checkJointLimits(config);
                end
                
            catch
                % 如果检测失败，保守地认为有碰撞
                collision = true;
            end
        end
        
        function collision = checkEnvironmentCollision(obj, q, arm_name)
            % 检查与环境的碰撞
            collision = false;
            
            try
                % 获取末端执行器位置
                config = obj.robot.homeConfiguration;
                
                if strcmp(arm_name, 'right')
                    config(obj.right_arm_joints) = q;
                    ee_name = 'gripper_r_base';
                elseif strcmp(arm_name, 'left')
                    config(obj.left_arm_joints) = q;
                    ee_name = 'gripper_l_base';
                else
                    config = q;
                    % 检查两个末端执行器
                    collision = obj.checkEnvironmentCollision(q(obj.right_arm_joints), 'right') || ...
                               obj.checkEnvironmentCollision(q(obj.left_arm_joints), 'left');
                    return;
                end
                
                % 获取末端执行器变换
                T_ee = getTransform(obj.robot, config, ee_name);
                ee_position = T_ee(1:3, 4);
                
                % 检查是否过低（可能撞到桌面）
                if ee_position(3) < 0.03 % 桌面以下3cm
                    collision = true;
                    return;
                end
                
                % 检查工作空间限制
                if obj.checkWorkspaceLimits(ee_position, arm_name)
                    collision = true;
                    return;
                end
                
            catch
                collision = true;
            end
        end
        
        function collision = checkDualArmCollision(obj, q)
            % 检查双臂间碰撞
            collision = false;
            
            try
                % 获取两个末端执行器的位置
                T_right = getTransform(obj.robot, q, 'gripper_r_base');
                T_left = getTransform(obj.robot, q, 'gripper_l_base');
                
                pos_right = T_right(1:3, 4);
                pos_left = T_left(1:3, 4);
                
                % 计算两个末端执行器之间的距离
                distance = norm(pos_right - pos_left);
                
                % 如果距离小于安全距离，认为有碰撞风险
                if distance < obj.safety_margin * 2
                    collision = true;
                    return;
                end
                
                % 更详细的碰撞检测可以在这里添加
                % 例如检查手臂链接之间的距离
                
            catch
                collision = true;
            end
        end
        
        function violation = checkJointLimits(obj, q)
            % 检查关节限制违反
            violation = false;
            
            if isfield(obj.robot, 'PositionLimits')
                limits = obj.robot.PositionLimits;
                for i = 1:length(q)
                    if q(i) < limits(i,1) || q(i) > limits(i,2)
                        violation = true;
                        return;
                    end
                end
            end
        end
        
        function violation = checkWorkspaceLimits(obj, position, arm_name)
            % 检查工作空间限制
            violation = false;
            
            x = position(1);
            y = position(2);
            z = position(3);
            
            % 基本工作空间限制
            if z < 0.05 || z > 0.8  % 高度限制
                violation = true;
                return;
            end
            
            % 手臂特定的工作空间限制
            if strcmp(arm_name, 'right')
                % 右臂工作区域
                if x < 0.3 || x > 1.0 || abs(y) > 0.5
                    violation = true;
                end
            elseif strcmp(arm_name, 'left')
                % 左臂工作区域
                if x < 0.0 || x > 0.7 || abs(y) > 0.5
                    violation = true;
                end
            end
        end
        
        function distance = computeMinDistance(obj, q1, q2, arm_name)
            % 计算两个配置之间的最小距离
            % 用于优化和安全评估
            
            try
                config1 = obj.robot.homeConfiguration;
                config2 = obj.robot.homeConfiguration;
                
                if strcmp(arm_name, 'right')
                    config1(obj.right_arm_joints) = q1;
                    config2(obj.right_arm_joints) = q2;
                    ee_name = 'gripper_r_base';
                elseif strcmp(arm_name, 'left')
                    config1(obj.left_arm_joints) = q1;
                    config2(obj.left_arm_joints) = q2;
                    ee_name = 'gripper_l_base';
                else
                    config1 = q1;
                    config2 = q2;
                    % 对于双臂情况，返回关节空间距离
                    distance = norm(q2 - q1);
                    return;
                end
                
                % 计算末端执行器位置距离
                T1 = getTransform(obj.robot, config1, ee_name);
                T2 = getTransform(obj.robot, config2, ee_name);
                
                distance = norm(T2(1:3,4) - T1(1:3,4));
                
            catch
                % 如果计算失败，返回关节空间距离
                distance = norm(q2 - q1);
            end
        end
        
        function addObstacle(obj, obstacle)
            % 添加环境障碍物
            obj.environment_objects{end+1} = obstacle;
        end
        
        function clearObstacles(obj)
            % 清除所有环境障碍物
            obj.environment_objects = {};
        end
    end
end

function value = getfield_default(s, field, default_value)
    % 获取结构体字段，如果不存在则返回默认值
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end

function result = hasMethod(obj, method_name)
    % 检查对象是否有指定方法
    result = any(strcmp(methods(obj), method_name));
end
