classdef LegoVisualization < handle
    % 乐高积木可视化系统
    % 用于在YuMi机器人仿真中显示乐高积木的抓取、移动和堆叠过程
    
    properties
        figure_handle       % 图形窗口句柄
        axes_handle        % 坐标轴句柄
        robot_handle       % 机器人显示句柄
        lego_objects       % 乐高积木对象
        gripper_objects    % 夹爪对象
        animation_speed    % 动画速度
        brick_config       % 积木配置
        current_positions  % 当前积木位置
        target_positions   % 目标积木位置
    end
    
    methods
        function obj = LegoVisualization(yumi, brick_config, options)
            % 构造函数
            % 输入：
            %   yumi - YuMi机器人模型
            %   brick_config - 乐高配置
            %   options - 可视化选项
            
            if nargin < 3
                options = struct();
            end
            
            obj.brick_config = brick_config;
            obj.animation_speed = getfield_default(options, 'animation_speed', 0.1);
            
            % 初始化可视化环境
            obj.initializeVisualization(yumi);
            
            % 创建乐高积木对象
            obj.createLegoObjects();
            
            fprintf('乐高可视化系统初始化完成\n');
        end
        
        function initializeVisualization(obj, yumi)
            % 初始化可视化环境
            
            % 创建新的图形窗口
            obj.figure_handle = figure('Name', 'YuMi机器人乐高堆叠仿真', ...
                                      'Position', [100, 100, 1200, 800], ...
                                      'Color', 'white');
            
            % 设置坐标轴
            obj.axes_handle = axes('Parent', obj.figure_handle);
            hold(obj.axes_handle, 'on');
            
            % 设置环境
            [~, ~, ~, ~] = setupRobotEnv();
            obj.axes_handle = gca;
            
            % 设置视角和光照
            view(obj.axes_handle, 45, 30);
            lighting(obj.axes_handle, 'gouraud');
            camlight(obj.axes_handle, 'headlight');
            
            % 设置坐标轴属性
            xlabel(obj.axes_handle, 'X (m)');
            ylabel(obj.axes_handle, 'Y (m)');
            zlabel(obj.axes_handle, 'Z (m)');
            title(obj.axes_handle, 'YuMi机器人乐高堆叠仿真', 'FontSize', 14, 'FontWeight', 'bold');
            
            % 显示机器人初始状态
            obj.robot_handle = show(yumi, yumi.homeConfiguration, ...
                                   'Parent', obj.axes_handle, ...
                                   'PreservePlot', true, ...
                                   'Frames', 'off');
            
            fprintf('可视化环境初始化完成\n');
        end
        
        function createLegoObjects(obj)
            % 创建乐高积木3D对象
            
            obj.lego_objects = {};
            obj.current_positions = {};
            obj.target_positions = obj.brick_config.all_targets;
            
            % 创建右手区域的乐高积木
            fprintf('创建右手区域乐高积木...\n');
            for i = 1:size(obj.brick_config.right_arm_initial, 1)
                lego_info = obj.brick_config.right_arm_initial(i, :);
                lego_type = lego_info{1};
                position = lego_info{2};
                orientation = lego_info{3};
                
                % 创建乐高积木对象
                lego_obj = obj.createSingleLego(lego_type, position, orientation, 'right', i);
                obj.lego_objects{end+1} = lego_obj;
                obj.current_positions{end+1} = position;
            end
            
            % 创建左手区域的乐高积木
            fprintf('创建左手区域乐高积木...\n');
            for i = 1:size(obj.brick_config.left_arm_initial, 1)
                lego_info = obj.brick_config.left_arm_initial(i, :);
                lego_type = lego_info{1};
                position = lego_info{2};
                orientation = lego_info{3};
                
                % 创建乐高积木对象
                lego_obj = obj.createSingleLego(lego_type, position, orientation, 'left', i);
                obj.lego_objects{end+1} = lego_obj;
                obj.current_positions{end+1} = position;
            end
            
            fprintf('创建了 %d 个乐高积木对象\n', length(obj.lego_objects));
        end
        
        function lego_obj = createSingleLego(obj, lego_type, position, orientation, arm, index)
            % 创建单个乐高积木3D对象
            
            % 获取乐高尺寸
            dimensions = obj.getLegoTypeDimensions(lego_type);
            
            % 创建立方体网格
            [X, Y, Z] = obj.createLegoMesh(dimensions, position, orientation);
            
            % 设置颜色
            if strcmp(arm, 'right')
                color = [0.2, 0.6, 1.0]; % 蓝色
            else
                color = [1.0, 0.3, 0.3]; % 红色
            end
            
            % 创建表面对象
            lego_obj = struct();
            lego_obj.surface = surf(obj.axes_handle, X, Y, Z, ...
                                   'FaceColor', color, ...
                                   'EdgeColor', 'black', ...
                                   'LineWidth', 0.5, ...
                                   'FaceAlpha', 0.8);
            
            lego_obj.type = lego_type;
            lego_obj.arm = arm;
            lego_obj.index = index;
            lego_obj.position = position;
            lego_obj.orientation = orientation;
            lego_obj.dimensions = dimensions;
            lego_obj.attached_to_gripper = false;
            
            % 添加文本标签
            lego_obj.label = text(obj.axes_handle, position(1), position(2), position(3) + dimensions(3)/2 + 0.01, ...
                                 sprintf('%s%d', upper(arm(1)), index), ...
                                 'HorizontalAlignment', 'center', ...
                                 'FontSize', 8, 'FontWeight', 'bold', ...
                                 'Color', 'black');
        end
        
        function [X, Y, Z] = createLegoMesh(obj, dimensions, position, orientation)
            % 创建乐高积木的3D网格
            
            % 基本立方体顶点
            lx = dimensions(1) / 2;
            ly = dimensions(2) / 2;
            lz = dimensions(3) / 2;
            
            % 立方体的8个顶点
            vertices = [
                -lx, -ly, -lz;  % 1
                 lx, -ly, -lz;  % 2
                 lx,  ly, -lz;  % 3
                -lx,  ly, -lz;  % 4
                -lx, -ly,  lz;  % 5
                 lx, -ly,  lz;  % 6
                 lx,  ly,  lz;  % 7
                -lx,  ly,  lz;  % 8
            ];
            
            % 旋转矩阵（绕Z轴）
            R = [cos(orientation), -sin(orientation), 0;
                 sin(orientation),  cos(orientation), 0;
                 0,                 0,                1];
            
            % 应用旋转和平移
            vertices_transformed = (R * vertices')' + position;
            
            % 创建立方体的6个面
            faces = [
                1, 2, 3, 4;  % 底面
                5, 6, 7, 8;  % 顶面
                1, 2, 6, 5;  % 前面
                3, 4, 8, 7;  % 后面
                1, 4, 8, 5;  % 左面
                2, 3, 7, 6;  % 右面
            ];
            
            % 为surf函数创建网格数据
            % 简化为使用patch对象的方式
            X = reshape(vertices_transformed(faces', 1), 4, 6);
            Y = reshape(vertices_transformed(faces', 2), 4, 6);
            Z = reshape(vertices_transformed(faces', 3), 4, 6);
        end
        
        function dimensions = getLegoTypeDimensions(obj, lego_type)
            % 获取乐高类型的尺寸
            switch lego_type
                case 'brick_2x4'
                    dimensions = [0.0318, 0.0159, 0.0096];
                case 'arch_1x4'
                    dimensions = [0.0318, 0.0127, 0.0096];
                case 'slope_brick'
                    dimensions = [0.0318, 0.0159, 0.0096];
                case 'cone_2x2x2'
                    dimensions = [0.0159, 0.0159, 0.0192];
                otherwise
                    dimensions = [0.032, 0.016, 0.0096];
            end
        end
        
        function animateTrajectoryWithLego(obj, yumi, trajectories, qHome)
            % 动画显示轨迹执行过程，包括乐高抓取和放置
            
            fprintf('\n=== 开始YuMi机器人乐高堆叠动画 ===\n');
            
            % 显示初始状态
            show(yumi, qHome, 'Parent', obj.axes_handle, 'PreservePlot', true, 'Frames', 'off');
            
            for i = 1:length(trajectories)
                traj = trajectories{i};
                
                fprintf('\n--- 执行任务 %d: %s臂 ---\n', i, traj.arm);
                
                % 获取任务信息
                if isfield(traj, 'task')
                    task = traj.task;
                    lego_index = obj.findLegoIndex(task.arm, task.arm_lego_id);
                    
                    if ~isempty(lego_index)
                        % 执行抓取和放置动画
                        obj.animatePickAndPlace(yumi, traj, lego_index, task);
                    end
                else
                    % 如果没有任务信息，只显示轨迹
                    obj.animateTrajectoryOnly(yumi, traj);
                end
                
                % 任务间暂停
                pause(1.0);
            end
            
            fprintf('\n🎉 乐高堆叠动画完成！\n');
            obj.showFinalResult();
        end
        
        function animatePickAndPlace(obj, yumi, traj, lego_index, task)
            % 动画显示抓取和放置过程
            
            lego_obj = obj.lego_objects{lego_index};
            
            % 阶段1：移动到抓取位置
            fprintf('  阶段1: 移动到抓取位置\n');
            pick_phase_end = round(size(traj.Q, 1) * 0.4);
            obj.animatePhase(yumi, traj, 1, pick_phase_end, '移动到抓取位置');
            
            % 阶段2：抓取动作
            fprintf('  阶段2: 抓取乐高积木\n');
            obj.animateGraspAction(yumi, traj, lego_obj, pick_phase_end);
            
            % 阶段3：移动到放置位置
            fprintf('  阶段3: 移动到放置位置\n');
            place_phase_start = round(size(traj.Q, 1) * 0.6);
            obj.animatePhaseWithLego(yumi, traj, place_phase_start, size(traj.Q, 1)-10, lego_obj, '移动到放置位置');
            
            % 阶段4：放置动作
            fprintf('  阶段4: 放置乐高积木\n');
            obj.animatePlaceAction(yumi, traj, lego_obj, task, size(traj.Q, 1)-10);
            
            % 阶段5：退离
            fprintf('  阶段5: 退离\n');
            obj.animatePhase(yumi, traj, size(traj.Q, 1)-10, size(traj.Q, 1), '退离');
        end
        
        function animatePhase(obj, yumi, traj, start_idx, end_idx, phase_name)
            % 动画显示轨迹的某个阶段
            
            for j = start_idx:end_idx
                % 更新机器人配置
                show(yumi, traj.Q(j, :), 'Parent', obj.axes_handle, 'PreservePlot', true, 'Frames', 'off');
                
                % 更新标题
                progress = (j - start_idx + 1) / (end_idx - start_idx + 1) * 100;
                title(obj.axes_handle, sprintf('%s - 进度: %.1f%%', phase_name, progress), ...
                      'FontSize', 12, 'FontWeight', 'bold');
                
                drawnow;
                pause(obj.animation_speed);
            end
        end
        
        function animatePhaseWithLego(obj, yumi, traj, start_idx, end_idx, lego_obj, phase_name)
            % 动画显示携带乐高积木的轨迹阶段
            
            for j = start_idx:end_idx
                % 更新机器人配置
                show(yumi, traj.Q(j, :), 'Parent', obj.axes_handle, 'PreservePlot', true, 'Frames', 'off');
                
                % 更新乐高积木位置（跟随末端执行器）
                if lego_obj.attached_to_gripper
                    ee_name = obj.getEndEffectorName(traj.arm);
                    T = getTransform(yumi, traj.Q(j, :), ee_name);
                    new_position = T(1:3, 4)' + [0, 0, -0.02]; % 稍微偏移以模拟夹持
                    
                    obj.updateLegoPosition(lego_obj, new_position);
                end
                
                % 更新标题
                progress = (j - start_idx + 1) / (end_idx - start_idx + 1) * 100;
                title(obj.axes_handle, sprintf('%s - 进度: %.1f%%', phase_name, progress), ...
                      'FontSize', 12, 'FontWeight', 'bold');
                
                drawnow;
                pause(obj.animation_speed);
            end
        end
        
        function animateGraspAction(obj, yumi, traj, lego_obj, phase_idx)
            % 动画显示抓取动作
            
            % 显示抓取效果
            set(lego_obj.surface, 'EdgeColor', 'red', 'LineWidth', 2);
            pause(0.5);
            
            % 标记为已抓取
            lego_obj.attached_to_gripper = true;
            set(lego_obj.surface, 'EdgeColor', 'green', 'LineWidth', 2);
            
            % 更新标签
            set(lego_obj.label, 'String', sprintf('%s%d-抓取', upper(lego_obj.arm(1)), lego_obj.index), ...
                               'Color', 'green', 'FontWeight', 'bold');
            
            fprintf('    ✓ 乐高积木已抓取\n');
        end
        
        function animatePlaceAction(obj, yumi, traj, lego_obj, task, phase_idx)
            % 动画显示放置动作
            
            % 计算目标位置
            target_pos = task.place_position;
            target_orientation = task.place_orientation;
            
            % 移动乐高积木到目标位置
            obj.updateLegoPosition(lego_obj, target_pos, target_orientation);
            
            % 显示放置效果
            set(lego_obj.surface, 'EdgeColor', 'blue', 'LineWidth', 2);
            pause(0.5);
            
            % 标记为已放置
            lego_obj.attached_to_gripper = false;
            set(lego_obj.surface, 'EdgeColor', 'black', 'LineWidth', 0.5);
            
            % 更新标签
            set(lego_obj.label, 'String', sprintf('B%02d-完成', task.id), ...
                               'Color', 'blue', 'FontWeight', 'bold');
            
            fprintf('    ✓ 乐高积木已放置到目标位置\n');
        end
        
        function updateLegoPosition(obj, lego_obj, new_position, new_orientation)
            % 更新乐高积木的位置和方向
            
            if nargin < 4
                new_orientation = lego_obj.orientation;
            end
            
            % 重新创建网格
            [X, Y, Z] = obj.createLegoMesh(lego_obj.dimensions, new_position, new_orientation);
            
            % 更新表面数据
            set(lego_obj.surface, 'XData', X, 'YData', Y, 'ZData', Z);
            
            % 更新标签位置
            set(lego_obj.label, 'Position', [new_position(1), new_position(2), new_position(3) + lego_obj.dimensions(3)/2 + 0.01]);
            
            % 更新对象属性
            lego_obj.position = new_position;
            lego_obj.orientation = new_orientation;
        end
        
        function lego_index = findLegoIndex(obj, arm, arm_lego_id)
            % 查找乐高积木索引
            
            lego_index = [];
            
            for i = 1:length(obj.lego_objects)
                lego_obj = obj.lego_objects{i};
                if strcmp(lego_obj.arm, arm) && lego_obj.index == arm_lego_id
                    lego_index = i;
                    break;
                end
            end
        end
        
        function ee_name = getEndEffectorName(obj, arm_name)
            % 获取末端执行器名称
            if strcmp(arm_name, 'right')
                ee_name = 'gripper_r_base';
            else
                ee_name = 'gripper_l_base';
            end
        end
        
        function animateTrajectoryOnly(obj, yumi, traj)
            % 仅显示轨迹动画（无乐高操作）
            
            for j = 1:size(traj.Q, 1)
                show(yumi, traj.Q(j, :), 'Parent', obj.axes_handle, 'PreservePlot', true, 'Frames', 'off');
                
                progress = j / size(traj.Q, 1) * 100;
                title(obj.axes_handle, sprintf('轨迹动画 - 进度: %.1f%%', progress), ...
                      'FontSize', 12, 'FontWeight', 'bold');
                
                drawnow;
                pause(obj.animation_speed);
            end
        end
        
        function showFinalResult(obj)
            % 显示最终结果
            
            title(obj.axes_handle, '🎉 YuMi机器人乐高堆叠完成！', ...
                  'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);
            
            % 统计信息
            completed_count = 0;
            for i = 1:length(obj.lego_objects)
                if ~obj.lego_objects{i}.attached_to_gripper
                    completed_count = completed_count + 1;
                end
            end
            
            fprintf('\n📊 堆叠统计:\n');
            fprintf('  总积木数: %d\n', length(obj.lego_objects));
            fprintf('  已完成: %d\n', completed_count);
            fprintf('  完成率: %.1f%%\n', completed_count / length(obj.lego_objects) * 100);
        end
    end
end

function value = getfield_default(s, field, default_value)
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end
