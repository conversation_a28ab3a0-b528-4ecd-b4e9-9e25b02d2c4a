function trajectories = AdvancedTrajectoryPlanner(yumi, brick_config, qHome, options)
% 改进的双臂机器人轨迹规划器
% 集成RRT路径规划、B-spline轨迹优化和双臂避碰协调
%
% 输入：
%   yumi - 机器人模型
%   brick_config - 乐高配置
%   qHome - 初始关节角
%   options - 规划选项（可选）
%
% 输出：
%   trajectories - 优化后的轨迹序列

fprintf('=== 启动改进的双臂轨迹规划系统 ===\n');

% 设置默认选项
if nargin < 4
    options = struct();
end

% 规划参数
planning_mode = getfield_default(options, 'planning_mode', 'advanced'); % 'basic', 'advanced', 'optimal'
coordination_mode = getfield_default(options, 'coordination_mode', 'adaptive'); % 'sequential', 'parallel', 'adaptive'
optimization_level = getfield_default(options, 'optimization_level', 'medium'); % 'low', 'medium', 'high'

fprintf('规划模式: %s\n', planning_mode);
fprintf('协调模式: %s\n', coordination_mode);
fprintf('优化级别: %s\n', optimization_level);

% 性能分析（可选）
if getfield_default(options, 'analyze_current', false)
    fprintf('\n--- 分析当前系统性能 ---\n');
    current_performance = analyzeCurrentPerformance(yumi, brick_config, qHome);
    fprintf('当前系统分析完成\n');
end

try
    % 根据规划模式选择算法
    switch planning_mode
        case 'basic'
            trajectories = planBasicTrajectories(yumi, brick_config, qHome, options);
        case 'advanced'
            trajectories = planAdvancedTrajectories(yumi, brick_config, qHome, options);
        case 'optimal'
            trajectories = planOptimalTrajectories(yumi, brick_config, qHome, options);
        otherwise
            error('未知的规划模式: %s', planning_mode);
    end
    
    % 验证轨迹质量
    trajectory_quality = validateTrajectoryQuality(trajectories, yumi);
    
    fprintf('\n=== 轨迹规划完成 ===\n');
    fprintf('生成轨迹数: %d\n', length(trajectories));
    fprintf('平均平滑度: %.4f\n', trajectory_quality.average_smoothness);
    fprintf('最大速度: %.4f rad/s\n', trajectory_quality.max_velocity);
    fprintf('碰撞检测通过率: %.1f%%\n', trajectory_quality.collision_free_rate * 100);
    
catch ME
    fprintf('轨迹规划失败: %s\n', ME.message);
    trajectories = {};
    rethrow(ME);
end

end

function trajectories = planBasicTrajectories(yumi, brick_config, qHome, options)
% 基础轨迹规划（改进的原始方法）

fprintf('使用基础轨迹规划方法...\n');

% 使用原始方法但添加基本优化
trajectories = planTrajectory(yumi, brick_config, qHome);

% 添加基本的轨迹优化
for i = 1:length(trajectories)
    % 改进平滑化
    trajectories{i}.Q_smooth = improveSmoothing(trajectories{i}.Q);
    
    % 添加时间信息
    trajectories{i}.time = linspace(0, 10, size(trajectories{i}.Q, 1));
    
    % 计算速度和加速度
    [trajectories{i}.velocity, trajectories{i}.acceleration] = computeKinematics(trajectories{i}.Q, trajectories{i}.time);
end

end

function trajectories = planAdvancedTrajectories(yumi, brick_config, qHome, options)
% 高级轨迹规划（RRT + B-spline + 基础协调）

fprintf('使用高级轨迹规划方法...\n');

% 配置协调器选项
coordinator_options = struct();
coordinator_options.coordination_mode = getfield_default(options, 'coordination_mode', 'adaptive');
coordinator_options.safety_margin = getfield_default(options, 'safety_margin', 0.08);

% RRT规划器选项
coordinator_options.rrt_options = struct();
coordinator_options.rrt_options.max_iterations = getfield_default(options, 'rrt_max_iterations', 3000);
coordinator_options.rrt_options.step_size = getfield_default(options, 'rrt_step_size', 0.1);
coordinator_options.rrt_options.goal_bias = getfield_default(options, 'rrt_goal_bias', 0.15);

% B-spline选项
coordinator_options.bspline_options = struct();
coordinator_options.bspline_options.degree = 3;
coordinator_options.bspline_options.max_velocity = getfield_default(options, 'max_velocity', 1.5);
coordinator_options.bspline_options.max_acceleration = getfield_default(options, 'max_acceleration', 3.0);

% 创建双臂协调器
coordinator = DualArmCoordinator(yumi, coordinator_options);

% 规划协调轨迹
trajectories = coordinator.planDualArmTrajectories(brick_config, qHome);

end

function trajectories = planOptimalTrajectories(yumi, brick_config, qHome, options)
% 最优轨迹规划（全局优化 + 高级协调）

fprintf('使用最优轨迹规划方法...\n');

% 首先使用高级方法获得初始解
initial_trajectories = planAdvancedTrajectories(yumi, brick_config, qHome, options);

% 全局优化
optimization_options = struct();
optimization_options.max_iterations = getfield_default(options, 'optimization_iterations', 100);
optimization_options.convergence_tolerance = getfield_default(options, 'convergence_tolerance', 1e-4);

trajectories = globalTrajectoryOptimization(initial_trajectories, yumi, optimization_options);

end

function Q_smooth = improveSmoothing(Q)
% 改进的轨迹平滑化

% 使用Savitzky-Golay滤波器
window_size = min(11, size(Q, 1));
if window_size < 5
    window_size = 5;
end
if mod(window_size, 2) == 0
    window_size = window_size - 1;
end

polynomial_order = min(3, window_size - 1);

Q_smooth = zeros(size(Q));
for j = 1:size(Q, 2)
    if size(Q, 1) >= window_size
        Q_smooth(:, j) = sgolayfilt(Q(:, j), polynomial_order, window_size);
    else
        Q_smooth(:, j) = Q(:, j);
    end
end

end

function [velocity, acceleration] = computeKinematics(Q, time)
% 计算轨迹的速度和加速度

if length(time) ~= size(Q, 1)
    error('时间向量长度与轨迹点数不匹配');
end

% 计算速度（数值微分）
velocity = zeros(size(Q));
for i = 2:size(Q, 1)-1
    dt1 = time(i) - time(i-1);
    dt2 = time(i+1) - time(i);
    
    % 使用中心差分
    velocity(i, :) = ((Q(i+1, :) - Q(i, :)) / dt2 + (Q(i, :) - Q(i-1, :)) / dt1) / 2;
end

% 边界点使用前向/后向差分
velocity(1, :) = (Q(2, :) - Q(1, :)) / (time(2) - time(1));
velocity(end, :) = (Q(end, :) - Q(end-1, :)) / (time(end) - time(end-1));

% 计算加速度
acceleration = zeros(size(Q));
for i = 2:size(velocity, 1)-1
    dt1 = time(i) - time(i-1);
    dt2 = time(i+1) - time(i);
    
    acceleration(i, :) = ((velocity(i+1, :) - velocity(i, :)) / dt2 + (velocity(i, :) - velocity(i-1, :)) / dt1) / 2;
end

acceleration(1, :) = (velocity(2, :) - velocity(1, :)) / (time(2) - time(1));
acceleration(end, :) = (velocity(end, :) - velocity(end-1, :)) / (time(end) - time(end-1));

end

function optimized_trajectories = globalTrajectoryOptimization(trajectories, yumi, options)
% 全局轨迹优化

fprintf('执行全局轨迹优化...\n');

optimized_trajectories = trajectories;

% 优化参数
max_iterations = getfield_default(options, 'max_iterations', 50);
convergence_tolerance = getfield_default(options, 'convergence_tolerance', 1e-4);

% 定义优化目标：最小化总执行时间和能耗
objective_function = @(x) computeGlobalObjective(x, trajectories, yumi);

% 当前解的参数化
x0 = parameterizeTrajectories(trajectories);

% 设置优化选项
opt_options = optimoptions('fmincon', ...
    'Display', 'iter', ...
    'MaxIterations', max_iterations, ...
    'ConstraintTolerance', convergence_tolerance, ...
    'OptimalityTolerance', convergence_tolerance);

try
    % 执行优化
    [x_opt, fval, exitflag] = fmincon(objective_function, x0, [], [], [], [], [], [], [], opt_options);
    
    if exitflag > 0
        % 重构优化后的轨迹
        optimized_trajectories = reconstructTrajectories(x_opt, trajectories);
        fprintf('全局优化成功，目标函数值: %.4f\n', fval);
    else
        fprintf('全局优化未收敛，使用原始轨迹\n');
    end
    
catch ME
    fprintf('全局优化失败: %s\n', ME.message);
end

end

function cost = computeGlobalObjective(x, trajectories, yumi)
% 计算全局优化目标函数

% 重构轨迹
temp_trajectories = reconstructTrajectories(x, trajectories);

cost = 0;

% 时间代价
total_time = 0;
for i = 1:length(temp_trajectories)
    if isfield(temp_trajectories{i}, 'time')
        total_time = total_time + temp_trajectories{i}.time(end);
    end
end
cost = cost + total_time;

% 平滑度代价
smoothness_cost = 0;
for i = 1:length(temp_trajectories)
    if isfield(temp_trajectories{i}, 'acceleration')
        smoothness_cost = smoothness_cost + sum(sum(temp_trajectories{i}.acceleration.^2));
    end
end
cost = cost + 0.1 * smoothness_cost;

% 能耗代价（简化为关节角变化的平方和）
energy_cost = 0;
for i = 1:length(temp_trajectories)
    if isfield(temp_trajectories{i}, 'Q')
        Q = temp_trajectories{i}.Q;
        energy_cost = energy_cost + sum(sum(diff(Q).^2));
    end
end
cost = cost + 0.01 * energy_cost;

end

function x = parameterizeTrajectories(trajectories)
% 参数化轨迹用于优化

x = [];
for i = 1:length(trajectories)
    if isfield(trajectories{i}, 'time') && length(trajectories{i}.time) > 1
        % 使用时间缩放因子作为优化变量
        x = [x; trajectories{i}.time(end)];
    else
        x = [x; 10.0]; % 默认时间
    end
end

end

function trajectories = reconstructTrajectories(x, original_trajectories)
% 从优化参数重构轨迹

trajectories = original_trajectories;

for i = 1:length(trajectories)
    if i <= length(x)
        new_duration = x(i);
        
        % 重新计算时间向量
        if isfield(trajectories{i}, 'Q')
            n_points = size(trajectories{i}.Q, 1);
            trajectories{i}.time = linspace(0, new_duration, n_points);
            
            % 重新计算速度和加速度
            [trajectories{i}.velocity, trajectories{i}.acceleration] = ...
                computeKinematics(trajectories{i}.Q, trajectories{i}.time);
        end
    end
end

end

function quality = validateTrajectoryQuality(trajectories, yumi)
% 验证轨迹质量

quality = struct();

if isempty(trajectories)
    quality.average_smoothness = inf;
    quality.max_velocity = inf;
    quality.collision_free_rate = 0;
    return;
end

% 计算平滑度指标
smoothness_values = [];
max_velocities = [];
collision_free_count = 0;

for i = 1:length(trajectories)
    traj = trajectories{i};
    
    % 平滑度（加速度变化率的RMS）
    if isfield(traj, 'acceleration') && size(traj.acceleration, 1) > 1
        jerk = diff(traj.acceleration);
        smoothness = sqrt(mean(sum(jerk.^2, 2)));
        smoothness_values = [smoothness_values; smoothness];
    end
    
    % 最大速度
    if isfield(traj, 'velocity')
        max_vel = max(sqrt(sum(traj.velocity.^2, 2)));
        max_velocities = [max_velocities; max_vel];
    end
    
    % 碰撞检测（简化）
    if isfield(traj, 'Q')
        collision_free = true;
        % 这里可以添加更详细的碰撞检测
        if collision_free
            collision_free_count = collision_free_count + 1;
        end
    end
end

quality.average_smoothness = mean(smoothness_values);
quality.max_velocity = max(max_velocities);
quality.collision_free_rate = collision_free_count / length(trajectories);

end

function value = getfield_default(s, field, default_value)
% 获取结构体字段，如果不存在则返回默认值
if isfield(s, field)
    value = s.(field);
else
    value = default_value;
end
end
