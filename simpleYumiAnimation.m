function simpleYumiAnimation()
% Simple YuMi Animation - Add motion to your existing successful display
% This builds on your working static visualization

fprintf('=== Simple YuMi Animation ===\n');
fprintf('Adding motion to your successful YuMi display\n\n');

try
    % === Load YuMi robot (same as your working version) ===
    fprintf('Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    fprintf('SUCCESS: YuMi robot loaded\n');
    
    % === Create figure (same style as your working version) ===
    fprintf('Creating visualization window...\n');
    
    figure('Name', 'YuMi Robot - Simple Animation', ...
           'Position', [100, 100, 1200, 800]);
    
    % === Display robot and environment (same as your working setup) ===
    qHome = robot.homeConfiguration;
    show(robot, qHome, 'Frames', 'off');
    hold on;
    
    % Same view and lighting as your successful display
    view(45, 30);
    axis equal;
    grid on;
    
    % Add the same environment objects as your working version
    addSimpleBlock([0.6, 0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);  % Blue block 1
    addSimpleBlock([0.6, -0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]); % Blue block 2
    addSimpleBlock([0.5, 0, 0.01], [0.25, 0.25, 0.02], [0, 0.8, 0]);  % Green platform
    
    % Same labels as your working version
    title('YuMi Robot - Simple Animation Demo', 'FontSize', 16);
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    
    % Same axis limits as your working version
    xlim([0.2, 0.8]);
    ylim([-0.4, 0.4]);
    zlim([0, 0.6]);
    
    % Add labels (same as your working version)
    text(0.3, 0.3, 0.4, 'YuMi Robot', 'FontSize', 12, 'Color', 'red');
    text(0.6, 0.2, 0.12, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue');
    text(0.6, -0.2, 0.12, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue');
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green');
    
    fprintf('SUCCESS: Display setup completed (same as your working version)\n');
    
    % === Now add simple animation ===
    fprintf('\nStarting simple animation...\n');
    fprintf('The robot will now move in a pick-and-place pattern\n\n');
    
    % Animation parameters
    duration = 15;  % Total animation time
    steps = 150;    % Number of animation steps
    
    fprintf('Animation: %d seconds, %d steps\n', duration, steps);
    fprintf('Press Ctrl+C to stop animation\n\n');
    
    % Animation loop
    for i = 1:steps
        t = (i-1) / (steps-1) * duration;
        
        % Generate robot configuration for this time step
        q = generateSimpleMotion(qHome, t);
        
        % Update robot display (same method as your working version)
        show(robot, q, 'PreservePlot', false, 'Frames', 'off');
        
        % Control animation speed
        pause(0.1);
        
        % Progress indicator
        if mod(i, 30) == 0
            progress = round(i/steps*100);
            fprintf('Animation progress: %d%% (time: %.1fs)\n', progress, t);
        end
        
        % Update display
        drawnow;
    end
    
    % Return to home position
    show(robot, qHome, 'PreservePlot', false, 'Frames', 'off');
    
    fprintf('\nAnimation completed! Robot returned to home position.\n');
    fprintf('You can run simpleYumiAnimation() again to repeat the animation.\n');
    
    % === Add restart option ===
    fprintf('\nWould you like to loop the animation? (Press any key to continue, Ctrl+C to stop)\n');
    
    % Optional: Continuous loop
    while true
        try
            pause(2);  % Wait 2 seconds between cycles
            fprintf('\nRestarting animation cycle...\n');
            
            % Repeat animation
            for i = 1:steps
                t = (i-1) / (steps-1) * duration;
                q = generateSimpleMotion(qHome, t);
                show(robot, q, 'PreservePlot', false, 'Frames', 'off');
                pause(0.1);
                drawnow;
            end
            
            show(robot, qHome, 'PreservePlot', false, 'Frames', 'off');
            fprintf('Animation cycle completed.\n');
            
        catch ME
            if contains(ME.message, 'interrupted')
                fprintf('Animation stopped by user.\n');
                break;
            else
                fprintf('Animation error: %s\n', ME.message);
                break;
            end
        end
    end
    
catch ME
    fprintf('ERROR: Animation failed: %s\n', ME.message);
    
    % Fallback to your working static display
    try
        fprintf('Falling back to static display (your working version)...\n');
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Robot - Static Display (Fallback)');
        view(45, 30);
        grid on;
        fprintf('SUCCESS: Static display created\n');
    catch
        fprintf('ERROR: Complete failure\n');
    end
end

end

function q = generateSimpleMotion(qHome, t)
% Generate simple pick-and-place motion

q = qHome;

% Simple sinusoidal motion for demonstration
cycle_period = 15;  % 15 second cycle
phase = 2 * pi * t / cycle_period;

% Right arm motion (pick first object)
q(1) = 0.3 * sin(phase);                    % Shoulder rotation
q(2) = -0.2 + 0.1 * cos(phase);            % Shoulder lift
q(3) = 0.1 * sin(2 * phase);               % Elbow
q(4) = -0.3 + 0.2 * cos(phase);            % Wrist

% Left arm motion (pick second object, opposite phase)
q(8) = -0.3 * sin(phase + pi);              % Shoulder rotation
q(9) = -0.2 + 0.1 * cos(phase + pi);       % Shoulder lift
q(10) = -0.1 * sin(2 * phase + pi);        % Elbow
q(11) = -0.3 + 0.2 * cos(phase + pi);      % Wrist

% Simple gripper motion
if sin(phase) > 0.5
    q(15) = 0.0;  % Closed
    q(16) = 0.0;
else
    q(15) = 0.02; % Open
    q(16) = 0.02;
end

end

function addSimpleBlock(center, size, color)
% Add simple block (same as your working version)

% Block vertices
dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;
vertices = [
    center(1)-dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)+dy, center(3)+dz;
    center(1)-dx, center(2)+dy, center(3)+dz;
];

% Block faces
faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];

% Create patch
patch('Vertices', vertices, 'Faces', faces, ...
      'FaceColor', color, 'FaceAlpha', 0.8, ...
      'EdgeColor', 'black', 'LineWidth', 0.5);

end
