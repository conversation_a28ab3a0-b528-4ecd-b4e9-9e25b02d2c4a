function simpleMechanicsExplorer()
% Simple Mechanics Explorer - Easy to use with play/pause controls
% Guaranteed to work with visible controls

fprintf('=== Simple Mechanics Explorer ===\n');
fprintf('Creating easy-to-use interface with play/pause controls\n\n');

try
    % === Load YuMi robot ===
    fprintf('Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    qHome = robot.homeConfiguration;
    fprintf('SUCCESS: YuMi robot loaded\n');
    
    % === Create main figure ===
    fprintf('Creating interface...\n');
    
    fig = figure('Name', 'Simple Mechanics Explorer - YuMi Robot', ...
                 'Position', [100, 100, 1200, 900], ...
                 'Color', [0.95, 0.95, 0.95]);
    
    % === Create control panel ===
    controlPanel = uipanel('Parent', fig, ...
                          'Position', [0.02, 0.02, 0.96, 0.12], ...
                          'Title', 'Animation Controls', ...
                          'FontSize', 12, ...
                          'BackgroundColor', 'white');
    
    % === Add control buttons ===
    
    % Play button
    playBtn = uicontrol('Parent', controlPanel, ...
                       'Style', 'pushbutton', ...
                       'String', '▶ PLAY', ...
                       'Position', [20, 20, 100, 40], ...
                       'FontSize', 14, ...
                       'FontWeight', 'bold', ...
                       'BackgroundColor', [0.2, 0.8, 0.2], ...
                       'ForegroundColor', 'white');
    
    % Pause button
    pauseBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', '⏸ PAUSE', ...
                        'Position', [130, 20, 100, 40], ...
                        'FontSize', 14, ...
                        'FontWeight', 'bold', ...
                        'BackgroundColor', [0.8, 0.6, 0.2], ...
                        'ForegroundColor', 'white');
    
    % Reset button
    resetBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', '⏹ RESET', ...
                        'Position', [240, 20, 100, 40], ...
                        'FontSize', 14, ...
                        'FontWeight', 'bold', ...
                        'BackgroundColor', [0.8, 0.2, 0.2], ...
                        'ForegroundColor', 'white');
    
    % Status display
    statusText = uicontrol('Parent', controlPanel, ...
                          'Style', 'text', ...
                          'String', 'Ready - Click PLAY to start animation', ...
                          'Position', [360, 35, 300, 25], ...
                          'FontSize', 12, ...
                          'FontWeight', 'bold', ...
                          'ForegroundColor', [0, 0.6, 0]);
    
    % Time display
    timeText = uicontrol('Parent', controlPanel, ...
                        'Style', 'text', ...
                        'String', 'Time: 0.0 s', ...
                        'Position', [680, 35, 100, 25], ...
                        'FontSize', 12);
    
    % Speed control
    uicontrol('Parent', controlPanel, ...
              'Style', 'text', ...
              'String', 'Speed:', ...
              'Position', [800, 45, 50, 20], ...
              'FontSize', 10);
    
    speedSlider = uicontrol('Parent', controlPanel, ...
                           'Style', 'slider', ...
                           'Position', [850, 45, 100, 20], ...
                           'Min', 0.5, 'Max', 3.0, 'Value', 1.0);
    
    % === Create 3D display area ===
    ax = axes('Parent', fig, ...
              'Position', [0.05, 0.2, 0.9, 0.75]);
    
    % === Display robot and environment ===
    fprintf('Setting up 3D scene...\n');
    
    % Show robot
    show(robot, qHome, 'Frames', 'off', 'Parent', ax);
    hold(ax, 'on');
    
    % Set view
    view(ax, 45, 30);
    axis(ax, 'equal');
    grid(ax, 'on');
    
    % Add environment
    addSimpleBlock(ax, [0.6, 0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    addSimpleBlock(ax, [0.6, -0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    addSimpleBlock(ax, [0.5, 0, 0.01], [0.25, 0.25, 0.02], [0, 0.8, 0]);
    
    % Labels
    title(ax, 'YuMi Robot - Pick and Place Animation', 'FontSize', 16);
    xlabel(ax, 'X (m)'); ylabel(ax, 'Y (m)'); zlabel(ax, 'Z (m)');
    xlim(ax, [0.2, 0.8]); ylim(ax, [-0.4, 0.4]); zlim(ax, [0, 0.6]);
    
    text(0.3, 0.3, 0.4, 'YuMi Robot', 'FontSize', 12, 'Color', 'red', 'Parent', ax);
    text(0.6, 0.2, 0.12, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue', 'Parent', ax);
    text(0.6, -0.2, 0.12, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue', 'Parent', ax);
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green', 'Parent', ax);
    
    fprintf('SUCCESS: Interface created!\n');
    
    % === Animation variables ===
    animData = struct();
    animData.isPlaying = false;
    animData.currentTime = 0;
    animData.totalTime = 10;
    animData.timer = [];
    animData.motionData = generateSimpleMotion(qHome, 100);
    
    % === Set up callbacks ===
    set(playBtn, 'Callback', @(src,evt) startAnimationCallback(src, evt, fig));
    set(pauseBtn, 'Callback', @(src,evt) pauseAnimationCallback(src, evt, fig));
    set(resetBtn, 'Callback', @(src,evt) resetAnimationCallback(src, evt, fig));
    
    fprintf('\n=== Controls Ready ===\n');
    fprintf('• Click PLAY to start animation\n');
    fprintf('• Click PAUSE to pause animation\n');
    fprintf('• Click RESET to return to start\n');
    fprintf('• Adjust speed slider to change animation speed\n\n');
    
    % === Store data for callbacks ===
    setappdata(fig, 'animData', animData);
    setappdata(fig, 'robot', robot);
    setappdata(fig, 'qHome', qHome);
    setappdata(fig, 'ax', ax);
    setappdata(fig, 'playBtn', playBtn);
    setappdata(fig, 'statusText', statusText);
    setappdata(fig, 'timeText', timeText);
    setappdata(fig, 'speedSlider', speedSlider);
    
    fprintf('Simple Mechanics Explorer is ready!\n');
    fprintf('Click the PLAY button to see the robot move!\n');
    
catch ME
    fprintf('ERROR: %s\n', ME.message);
    
    % Fallback
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Robot - Fallback Display');
        fprintf('Fallback display created\n');
    catch
        fprintf('Complete failure\n');
    end

function startAnimationCallback(~, ~, fig)
% Start animation callback

animData = getappdata(fig, 'animData');
if ~animData.isPlaying
    animData.isPlaying = true;

    % Get UI elements
    playBtn = getappdata(fig, 'playBtn');
    statusText = getappdata(fig, 'statusText');

    % Update UI
    set(playBtn, 'String', '⏸ PLAYING', 'BackgroundColor', [0.8, 0.6, 0.2]);
    set(statusText, 'String', 'Animation PLAYING - Robot is moving!', 'ForegroundColor', [0, 0.6, 0]);

    % Start timer
    animData.timer = timer('ExecutionMode', 'fixedRate', ...
                          'Period', 0.1, ...
                          'TimerFcn', @(~,~) updateAnimationCallback(fig));
    start(animData.timer);

    % Save data
    setappdata(fig, 'animData', animData);

    fprintf('Animation STARTED!\n');
end

end

function pauseAnimationCallback(~, ~, fig)
% Pause animation callback

animData = getappdata(fig, 'animData');
if animData.isPlaying
    animData.isPlaying = false;

    % Stop timer
    if ~isempty(animData.timer) && isvalid(animData.timer)
        stop(animData.timer);
        delete(animData.timer);
        animData.timer = [];
    end

    % Get UI elements
    playBtn = getappdata(fig, 'playBtn');
    statusText = getappdata(fig, 'statusText');

    % Update UI
    set(playBtn, 'String', '▶ PLAY', 'BackgroundColor', [0.2, 0.8, 0.2]);
    set(statusText, 'String', 'Animation PAUSED - Click PLAY to continue', 'ForegroundColor', [0.8, 0.6, 0]);

    % Save data
    setappdata(fig, 'animData', animData);

    fprintf('Animation PAUSED\n');
end

end

function resetAnimationCallback(~, ~, fig)
% Reset animation callback

animData = getappdata(fig, 'animData');

% Stop if playing
if animData.isPlaying
    pauseAnimationCallback([], [], fig);
    animData = getappdata(fig, 'animData');  % Get updated data
end

% Reset time
animData.currentTime = 0;

% Get components
robot = getappdata(fig, 'robot');
qHome = getappdata(fig, 'qHome');
ax = getappdata(fig, 'ax');
statusText = getappdata(fig, 'statusText');
timeText = getappdata(fig, 'timeText');

% Reset robot
show(robot, qHome, 'PreservePlot', false, 'Frames', 'off', 'Parent', ax);

% Update UI
set(statusText, 'String', 'Animation RESET - Ready to play', 'ForegroundColor', [0, 0.6, 0]);
set(timeText, 'String', 'Time: 0.0 s');

% Save data
setappdata(fig, 'animData', animData);

fprintf('Animation RESET\n');

end

function updateAnimationCallback(fig)
% Update animation callback

try
    % Get data
    animData = getappdata(fig, 'animData');
    robot = getappdata(fig, 'robot');
    ax = getappdata(fig, 'ax');
    speedSlider = getappdata(fig, 'speedSlider');
    timeText = getappdata(fig, 'timeText');

    % Get speed
    speed = get(speedSlider, 'Value');

    % Update time
    animData.currentTime = animData.currentTime + 0.1 * speed;

    % Loop animation
    if animData.currentTime >= animData.totalTime
        animData.currentTime = 0;
    end

    % Calculate frame
    frameIndex = round((animData.currentTime / animData.totalTime) * size(animData.motionData, 2)) + 1;
    frameIndex = min(frameIndex, size(animData.motionData, 2));

    % Update robot
    q = animData.motionData(:, frameIndex);
    show(robot, q, 'PreservePlot', false, 'Frames', 'off', 'Parent', ax);

    % Update time display
    set(timeText, 'String', sprintf('Time: %.1f s', animData.currentTime));

    % Save data
    setappdata(fig, 'animData', animData);

    drawnow;

catch ME
    fprintf('Animation error: %s\n', ME.message);
    pauseAnimationCallback([], [], fig);
end

end
end

end

function motionData = generateSimpleMotion(qHome, numFrames)
% Generate simple motion sequence

motionData = zeros(length(qHome), numFrames);

for i = 1:numFrames
    t = (i-1) / (numFrames-1) * 2 * pi;
    
    q = qHome;
    
    % Simple sinusoidal motion
    q(1) = 0.3 * sin(t);
    q(2) = -0.2 + 0.1 * cos(t);
    q(8) = -0.3 * sin(t + pi);
    q(9) = -0.2 + 0.1 * cos(t + pi);
    
    motionData(:, i) = q;
end

end

function addSimpleBlock(ax, center, size, color)
% Add block to axes

dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;

vertices = [
    center(1)-dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)+dy, center(3)+dz;
    center(1)-dx, center(2)+dy, center(3)+dz;
];

faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];

patch('Vertices', vertices, 'Faces', faces, ...
      'FaceColor', color, 'FaceAlpha', 0.8, ...
      'EdgeColor', 'black', 'LineWidth', 0.5, ...
      'Parent', ax);

end
