% Final, Self-Contained Main Script for Lego Assembly

% Clear everything and set up the environment
clear;
clc;
addpath(pwd); % Ensure all functions in the directory are available

fprintf('====== Lego Assembly Simulation - Final, Self-Contained Run ======\n');

% --- Stage 1: Parse LDR and Create Task Sequence ---
bricks = parse_ldr('mainbu.ldr');
task_sequence = create_task_sequence(bricks);
fprintf('LDR parsed and task sequence created: %d tasks.\n', length(task_sequence));

% --- Stage 2: Initialize Planners and Robot ---
yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
qHome = yumi.homeConfiguration;
collision_checker = CollisionChecker(yumi);
rrt_planner = RRTPlanner(yumi, collision_checker);
bspline_generator = BSplineTrajectory();

% --- Stage 3: Generate Trajectories for Each Task, Manually ---
fprintf('\n--- Stage 3: Generating All Trajectories, Manually ---\n');
all_trajectories = {};

for i = 1:length(task_sequence)
    task = task_sequence(i);
    fprintf('Processing Task %d/%d (%s arm)...\n', i, length(task_sequence), task.arm);

    try
        % 1. Get Waypoints using Inverse Kinematics
        waypoints = get_waypoints_for_task(yumi, task, qHome);
        if isempty(waypoints), continue; end
        
        % 2. Plan Path using RRT
        path = plan_path_with_rrt(rrt_planner, waypoints, task.arm, qHome);
        if isempty(path), continue; end
        
        % 3. Generate Smooth Trajectory using B-Spline
        [trajectory, success] = bspline_generator.generateTrajectory(path, 10.0); % 10s duration
        if ~success, continue; end
        
        trajectory.arm = task.arm;
        all_trajectories{end+1} = trajectory;
        fprintf('  -> Trajectory for task %d generated successfully.\n', i);

    catch ME
        fprintf('  -> FAILED to process task %d: %s\n', i, ME.message);
    end
end

if isempty(all_trajectories), error('No trajectories were generated.'); end

% --- Stage 4: Save Final Trajectories ---
save('final_assembly_trajectories.mat', 'all_trajectories');
fprintf('\n\n✅ --- All %d Trajectories Generated and Saved --- ✅\n', length(all_trajectories));
fprintf('You can now use final_assembly_trajectories.mat to drive the Simulink model.\n');


% --- Helper Functions ---

function [bricks] = parse_ldr(file_path)
    fileID = fopen(file_path, 'r');
    if fileID == -1, error('Could not open LDR file.'); end
    bricks = struct('part_name', {}, 'position', {}, 'orientation', {});
    while ~feof(fileID)
        line = fgetl(fileID);
        parts = strsplit(strtrim(line));
        if ~isempty(parts) && strcmp(parts{1}, '1') && length(parts) == 15
            numeric_parts = cellfun(@str2double, parts(2:14));
            brick.position = numeric_parts(2:4)';
            brick.orientation = [numeric_parts(5:7); numeric_parts(8:10); numeric_parts(11:13)]';
            brick.part_name = parts{15};
            bricks(end+1) = brick;
        end
    end
    fclose(fileID);
end

function [task_sequence] = create_task_sequence(bricks)
    [~, sort_order] = sort(arrayfun(@(b) b.position(3), bricks));
    sorted_bricks = bricks(sort_order);
    task_sequence = struct('brick_index', {}, 'arm', {}, 'pick_pose', {}, 'place_pose', {});
    for i = 1:length(sorted_bricks)
        brick = sorted_bricks(i);
        task.arm = 'right';
        if brick.position(1) < 0, task.arm = 'left'; end
        task.pick_pose.position = [0.2, 0.4, 0.1];
        if strcmp(task.arm, 'left'), task.pick_pose.position = [-0.2, 0.4, 0.1]; end
        task.pick_pose.orientation = eye(3);
        task.place_pose.position = brick.position / 1000;
        task.place_pose.orientation = brick.orientation;
        task.brick_index = i;
        task_sequence(i) = task;
    end
end

function waypoints = get_waypoints_for_task(robot, task, qHome)
    % This function now correctly returns 7-DOF waypoints for the ACTIVE arm only.
    arm_joints = 1:7;
    ee_name = 'gripper_r_base';
    if strcmp(task.arm, 'left')
        arm_joints = 8:14;
        ee_name = 'gripper_l_base';
    end
    
    ik = inverseKinematics('RigidBodyTree', robot);
    weights = [0.1, 0.1, 0.1, 1, 1, 1];
    q_initial_arm = qHome(arm_joints);

    T_pick = rt2tr(task.pick_pose.orientation, task.pick_pose.position);
    T_place = rt2tr(task.place_pose.orientation, task.place_pose.position);

    q_pick_arm = ik(ee_name, T_pick, weights, q_initial_arm);
    q_place_arm = ik(ee_name, T_place, weights, q_pick_arm);
    
    waypoints = [q_initial_arm; q_pick_arm; q_place_arm];
end

function path = plan_path_with_rrt(rrt_planner, waypoints, arm, ~) % qHome is no longer needed here
    % This function now correctly passes the 7-DOF waypoints directly to the RRT planner.
    path = waypoints(1,:);
    for i = 1:size(waypoints, 1)-1
        [segment, success] = rrt_planner.planPath(waypoints(i,:), waypoints(i+1,:), arm);
        if ~success
            path = []; 
            fprintf('    -> RRT segment failed.\n');
            return; 
        end
        path = [path; segment(2:end,:)];
    end
end

function T = rt2tr(R, t)
    T = eye(4);
    T(1:3, 1:3) = R;
    T(1:3, 4) = t;
end
