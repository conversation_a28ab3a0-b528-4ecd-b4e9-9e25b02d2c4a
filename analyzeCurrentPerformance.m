function performance_report = analyzeCurrentPerformance(yumi, brick_config, qHome)
% 分析当前轨迹规划算法的性能
% 输入：
%   yumi - 机器人模型
%   brick_config - 乐高配置
%   qHome - 初始关节角
% 输出：
%   performance_report - 性能分析报告

fprintf('=== 开始分析当前轨迹规划性能 ===\n');

% 初始化性能指标
performance_report = struct();
performance_report.timestamp = datetime('now');
performance_report.total_tasks = length(brick_config.task_sequence);

% 性能计时
tic;

try
    % 调用原始轨迹规划算法
    trajectories = planTrajectory(yumi, brick_config, qHome);
    
    planning_time = toc;
    performance_report.planning_time = planning_time;
    performance_report.success = true;
    performance_report.num_trajectories = length(trajectories);
    
    fprintf('✓ 轨迹规划成功完成\n');
    fprintf('  规划时间: %.2f 秒\n', planning_time);
    fprintf('  生成轨迹数: %d\n', length(trajectories));
    
    % 分析每个轨迹的质量
    trajectory_analysis = [];
    
    for i = 1:length(trajectories)
        traj = trajectories{i};
        analysis = struct();
        
        % 基本信息
        analysis.arm = traj.arm;
        analysis.num_points = size(traj.Q, 1);
        analysis.num_joints = size(traj.Q, 2);
        
        % 计算轨迹平滑度指标
        if size(traj.Q, 1) > 2
            % 计算关节角速度（数值微分）
            dt = 0.1; % 假设时间步长
            vel = diff(traj.Q) / dt;
            
            % 计算关节角加速度
            if size(vel, 1) > 1
                acc = diff(vel) / dt;
                
                % 平滑度指标：加速度变化率的RMS
                if size(acc, 1) > 1
                    jerk = diff(acc) / dt;
                    analysis.smoothness_rms = sqrt(mean(sum(jerk.^2, 2)));
                else
                    analysis.smoothness_rms = 0;
                end
                
                % 最大加速度
                analysis.max_acceleration = max(sqrt(sum(acc.^2, 2)));
            else
                analysis.smoothness_rms = 0;
                analysis.max_acceleration = 0;
            end
            
            % 最大速度
            analysis.max_velocity = max(sqrt(sum(vel.^2, 2)));
        else
            analysis.smoothness_rms = 0;
            analysis.max_velocity = 0;
            analysis.max_acceleration = 0;
        end
        
        % 检查关节限制违反
        joint_limits_violated = false;
        if isfield(yumi, 'PositionLimits')
            for j = 1:size(traj.Q, 2)
                if any(traj.Q(:,j) < yumi.PositionLimits(j,1)) || ...
                   any(traj.Q(:,j) > yumi.PositionLimits(j,2))
                    joint_limits_violated = true;
                    break;
                end
            end
        end
        analysis.joint_limits_violated = joint_limits_violated;
        
        % 检查轨迹连续性（相邻段之间的跳跃）
        if i > 1
            prev_traj = trajectories{i-1};
            if strcmp(traj.arm, prev_traj.arm)
                % 同一手臂的连续轨迹
                position_jump = norm(traj.Q(1,:) - prev_traj.Q(end,:));
                analysis.position_continuity_error = position_jump;
            else
                analysis.position_continuity_error = 0;
            end
        else
            analysis.position_continuity_error = 0;
        end
        
        trajectory_analysis = [trajectory_analysis; analysis];
    end
    
    performance_report.trajectory_analysis = trajectory_analysis;
    
    % 计算总体性能指标
    all_smoothness = [trajectory_analysis.smoothness_rms];
    all_max_vel = [trajectory_analysis.max_velocity];
    all_max_acc = [trajectory_analysis.max_acceleration];
    continuity_errors = [trajectory_analysis.position_continuity_error];
    
    performance_report.overall_smoothness = mean(all_smoothness);
    performance_report.overall_max_velocity = max(all_max_vel);
    performance_report.overall_max_acceleration = max(all_max_acc);
    performance_report.max_continuity_error = max(continuity_errors);
    performance_report.joint_violations = sum([trajectory_analysis.joint_limits_violated]);
    
    % 分析双臂协调问题
    right_trajectories = find(strcmp({trajectory_analysis.arm}, 'right'));
    left_trajectories = find(strcmp({trajectory_analysis.arm}, 'left'));
    
    performance_report.right_arm_tasks = length(right_trajectories);
    performance_report.left_arm_tasks = length(left_trajectories);
    performance_report.coordination_analysis = 'No collision detection implemented';
    
    fprintf('\n=== 性能分析结果 ===\n');
    fprintf('总体平滑度 (RMS): %.4f\n', performance_report.overall_smoothness);
    fprintf('最大速度: %.4f rad/s\n', performance_report.overall_max_velocity);
    fprintf('最大加速度: %.4f rad/s²\n', performance_report.overall_max_acceleration);
    fprintf('最大连续性误差: %.4f rad\n', performance_report.max_continuity_error);
    fprintf('关节限制违反次数: %d\n', performance_report.joint_violations);
    fprintf('右臂任务数: %d\n', performance_report.right_arm_tasks);
    fprintf('左臂任务数: %d\n', performance_report.left_arm_tasks);
    
catch ME
    performance_report.success = false;
    performance_report.error_message = ME.message;
    performance_report.planning_time = toc;
    
    fprintf('✗ 轨迹规划失败: %s\n', ME.message);
end

% 保存分析结果
save('current_performance_analysis.mat', 'performance_report');
fprintf('\n性能分析完成，结果已保存到 current_performance_analysis.mat\n');

end
