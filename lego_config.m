
function config = lego_config()
% Loads the complete Lego assembly configuration from the parsed LDR data.

fprintf('🏰 === Loading Lego Castle Configuration from Parsed LDR Data === 🏰\n');

% Load the pre-parsed LDR data
load('full_lego_assembly_config.mat', 'bricks', 'task_sequence');

% Basic parameters
config.center = [0.5, 0, 0.05];
config.brick_length = 0.0318;
config.brick_width = 0.0159;
config.brick_height = 0.0096;

% --- Provide the data in the format expected by the planner ---

% 1. Full Task Sequence
config.task_sequence = task_sequence;

% 2. All Target Poses (for collision checking and coordination)
num_bricks = length(bricks);
all_targets = zeros(num_bricks, 6); % [x, y, z, roll, pitch, yaw]
for i = 1:num_bricks
    all_targets(i, 1:3) = bricks(i).position' / 1000; % Convert to meters
    % Note: Orientation is a rotation matrix, converting to rpy is complex.
    % The planner likely expects this format or handles matrices directly.
    % For simplicity, we'll pass the position data.
end
config.all_targets = all_targets;

% 3. Brick lists for each arm (might still be used by some parts)
left_bricks = [];
right_bricks = [];
for i = 1:length(task_sequence)
    task = task_sequence(i);
    if strcmp(task.arm, 'left')
        left_bricks = [left_bricks; [task.place_pose.position', task.pick_pose.position']];
    else
        right_bricks = [right_bricks; [task.place_pose.position', task.pick_pose.position']];
    end
end
config.left_arm_bricks = left_bricks;
config.right_arm_bricks = right_bricks;


fprintf('\n🏰 === Castle Configuration Loaded Successfully === 🏰\n');
fprintf('Total bricks: %d\n', num_bricks);
fprintf('Total tasks in sequence: %d\n', length(config.task_sequence));
fprintf('✅ Configuration is ready for the AdvancedTrajectoryPlanner.\n');

end