function runVisualYumiSystem()
% 启动可视化YuMi乐高拼接系统
% 真正能看到机械臂运动的版本

fprintf('🎬 === 启动可视化YuMi乐高拼接系统 === 🎬\n');
fprintf('这次你真的能看到机械臂运动！\n\n');

try
    %% 环境检查
    fprintf('🔍 环境检查...\n');
    
    % 检查必要文件
    required_files = {'mainbu.ldr', 'LDRParser.m', 'VisualYumiAssembly.m'};
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            error('缺少必要文件: %s', required_files{i});
        end
        fprintf('  ✅ %s 存在\n', required_files{i});
    end
    
    % 检查工具箱
    if ~exist('loadrobot', 'file')
        error('需要Robotics System Toolbox');
    end
    fprintf('  ✅ Robotics System Toolbox 可用\n');
    
    %% 创建可视化系统
    fprintf('\n🎬 创建可视化YuMi拼接系统...\n');
    
    visual_system = VisualYumiAssembly();
    
    %% 显示系统信息
    fprintf('\n📊 === 可视化系统信息 === 📊\n');
    
    fprintf('🎬 可视化特点:\n');
    fprintf('   - 真正的机械臂运动动画\n');
    fprintf('   - 实时关节配置更新\n');
    fprintf('   - 可见的运动轨迹\n');
    fprintf('   - 平滑的运动插值\n');
    
    fprintf('\n🤖 YuMi机器人:\n');
    fprintf('   - 双臂结构: 7+7 自由度\n');
    fprintf('   - 实时关节角度更新\n');
    fprintf('   - 可视化运动轨迹\n');
    fprintf('   - 真实的机械臂动作\n');
    
    fprintf('\n🏗️ 建筑信息:\n');
    fprintf('   - 设计文件: mainbu.ldr\n');
    fprintf('   - 积木总数: %d 个\n', length(visual_system.target_bricks));
    fprintf('   - 拼接步骤: %d 步\n', visual_system.total_steps);
    
    fprintf('\n🎯 可视化功能:\n');
    fprintf('   ✅ 机械臂实际运动\n');
    fprintf('   ✅ 关节角度实时更新\n');
    fprintf('   ✅ 运动轨迹可视化\n');
    fprintf('   ✅ 平滑运动插值\n');
    fprintf('   ✅ 左右臂分工动画\n');
    fprintf('   ✅ 积木逐步拼接\n');
    
    fprintf('\n🎮 操作说明:\n');
    fprintf('   ▶️  点击"开始拼接" - 启动可视化拼接动画\n');
    fprintf('   ⏸️  点击"暂停" - 暂停当前动画\n');
    fprintf('   ⏹️  点击"停止" - 停止并重置\n');
    fprintf('   🤖 点击"测试左臂运动" - 单独测试左臂\n');
    fprintf('   🤖 点击"测试右臂运动" - 单独测试右臂\n');
    fprintf('   📊 拖动进度条 - 跳转到任意步骤\n');
    
    fprintf('\n🔧 运动原理:\n');
    fprintf('   - 关节角度插值计算\n');
    fprintf('   - 实时机器人显示更新\n');
    fprintf('   - 平滑运动轨迹生成\n');
    fprintf('   - 5%增量运动步进\n');
    fprintf('   - 20步完成一个运动\n');
    
    fprintf('\n🎬 动画流程:\n');
    fprintf('   每个拼接步骤包含5个阶段:\n');
    fprintf('   1. 移动到拾取位置 ← 🔄 可见的机械臂运动\n');
    fprintf('   2. 夹爪关闭抓取   ← 🤏 夹爪动作\n');
    fprintf('   3. 移动到放置位置 ← 🔄 可见的机械臂运动\n');
    fprintf('   4. 夹爪开启放置   ← 🤏 夹爪动作\n');
    fprintf('   5. 返回初始位置   ← 🔄 可见的机械臂运动\n');
    
    fprintf('\n🎯 运动特点:\n');
    fprintf('   - 每个运动分为20个子步骤\n');
    fprintf('   - 5%%增量平滑插值\n');
    fprintf('   - 实时关节配置更新\n');
    fprintf('   - 机器人显示实时刷新\n');
    fprintf('   - 可见的运动轨迹\n');
    
    fprintf('\n🎉 === 可视化系统启动完成 === 🎉\n');
    fprintf('💡 现在你可以看到:\n');
    fprintf('   - 🤖 完整的YuMi双臂机器人\n');
    fprintf('   - 🔄 真正的机械臂运动动画\n');
    fprintf('   - 🎯 可见的运动轨迹\n');
    fprintf('   - 🤏 左右臂分工协作\n');
    fprintf('   - 🔴 积木的逐步放置过程\n');
    
    fprintf('\n🚀 使用建议:\n');
    fprintf('1. 先点击"测试左臂运动"或"测试右臂运动"看看效果\n');
    fprintf('2. 确认能看到机械臂运动后，点击"开始拼接"\n');
    fprintf('3. 观察左右臂轮流工作的动画过程\n');
    fprintf('4. 注意积木逐个变红的拼接效果\n');
    
    fprintf('\n📋 === 预期效果 === 📋\n');
    fprintf('✅ 机械臂会实际运动（不再是静止的）\n');
    fprintf('✅ 可以看到关节角度的变化\n');
    fprintf('✅ 运动轨迹清晰可见\n');
    fprintf('✅ 左右臂轮流工作动画\n');
    fprintf('✅ 积木逐个变红显示\n');
    fprintf('✅ 完整的50步拼接过程\n');
    fprintf('✅ 流畅的运动动画效果\n');
    
    fprintf('\n🎬 立即测试机械臂运动:\n');
    fprintf('点击"测试左臂运动"或"测试右臂运动"按钮！\n');
    
catch ME
    fprintf('❌ 可视化系统启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    fprintf('\n🔧 故障排除:\n');
    fprintf('1. 确保安装了Robotics System Toolbox\n');
    fprintf('2. 确保mainbu.ldr和LDRParser.m文件存在\n');
    fprintf('3. 检查MATLAB版本兼容性\n');
    fprintf('4. 尝试重启MATLAB\n');
    
    fprintf('\n💡 如果仍有问题，可以尝试:\n');
    fprintf('   - 关闭其他MATLAB图形窗口\n');
    fprintf('   - 清理工作空间: clear all; close all\n');
    fprintf('   - 重新运行: runVisualYumiSystem\n');
end

end
