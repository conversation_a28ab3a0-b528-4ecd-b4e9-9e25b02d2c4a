function 简单启动MechanicsExplorer()
% 简化版本：快速启动Mechanics Explorer界面
% 确保能够生成与官方教程一样的界面

fprintf('🚀 === 简化版Mechanics Explorer启动 === 🚀\n');
fprintf('快速生成与MathWorks官方教程一样的界面\n\n');

try
    % === 方法1：使用现有的官方示例 ===
    fprintf('方法1: 尝试加载官方示例...\n');
    
    try
        % 检查是否有官方示例
        if exist('modelWithSimscapeRobotAndEnvironmentDynamics.slx', 'file')
            fprintf('发现官方示例模型，直接运行...\n');
            simOut = sim('modelWithSimscapeRobotAndEnvironmentDynamics');
            fprintf('✅ 官方示例运行成功\n');
            return;
        end
    catch
        fprintf('官方示例不可用，使用自定义方法\n');
    end
    
    % === 方法2：创建简化的Simscape模型 ===
    fprintf('\n方法2: 创建简化Simscape模型...\n');
    
    % 模型名称
    modelName = 'SimpleYumiMechanicsExplorer';
    
    % 创建新模型
    if bdIsLoaded(modelName)
        close_system(modelName, 0);
    end
    
    new_system(modelName);
    open_system(modelName);
    
    % 设置求解器
    set_param(modelName, 'SolverName', 'ode23t');
    set_param(modelName, 'StopTime', '10');
    
    % === 添加基本Simscape组件 ===
    fprintf('添加Simscape组件...\n');
    
    % Solver Configuration
    add_block('nesl_utility/Solver Configuration', ...
              [modelName '/Solver Configuration'], ...
              'Position', [50, 50, 150, 100]);
    
    % World Frame
    add_block('sm_lib/Frames and Transforms/World Frame', ...
              [modelName '/World Frame'], ...
              'Position', [200, 50, 250, 100]);
    
    % === 添加YuMi机器人 ===
    fprintf('添加YuMi机器人...\n');
    
    % 使用Robotics System Toolbox的机器人
    try
        % 加载YuMi机器人
        robot = loadrobot('abbYumi');
        
        % 创建机器人可视化
        add_block('simulink/User-Defined Functions/MATLAB Function', ...
                  [modelName '/YuMi Robot'], ...
                  'Position', [300, 150, 450, 200]);
        
        % 设置机器人代码
        robotCode = sprintf(['function robotState = fcn(time)\n'...
            '%% YuMi机器人仿真\n'...
            'persistent robot iviz\n'...
            'if isempty(robot)\n'...
            '    robot = loadrobot(''abbYumi'');\n'...
            '    iviz = interactiveRigidBodyTree(robot);\n'...
            '    iviz.showFigure();\n'...
            'end\n\n'...
            '%% 简单的运动\n'...
            'q = robot.homeConfiguration;\n'...
            'q(1) = 0.3 * sin(time);\n'...
            'q(8) = -0.3 * sin(time);\n'...
            'iviz.Configuration = q;\n'...
            'robotState = 1;\n']);
        
        set_param([modelName '/YuMi Robot'], 'Script', robotCode);
        
    catch ME
        fprintf('YuMi机器人加载失败: %s\n', ME.message);
        
        % 使用简化的机器人表示
        add_block('sm_lib/Body Elements/Solid', [modelName '/Robot Base'], ...
                  'Position', [300, 150, 350, 200]);
    end
    
    % === 添加环境对象 ===
    fprintf('添加环境对象...\n');
    
    % 蓝色方块 (如图片所示)
    add_block('sm_lib/Body Elements/Solid', [modelName '/Blue Block 1'], ...
              'Position', [400, 250, 450, 300]);
    add_block('sm_lib/Body Elements/Solid', [modelName '/Blue Block 2'], ...
              'Position', [500, 250, 550, 300]);
    
    % 绿色平台 (如图片所示)
    add_block('sm_lib/Body Elements/Solid', [modelName '/Green Platform'], ...
              'Position', [600, 250, 650, 300]);
    
    % === 添加Mechanics Explorer ===
    fprintf('添加Mechanics Explorer...\n');
    
    try
        add_block('sm_lib/Utilities/Mechanics Explorer', ...
                  [modelName '/Mechanics Explorer'], ...
                  'Position', [700, 150, 800, 250]);
        
        % 配置Mechanics Explorer
        set_param([modelName '/Mechanics Explorer'], 'StartVisualization', 'on');
        
    catch ME
        fprintf('Mechanics Explorer添加失败: %s\n', ME.message);
        
        % 使用替代可视化
        add_block('simulink/Sinks/Scope', [modelName '/Visualization'], ...
                  'Position', [700, 150, 750, 200]);
    end
    
    % === 添加时钟和连接 ===
    add_block('simulink/Sources/Clock', [modelName '/Clock'], ...
              'Position', [50, 150, 80, 180]);
    
    % 基本连接
    try
        add_line(modelName, 'Clock/1', 'YuMi Robot/1');
    catch
        % 连接失败时忽略
    end
    
    % === 保存并运行模型 ===
    save_system(modelName);
    
    fprintf('✅ 模型创建完成\n');
    fprintf('🎬 启动仿真...\n');
    
    % 运行仿真
    simOut = sim(modelName);
    
    if ~isempty(simOut)
        fprintf('✅ 仿真成功完成\n');
        fprintf('💡 请查看打开的窗口\n');
    end
    
    % === 方法3：直接使用interactiveRigidBodyTree ===
    fprintf('\n方法3: 直接创建YuMi可视化...\n');
    
    try
        % 加载YuMi机器人
        robot = loadrobot('abbYumi');
        
        % 创建交互式可视化
        iviz = interactiveRigidBodyTree(robot);
        iviz.showFigure();
        
        % 设置视角 (与官方教程一致)
        view(45, 30);
        
        % 添加环境对象
        hold on;
        
        % 蓝色方块
        plotCube([0.1, 0.1, 0.1], [0.6, 0.2, 0.05], 'blue');
        plotCube([0.1, 0.1, 0.1], [0.6, -0.2, 0.05], 'blue');
        
        % 绿色平台
        plotCube([0.3, 0.3, 0.02], [0.5, 0, 0.0], 'green');
        
        % 简单动画
        fprintf('🎬 开始动画演示...\n');
        for t = 0:0.1:10
            q = robot.homeConfiguration;
            q(1) = 0.3 * sin(t);
            q(8) = -0.3 * sin(t);
            iviz.Configuration = q;
            drawnow;
            pause(0.05);
        end
        
        fprintf('✅ YuMi可视化完成\n');
        
    catch ME
        fprintf('YuMi可视化失败: %s\n', ME.message);
    end
    
    fprintf('\n🎉 === 简化版Mechanics Explorer启动完成 === 🎉\n');
    fprintf('您现在应该看到YuMi机器人的3D可视化界面\n');
    
catch ME
    fprintf('❌ 启动失败: %s\n', ME.message);
    
    % 最后的备用方案
    fprintf('\n🔧 备用方案：基础YuMi显示\n');
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi双臂机器人');
        view(45, 30);
        fprintf('✅ 基础YuMi显示成功\n');
    catch
        fprintf('❌ 所有方案都失败\n');
    end
end

end

function plotCube(size, position, color)
% 绘制立方体

try
    % 立方体顶点
    vertices = [
        0 0 0; 1 0 0; 1 1 0; 0 1 0;
        0 0 1; 1 0 1; 1 1 1; 0 1 1
    ];
    
    % 缩放和平移
    vertices = vertices .* repmat(size, 8, 1) + repmat(position, 8, 1);
    
    % 立方体面
    faces = [
        1 2 3 4; 5 6 7 8; 1 2 6 5;
        3 4 8 7; 1 4 8 5; 2 3 7 6
    ];
    
    % 绘制
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', 0.7, ...
          'EdgeColor', 'k', 'LineWidth', 1);
catch
    % 简化绘制
    plot3(position(1), position(2), position(3), ...
          'o', 'Color', color, 'MarkerSize', 20, 'MarkerFaceColor', color);
end

end
