function quickStartYumi()
% Quick Start YuMi Mechanics Explorer
% Simple method to generate the interface shown in the official tutorial

fprintf('=== Quick Start YuMi Mechanics Explorer ===\n');
fprintf('Generating interface similar to MathWorks official tutorial\n\n');

try
    % === Load YuMi robot ===
    fprintf('Step 1: Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    fprintf('SUCCESS: YuMi robot loaded (%d joints)\n', robot.NumBodies);
    
    % === Create visualization ===
    fprintf('\nStep 2: Creating 3D visualization...\n');
    
    % Create figure window
    fig = figure('Name', 'YuMi Robot - Pick and Place Simulation', ...
                 'Position', [100, 100, 1200, 800]);
    
    % Show robot
    show(robot, robot.homeConfiguration);
    hold on;
    
    % Set view angle (same as official tutorial)
    view(45, 30);
    axis equal;
    grid on;
    
    % === Add environment objects (same as in the image) ===
    fprintf('Step 3: Adding environment objects...\n');
    
    % Blue blocks (pick objects)
    plotSimpleBox([0.6, 0.2, 0.05], [0.08, 0.08, 0.08], 'blue', 'Blue Block 1');
    plotSimpleBox([0.6, -0.2, 0.05], [0.08, 0.08, 0.08], 'blue', 'Blue Block 2');
    
    % Green platform (place target)
    plotSimpleBox([0.5, 0, 0.01], [0.25, 0.25, 0.02], 'green', 'Green Platform');
    
    % Work table
    plotSimpleBox([0.5, 0, -0.01], [0.4, 0.4, 0.02], [0.8, 0.8, 0.8], 'Work Table');
    
    % === Add labels and information ===
    fprintf('Step 4: Adding labels and information...\n');
    
    % Title
    title('YuMi Dual-Arm Robot - Pick and Place Simulation', 'FontSize', 16);
    
    % Labels
    text(0.3, 0.3, 0.3, 'YuMi Robot', 'FontSize', 12, 'Color', 'red', 'FontWeight', 'bold');
    text(0.6, 0.2, 0.12, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue');
    text(0.6, -0.2, 0.12, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue');
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green');
    
    % === Start animation ===
    fprintf('Step 5: Starting pick and place animation...\n');
    
    % Animation parameters
    duration = 15;  % seconds
    fps = 20;       % frames per second
    totalFrames = duration * fps;
    
    fprintf('Animation: %d seconds, %d fps, %d total frames\n', duration, fps, totalFrames);
    
    % Animation loop
    for frame = 1:totalFrames
        t = frame / fps;
        
        % Calculate joint angles for pick and place motion
        q = robot.homeConfiguration;
        
        % Right arm motion (pick first blue block)
        if t < 3
            % Move to first blue block
            q(1) = 0.3 * (t/3);
            q(2) = -0.4 * (t/3);
            q(3) = 0.2 * (t/3);
        elseif t < 6
            % Pick motion
            q(1) = 0.3;
            q(2) = -0.4;
            q(3) = 0.2;
            q(4) = -0.5 * ((t-3)/3);
        elseif t < 9
            % Move to green platform
            q(1) = 0.3 - 0.2 * ((t-6)/3);
            q(2) = -0.4 + 0.2 * ((t-6)/3);
            q(3) = 0.2;
            q(4) = -0.5;
        elseif t < 12
            % Place motion
            q(1) = 0.1;
            q(2) = -0.2;
            q(3) = 0.2;
            q(4) = -0.5 + 0.3 * ((t-9)/3);
        else
            % Return to home
            alpha = (t-12)/3;
            q(1) = 0.1 * (1-alpha);
            q(2) = -0.2 * (1-alpha);
            q(3) = 0.2 * (1-alpha);
            q(4) = -0.2 * (1-alpha);
        end
        
        % Left arm motion (pick second blue block)
        if t < 2
            % Wait
        elseif t < 5
            % Move to second blue block
            q(8) = -0.3 * ((t-2)/3);
            q(9) = -0.4 * ((t-2)/3);
            q(10) = -0.2 * ((t-2)/3);
        elseif t < 8
            % Pick motion
            q(8) = -0.3;
            q(9) = -0.4;
            q(10) = -0.2;
            q(11) = -0.5 * ((t-5)/3);
        elseif t < 11
            % Move to green platform
            q(8) = -0.3 + 0.2 * ((t-8)/3);
            q(9) = -0.4 + 0.2 * ((t-8)/3);
            q(10) = -0.2;
            q(11) = -0.5;
        elseif t < 14
            % Place motion
            q(8) = -0.1;
            q(9) = -0.2;
            q(10) = -0.2;
            q(11) = -0.5 + 0.3 * ((t-11)/3);
        else
            % Return to home
            alpha = (t-14)/1;
            q(8) = -0.1 * (1-alpha);
            q(9) = -0.2 * (1-alpha);
            q(10) = -0.2 * (1-alpha);
            q(11) = -0.2 * (1-alpha);
        end
        
        % Update robot configuration
        show(robot, q, 'PreservePlot', false, 'Frames', 'off');
        
        % Update display
        drawnow;
        
        % Control frame rate
        pause(1/fps);
        
        % Progress indicator
        if mod(frame, fps) == 0
            fprintf('Animation progress: %d/%d seconds\n', round(t), duration);
        end
    end
    
    fprintf('\nSUCCESS: Animation completed!\n');
    
    % === Final display ===
    fprintf('\nStep 6: Final display setup...\n');
    
    % Reset to home position
    show(robot, robot.homeConfiguration, 'PreservePlot', false, 'Frames', 'off');
    
    % Add final information
    text(0.2, -0.3, 0.1, sprintf('Animation completed at %s', datestr(now)), ...
         'FontSize', 10, 'Color', 'black');
    
    fprintf('\n=== YuMi Mechanics Explorer Quick Start Completed ===\n');
    fprintf('Interface features:\n');
    fprintf('  - YuMi dual-arm robot 3D model\n');
    fprintf('  - Blue blocks (pick objects)\n');
    fprintf('  - Green platform (place target)\n');
    fprintf('  - Pick and place animation\n');
    fprintf('  - Real-time motion visualization\n\n');
    
    fprintf('Interface controls:\n');
    fprintf('  - Drag to rotate view\n');
    fprintf('  - Scroll to zoom\n');
    fprintf('  - Right-click for options\n\n');
    
catch ME
    fprintf('ERROR: Quick start failed: %s\n', ME.message);
    
    % Simple backup
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Dual-Arm Robot');
        fprintf('SUCCESS: Basic robot display\n');
    catch
        fprintf('ERROR: Complete failure\n');
    end
end

end

function plotSimpleBox(center, size, color, label)
% Plot simple box

try
    % Box corners
    x = center(1) + [-1, 1, 1, -1, -1, 1, 1, -1] * size(1)/2;
    y = center(2) + [-1, -1, 1, 1, -1, -1, 1, 1] * size(2)/2;
    z = center(3) + [-1, -1, -1, -1, 1, 1, 1, 1] * size(3)/2;
    
    % Box faces
    faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];
    
    % Draw box
    patch('Vertices', [x' y' z'], 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', 0.8, ...
          'EdgeColor', 'k', 'LineWidth', 0.5);
          
    fprintf('  Added: %s\n', label);
    
catch
    % Fallback: simple point
    plot3(center(1), center(2), center(3), ...
          'o', 'Color', color, 'MarkerSize', 12, 'MarkerFaceColor', color);
    fprintf('  Added (simplified): %s\n', label);
end

end
