function createYumiTaskSchedulerModel()
% 创建YuMi双臂机器人任务调度和轨迹生成Simulink模型
% 严格按照MathWorks官方教程结构

fprintf('🤖 === 创建YuMi任务调度模型 === 🤖\n');
fprintf('按照MathWorks官方教程结构实现\n\n');

% 模型名称
modelName = 'YumiTaskSchedulerModel';

% 关闭已存在的模型
if bdIsLoaded(modelName)
    close_system(modelName, 0);
    fprintf('关闭已存在的模型: %s\n', modelName);
end

% 创建新模型
new_system(modelName);
open_system(modelName);
fprintf('✅ 创建新模型: %s\n', modelName);

% 设置模型参数
set_param(modelName, 'SolverName', 'ode45');
set_param(modelName, 'StopTime', '30');
set_param(modelName, 'RelTol', '1e-3');

% === 1. 添加任务调度器 (按照官方教程) ===
fprintf('\n步骤1: 添加任务调度器...\n');

% Command Logic (MATLAB Function Block)
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/CommandLogic'], ...
          'Position', [100, 100, 200, 150]);

% 设置CommandLogic代码 (按照官方教程结构)
commandLogicCode = sprintf(['function [rightArmCmd, leftArmCmd, rightGripperCmd, leftGripperCmd, taskState] = fcn(time, rightArmReached, leftArmReached, rightGripperReached, leftGripperReached)\n'...
    '%% YuMi双臂任务调度器\n'...
    '%% 按照MathWorks官方教程结构实现\n\n'...
    'persistent currentTask taskStartTime configSequence\n\n'...
    'if isempty(currentTask)\n'...
    '    currentTask = 1;\n'...
    '    taskStartTime = time;\n'...
    '    configSequence = getYumiConfigSequence();\n'...
    'end\n\n'...
    '%% 任务状态机 (8个状态，按照官方教程)\n'...
    'switch currentTask\n'...
    '    case 1  %% 初始位置\n'...
    '        rightArmCmd = configSequence.right(:,1);\n'...
    '        leftArmCmd = configSequence.left(:,1);\n'...
    '        rightGripperCmd = 0;  %% 打开\n'...
    '        leftGripperCmd = 0;   %% 打开\n'...
    '        \n'...
    '    case 2  %% 移动到抓取准备位置\n'...
    '        rightArmCmd = configSequence.right(:,2);\n'...
    '        leftArmCmd = configSequence.left(:,2);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    '        \n'...
    '    case 3  %% 抓取积木\n'...
    '        rightArmCmd = configSequence.right(:,3);\n'...
    '        leftArmCmd = configSequence.left(:,3);\n'...
    '        rightGripperCmd = 1;  %% 闭合\n'...
    '        leftGripperCmd = 1;   %% 闭合\n'...
    '        \n'...
    '    case 4  %% 提升积木\n'...
    '        rightArmCmd = configSequence.right(:,4);\n'...
    '        leftArmCmd = configSequence.left(:,4);\n'...
    '        rightGripperCmd = 1;\n'...
    '        leftGripperCmd = 1;\n'...
    '        \n'...
    '    case 5  %% 移动到放置位置\n'...
    '        rightArmCmd = configSequence.right(:,5);\n'...
    '        leftArmCmd = configSequence.left(:,5);\n'...
    '        rightGripperCmd = 1;\n'...
    '        leftGripperCmd = 1;\n'...
    '        \n'...
    '    case 6  %% 放置积木\n'...
    '        rightArmCmd = configSequence.right(:,6);\n'...
    '        leftArmCmd = configSequence.left(:,6);\n'...
    '        rightGripperCmd = 0;  %% 打开\n'...
    '        leftGripperCmd = 0;   %% 打开\n'...
    '        \n'...
    '    case 7  %% 退离\n'...
    '        rightArmCmd = configSequence.right(:,7);\n'...
    '        leftArmCmd = configSequence.left(:,7);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    '        \n'...
    '    case 8  %% 返回初始位置\n'...
    '        rightArmCmd = configSequence.right(:,1);\n'...
    '        leftArmCmd = configSequence.left(:,1);\n'...
    '        rightGripperCmd = 0;\n'...
    '        leftGripperCmd = 0;\n'...
    '        \n'...
    '    otherwise\n'...
    '        currentTask = 1;  %% 循环\n'...
    'end\n\n'...
    '%% 状态转换逻辑\n'...
    'if rightArmReached && leftArmReached && rightGripperReached && leftGripperReached\n'...
    '    if time - taskStartTime > 2.0  %% 每个状态至少2秒\n'...
    '        currentTask = currentTask + 1;\n'...
    '        if currentTask > 8\n'...
    '            currentTask = 1;\n'...
    '        end\n'...
    '        taskStartTime = time;\n'...
    '    end\n'...
    'end\n\n'...
    'taskState = currentTask;\n\n'...
    'function configs = getYumiConfigSequence()\n'...
    '    %% 定义YuMi双臂配置序列\n'...
    '    configs.right = [\n'...
    '        0, 0.2, 0.3, 0.2, -0.1, -0.2, 0, 0;      %% 8个状态\n'...
    '        0, -0.3, -0.5, -0.3, 0.2, 0.3, 0, 0;\n'...
    '        0, 0.1, 0.2, 0.1, -0.1, -0.1, 0, 0;\n'...
    '        0, -0.5, -0.8, -0.5, 0.3, 0.4, 0, 0;\n'...
    '        0, 0, 0.1, 0, -0.1, -0.1, 0, 0;\n'...
    '        0, 0.2, 0.3, 0.2, -0.2, -0.2, 0, 0;\n'...
    '        0, 0, 0, 0, 0, 0, 0, 0;\n'...
    '    ];\n'...
    '    \n'...
    '    configs.left = [\n'...
    '        0, -0.2, -0.3, -0.2, 0.1, 0.2, 0, 0;\n'...
    '        0, 0.3, 0.5, 0.3, -0.2, -0.3, 0, 0;\n'...
    '        0, -0.1, -0.2, -0.1, 0.1, 0.1, 0, 0;\n'...
    '        0, 0.5, 0.8, 0.5, -0.3, -0.4, 0, 0;\n'...
    '        0, 0, -0.1, 0, 0.1, 0.1, 0, 0;\n'...
    '        0, -0.2, -0.3, -0.2, 0.2, 0.2, 0, 0;\n'...
    '        0, 0, 0, 0, 0, 0, 0, 0;\n'...
    '    ];\n'...
    'end\n']);

set_param([modelName '/CommandLogic'], 'Script', commandLogicCode);

% === 2. 添加轨迹生成器 (按照官方教程) ===
fprintf('步骤2: 添加轨迹生成器...\n');

% 右臂轨迹生成器
add_block('robotics/Trajectory Generation/Trapezoidal Velocity Profile Trajectory', ...
          [modelName '/RightArmTrajectory'], ...
          'Position', [300, 50, 400, 100]);

% 左臂轨迹生成器
add_block('robotics/Trajectory Generation/Trapezoidal Velocity Profile Trajectory', ...
          [modelName '/LeftArmTrajectory'], ...
          'Position', [300, 150, 400, 200]);

% === 3. 添加简化系统动力学 (按照官方教程) ===
fprintf('步骤3: 添加简化系统动力学...\n');

% 右臂运动模型
add_block('robotics/Manipulator Algorithms/Joint Space Motion Model', ...
          [modelName '/RightArmMotionModel'], ...
          'Position', [500, 50, 600, 100]);

% 左臂运动模型
add_block('robotics/Manipulator Algorithms/Joint Space Motion Model', ...
          [modelName '/LeftArmMotionModel'], ...
          'Position', [500, 150, 600, 200]);

% === 4. 添加夹爪模型 (按照官方教程) ===
fprintf('步骤4: 添加夹爪模型...\n');

% 右手夹爪模型
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/RightGripperModel'], ...
          'Position', [500, 250, 600, 300]);

% 左手夹爪模型
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LeftGripperModel'], ...
          'Position', [500, 350, 600, 400]);

% 设置夹爪模型代码
gripperCode = sprintf(['function [gripperReached] = fcn(gripperCmd)\n'...
    '%% 简化夹爪模型\n'...
    '%% 按照官方教程结构\n\n'...
    'persistent currentState\n'...
    'if isempty(currentState)\n'...
    '    currentState = 0;\n'...
    'end\n\n'...
    '%% 简单的一阶响应\n'...
    'tau = 0.5;  %% 时间常数\n'...
    'dt = 0.01;  %% 采样时间\n'...
    'currentState = currentState + (gripperCmd - currentState) * dt / tau;\n\n'...
    '%% 判断是否到达目标\n'...
    'threshold = 0.1;\n'...
    'gripperReached = abs(currentState - gripperCmd) < threshold;\n']);

set_param([modelName '/RightGripperModel'], 'Script', gripperCode);
set_param([modelName '/LeftGripperModel'], 'Script', gripperCode);

% === 5. 添加到达检测 (按照官方教程) ===
fprintf('步骤5: 添加到达检测...\n');

% 右臂到达检测
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/RightArmReachedDetection'], ...
          'Position', [700, 50, 800, 100]);

% 左臂到达检测
add_block('simulink/User-Defined Functions/MATLAB Function', ...
          [modelName '/LeftArmReachedDetection'], ...
          'Position', [700, 150, 800, 200]);

% 设置到达检测代码
reachedCode = sprintf(['function [reached] = fcn(desired, actual)\n'...
    '%% 关节到达检测\n'...
    '%% 按照官方教程结构\n\n'...
    'threshold = 0.05;  %% 5度阈值\n'...
    'error = abs(desired - actual);\n'...
    'reached = all(error < threshold);\n']);

set_param([modelName '/RightArmReachedDetection'], 'Script', reachedCode);
set_param([modelName '/LeftArmReachedDetection'], 'Script', reachedCode);

% === 6. 添加时钟和输出 ===
fprintf('步骤6: 添加时钟和输出...\n');

% 时钟
add_block('simulink/Sources/Clock', [modelName '/Clock'], ...
          'Position', [50, 200, 80, 230]);

% 输出端口
add_block('simulink/Sinks/Out1', [modelName '/RightArmOut'], ...
          'Position', [850, 50, 880, 80]);
add_block('simulink/Sinks/Out1', [modelName '/LeftArmOut'], ...
          'Position', [850, 150, 880, 180]);
add_block('simulink/Sinks/Out1', [modelName '/TaskStateOut'], ...
          'Position', [850, 250, 880, 280]);

% === 7. 添加可视化 (按照官方教程) ===
fprintf('步骤7: 添加可视化...\n');

% 3D可视化
add_block('simulink/Sinks/To Workspace', [modelName '/RightArmData'], ...
          'Position', [850, 350, 950, 380]);
set_param([modelName '/RightArmData'], 'VariableName', 'rightArmData');
set_param([modelName '/RightArmData'], 'SaveFormat', 'Timeseries');

add_block('simulink/Sinks/To Workspace', [modelName '/LeftArmData'], ...
          'Position', [850, 400, 950, 430]);
set_param([modelName '/LeftArmData'], 'VariableName', 'leftArmData');
set_param([modelName '/LeftArmData'], 'SaveFormat', 'Timeseries');

% === 8. 连接信号线 ===
fprintf('步骤8: 连接信号线...\n');

% 时钟到CommandLogic
add_line(modelName, 'Clock/1', 'CommandLogic/1');

% CommandLogic到轨迹生成器
add_line(modelName, 'CommandLogic/1', 'RightArmTrajectory/1');
add_line(modelName, 'CommandLogic/2', 'LeftArmTrajectory/1');

% 轨迹生成器到运动模型
add_line(modelName, 'RightArmTrajectory/1', 'RightArmMotionModel/1');
add_line(modelName, 'LeftArmTrajectory/1', 'LeftArmMotionModel/1');

% 运动模型到到达检测
add_line(modelName, 'RightArmMotionModel/1', 'RightArmReachedDetection/2');
add_line(modelName, 'LeftArmMotionModel/1', 'LeftArmReachedDetection/2');

% 到达检测反馈到CommandLogic
add_line(modelName, 'RightArmReachedDetection/1', 'CommandLogic/2');
add_line(modelName, 'LeftArmReachedDetection/1', 'CommandLogic/3');

% 夹爪连接
add_line(modelName, 'CommandLogic/3', 'RightGripperModel/1');
add_line(modelName, 'CommandLogic/4', 'LeftGripperModel/1');
add_line(modelName, 'RightGripperModel/1', 'CommandLogic/4');
add_line(modelName, 'LeftGripperModel/1', 'CommandLogic/5');

% 输出连接
add_line(modelName, 'RightArmMotionModel/1', 'RightArmOut/1');
add_line(modelName, 'LeftArmMotionModel/1', 'LeftArmOut/1');
add_line(modelName, 'CommandLogic/5', 'TaskStateOut/1');

% 数据记录连接
add_line(modelName, 'RightArmMotionModel/1', 'RightArmData/1');
add_line(modelName, 'LeftArmMotionModel/1', 'LeftArmData/1');

% === 9. 保存模型 ===
save_system(modelName);

fprintf('\n🎉 === YuMi任务调度模型创建完成 === 🎉\n');
fprintf('模型名称: %s\n', modelName);
fprintf('模型结构:\n');
fprintf('  ✅ 任务调度器 (CommandLogic)\n');
fprintf('  ✅ 双臂轨迹生成器\n');
fprintf('  ✅ 简化系统动力学\n');
fprintf('  ✅ 夹爪模型\n');
fprintf('  ✅ 到达检测\n');
fprintf('  ✅ 数据记录\n');
fprintf('\n💡 按照MathWorks官方教程结构实现\n');
fprintf('📖 参考: Model and Control a Manipulator Arm with Robotics and Simscape\n');

end
