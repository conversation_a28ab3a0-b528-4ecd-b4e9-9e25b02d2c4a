function runRealYumiSystem()
% 启动真正的YuMi乐高拼接系统
% 使用YumiSimscape.slx模型和mainbu.ldr文件

fprintf('🤖 === 启动真正的YuMi乐高拼接系统 === 🤖\n');
fprintf('使用文件: YumiSimscape.slx + mainbu.ldr\n\n');

try
    %% 第一步：环境检查
    fprintf('🔍 第一步：环境检查...\n');
    
    % 检查必要文件
    files_to_check = {'YumiSimscape.slx', 'mainbu.ldr', 'LDRParser.m'};
    missing_files = {};
    
    for i = 1:length(files_to_check)
        if ~exist(files_to_check{i}, 'file')
            missing_files{end+1} = files_to_check{i};
        else
            fprintf('  ✅ %s 存在\n', files_to_check{i});
        end
    end
    
    if ~isempty(missing_files)
        fprintf('❌ 缺少必要文件:\n');
        for i = 1:length(missing_files)
            fprintf('   - %s\n', missing_files{i});
        end
        error('请确保所有必要文件都在当前目录');
    end
    
    % 检查工具箱
    if ~exist('loadrobot', 'file')
        fprintf('❌ 需要Robotics System Toolbox\n');
        error('请安装Robotics System Toolbox');
    end
    
    if ~exist('sim', 'file')
        fprintf('❌ 需要Simulink\n');
        error('请安装Simulink');
    end
    
    fprintf('✅ 环境检查通过\n');
    
    %% 第二步：预处理模型文件
    fprintf('\n🔧 第二步：预处理模型文件...\n');
    
    % 检查YumiSimscape.slx模型
    try
        if bdIsLoaded('YumiSimscape')
            fprintf('  关闭现有YumiSimscape模型...\n');
            close_system('YumiSimscape', 0);
        end
        
        fprintf('  加载YumiSimscape.slx...\n');
        load_system('YumiSimscape');
        fprintf('  ✅ YumiSimscape模型加载成功\n');
        
        % 检查模型完整性
        blocks = find_system('YumiSimscape', 'Type', 'block');
        fprintf('  模型包含 %d 个模块\n', length(blocks));
        
    catch ME
        fprintf('  ⚠️ YumiSimscape模型加载失败: %s\n', ME.message);
        fprintf('  将使用MATLAB动画模式\n');
    end
    
    % 解析mainbu.ldr文件
    fprintf('  解析mainbu.ldr文件...\n');
    parser = LDRParser('mainbu.ldr');
    success = parser.parseLDRFile();
    
    if success
        fprintf('  ✅ mainbu.ldr解析成功\n');
        fprintf('    - 积木数量: %d 个\n', parser.total_bricks);
        
        % 显示积木分布
        displayBrickDistribution(parser.bricks);
    else
        error('mainbu.ldr文件解析失败');
    end
    
    %% 第三步：创建拼接系统
    fprintf('\n🏗️ 第三步：创建拼接系统...\n');
    
    assembly_system = RealYumiLegoAssembly();
    
    %% 第四步：显示系统信息
    fprintf('\n📊 第四步：系统信息...\n');
    
    fprintf('🤖 YuMi机器人配置:\n');
    fprintf('   - 双臂结构: 7+7 自由度\n');
    fprintf('   - 夹爪系统: 可控制开合\n');
    fprintf('   - 工作空间: 559mm 臂展\n');
    
    fprintf('\n🏗️ 建筑设计信息:\n');
    fprintf('   - 设计文件: mainbu.ldr\n');
    fprintf('   - 积木总数: %d 个\n', parser.total_bricks);
    fprintf('   - 建筑类型: 复杂结构\n');
    
    fprintf('\n🎮 操作指南:\n');
    fprintf('   ▶️  点击"开始拼接"按钮开始动画\n');
    fprintf('   ⏸️  点击"暂停"按钮暂停拼接\n');
    fprintf('   ⏹️  点击"停止"按钮停止并重置\n');
    fprintf('   📊 拖动进度条跳转到任意步骤\n');
    fprintf('   🤏 使用夹爪控制按钮控制左右夹爪\n');
    
    fprintf('\n🎯 核心功能:\n');
    fprintf('   ✅ 显示YuMi双臂结构和运动\n');
    fprintf('   ✅ 可视化夹取、放置和堆叠过程\n');
    fprintf('   ✅ 双臂任务分配和协调\n');
    fprintf('   ✅ 夹爪开合动作控制\n');
    fprintf('   ✅ 严格按照mainbu.ldr设计拼接\n');
    
    fprintf('\n🎉 === 系统启动完成 === 🎉\n');
    fprintf('💡 提示：点击"开始拼接"按钮观看完整的拼接过程！\n');
    
catch ME
    fprintf('❌ 系统启动失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    
    % 提供故障排除建议
    fprintf('\n🔧 故障排除建议:\n');
    fprintf('1. 确保YumiSimscape.slx文件完整且可打开\n');
    fprintf('2. 确保mainbu.ldr文件格式正确\n');
    fprintf('3. 检查MATLAB工具箱安装情况\n');
    fprintf('4. 尝试重启MATLAB后再次运行\n');
end

end

function displayBrickDistribution(bricks)
% 显示积木分布信息

fprintf('    积木分布分析:\n');

% 按颜色统计
colors = {};
color_counts = containers.Map();

for i = 1:length(bricks)
    color_name = bricks(i).color_name;
    if ~isKey(color_counts, color_name)
        color_counts(color_name) = 0;
        colors{end+1} = color_name;
    end
    color_counts(color_name) = color_counts(color_name) + 1;
end

fprintf('      颜色分布:\n');
for i = 1:length(colors)
    fprintf('        %s: %d 个\n', colors{i}, color_counts(colors{i}));
end

% 按高度层次统计
z_coords = zeros(length(bricks), 1);
for i = 1:length(bricks)
    z_coords(i) = bricks(i).position(3);
end

unique_z = unique(z_coords);
fprintf('      高度层次: %d 层\n', length(unique_z));

% 位置范围
positions = reshape([bricks.position], 3, [])';
min_pos = min(positions, [], 1);
max_pos = max(positions, [], 1);

fprintf('      位置范围:\n');
fprintf('        X: %.1f 到 %.1f mm\n', min_pos(1), max_pos(1));
fprintf('        Y: %.1f 到 %.1f mm\n', min_pos(2), max_pos(2));
fprintf('        Z: %.1f 到 %.1f mm\n', min_pos(3), max_pos(3));

% 双臂任务分配预览
left_arm_count = sum(positions(:, 2) >= 0);
right_arm_count = sum(positions(:, 2) < 0);

fprintf('      任务分配预览:\n');
fprintf('        左臂负责: %d 个积木 (Y≥0)\n', left_arm_count);
fprintf('        右臂负责: %d 个积木 (Y<0)\n', right_arm_count);

end
