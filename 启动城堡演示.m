function 启动城堡演示()
% 🏰 一键启动YuMi城堡拼接演示
% 最简单的启动方法

fprintf('🏰 === 一键启动城堡拼接演示 === 🏰\n');
fprintf('严格按照图片笔记实现\n\n');

try
    % === 快速启动 ===
    fprintf('⚡ 快速启动模式\n');
    fprintf('正在加载系统...\n');
    
    % 加载城堡配置
    fprintf('1. 加载城堡配置...\n');
    config = castle_lego_config();
    
    % 加载YuMi机器人
    fprintf('2. 加载YuMi机器人...\n');
    [yumi, qHome, ~, ~] = setupRobotEnv();
    
    % 显示配置信息
    fprintf('\n✅ 系统加载完成！\n');
    fprintf('城堡配置信息:\n');
    fprintf('  - 中心坐标: [%.3f, %.3f, %.3f]\n', config.center);
    fprintf('  - 积木尺寸: %.4f x %.4f m\n', config.brick_length, config.brick_width);
    fprintf('  - 总积木数: %d\n', size(config.assembly_sequence, 1));
    fprintf('  - 第五层积木: %d块\n', size(config.left_arm_bricks,1) + size(config.right_arm_bricks,1));
    
    % 启动动画
    fprintf('\n🎬 启动城堡拼接动画...\n');
    fprintf('请在MATLAB图形窗口中观看拼接过程！\n\n');
    
    castleAssemblyAnimation(yumi, qHome, config);
    
    fprintf('\n🎉 城堡拼接演示完成！\n');
    
catch ME
    fprintf('❌ 启动失败: %s\n', ME.message);
    
    % 提供备用启动方法
    fprintf('\n💡 备用启动方法:\n');
    fprintf('在MATLAB命令窗口中运行:\n');
    fprintf('>> config = castle_lego_config();\n');
    fprintf('>> yumi = loadrobot(''abbYumi'');\n');
    fprintf('>> qHome = yumi.homeConfiguration;\n');
    fprintf('>> castleAssemblyAnimation(yumi, qHome, config);\n');
end

end
