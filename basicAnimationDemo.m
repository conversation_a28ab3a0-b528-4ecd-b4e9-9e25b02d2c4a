function basicAnimationDemo()
% 基础动画演示 - 直接运行动画

fprintf('🎬 === 基础动画演示 === 🎬\n');

try
    % 创建主窗口
    main_fig = figure('Name', '🏰 YuMi城堡拼接动画演示', ...
                      'Position', [100, 100, 1200, 800], ...
                      'Color', [0.1, 0.1, 0.1]);
    
    % 创建3D显示区域
    axes_3d = axes('Parent', main_fig, ...
                   'Position', [0.05, 0.1, 0.9, 0.85], ...
                   'Color', [0.05, 0.05, 0.05]);
    
    %% 初始化3D场景
    fprintf('初始化3D场景...\n');
    axes(axes_3d);
    hold on;
    
    % 设置坐标轴
    xlabel('X (mm)', 'Color', 'white');
    ylabel('Y (mm)', 'Color', 'white');
    zlabel('Z (mm)', 'Color', 'white');
    title('🏰 YuMi机器人城堡拼接动画', 'Color', 'white', 'FontSize', 14);
    
    % 设置视角
    view(45, 30);
    axis equal;
    grid on;
    
    % 设置坐标轴范围
    xlim([0, 400]);
    ylim([0, 300]);
    zlim([0, 200]);
    
    % 添加地面
    [X, Y] = meshgrid(0:50:400, 0:50:300);
    Z = zeros(size(X));
    surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
    
    % 添加机器人指示器
    robot_marker = plot3(200, 150, 50, 'ro', 'MarkerSize', 20, 'MarkerFaceColor', 'red');
    text(200, 150, 80, 'YuMi Robot', 'Color', 'white', 'FontSize', 12, 'HorizontalAlignment', 'center');
    
    fprintf('✅ 界面初始化完成\n');
    fprintf('🎬 开始运行动画...\n');
    
    % 创建积木位置和颜色
    brick_positions = [
        100, 100, 0;   150, 100, 0;   200, 100, 0;   250, 100, 0;
        100, 150, 10;  150, 150, 10;  200, 150, 10;  250, 150, 10;
        125, 125, 20;  175, 125, 20;  225, 125, 20;
        150, 125, 30;  200, 125, 30;
        175, 125, 40
    ];
    
    brick_colors = {
        [1, 0, 0], [0, 1, 0], [0, 0, 1], [1, 1, 0],
        [1, 0, 1], [0, 1, 1], [1, 0.5, 0], [0.5, 0, 1],
        [1, 0.5, 0.5], [0.5, 1, 0.5], [0.8, 0.8, 0.2],
        [0.2, 0.8, 0.8], [0.8, 0.2, 0.8], [0.5, 0.5, 0.5]
    ];
    
    % 逐个添加积木
    for i = 1:size(brick_positions, 1)
        fprintf('添加积木 %d/%d\n', i, size(brick_positions, 1));
        
        % 机器人移动到积木位置
        for step = 1:10
            alpha = step / 10;
            x_pos = 200 * (1-alpha) + brick_positions(i, 1) * alpha;
            y_pos = 150 * (1-alpha) + brick_positions(i, 2) * alpha;
            z_pos = 50 + 20 * sin(alpha * pi);
            
            set(robot_marker, 'XData', x_pos, 'YData', y_pos, 'ZData', z_pos);
            drawnow;
            pause(0.1);
        end
        
        % 创建积木
        brick_obj = createBrick(brick_positions(i, :), brick_colors{i}, axes_3d);
        
        % 机器人返回中心
        for step = 1:10
            alpha = step / 10;
            x_pos = brick_positions(i, 1) * (1-alpha) + 200 * alpha;
            y_pos = brick_positions(i, 2) * (1-alpha) + 150 * alpha;
            z_pos = 50 + 20 * sin((1-alpha) * pi);
            
            set(robot_marker, 'XData', x_pos, 'YData', y_pos, 'ZData', z_pos);
            drawnow;
            pause(0.1);
        end
        
        pause(0.3); % 短暂停顿
    end
    
    fprintf('✅ 城堡拼接完成！\n');
    
    % 最终展示 - 旋转视角
    fprintf('🎭 最终展示 - 旋转视角...\n');
    for angle = 0:5:360
        view(angle, 30);
        drawnow;
        pause(0.05);
    end
    
    view(45, 30); % 恢复原始视角
    fprintf('🎉 演示完成！\n');
    
catch ME
    fprintf('❌ 演示失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

end

function brick_obj = createBrick(position, color, parent_axes)
% 创建积木3D对象

brick_size = [32, 16, 9.6];

% 创建立方体的顶点
vertices = [
    0, 0, 0;
    brick_size(1), 0, 0;
    brick_size(1), brick_size(2), 0;
    0, brick_size(2), 0;
    0, 0, brick_size(3);
    brick_size(1), 0, brick_size(3);
    brick_size(1), brick_size(2), brick_size(3);
    0, brick_size(2), brick_size(3)
];

% 平移到正确位置
vertices = vertices + repmat(position, 8, 1);

% 定义面
faces = [
    1, 2, 3, 4;  % 底面
    5, 6, 7, 8;  % 顶面
    1, 2, 6, 5;  % 前面
    3, 4, 8, 7;  % 后面
    1, 4, 8, 5;  % 左面
    2, 3, 7, 6   % 右面
];

% 绘制积木
brick_obj = patch('Vertices', vertices, ...
                  'Faces', faces, ...
                  'FaceColor', color, ...
                  'EdgeColor', 'black', ...
                  'FaceAlpha', 0.8, ...
                  'LineWidth', 1, ...
                  'Parent', parent_axes);

end
