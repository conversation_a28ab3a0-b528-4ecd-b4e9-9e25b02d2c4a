classdef BSplineTrajectory < handle
    % B-spline轨迹生成和优化器
    % 用于生成平滑的机器人轨迹，满足动力学约束
    
    properties
        degree          % B-spline阶数
        num_control_points % 控制点数量
        control_points  % 控制点
        knot_vector    % 节点向量
        time_duration  % 轨迹持续时间
        max_velocity   % 最大速度约束
        max_acceleration % 最大加速度约束
        num_joints     % 关节数量
    end
    
    methods
        function obj = BSplineTrajectory(options)
            % 构造函数
            % 输入：
            %   options - 配置选项结构体
            
            if nargin < 1
                options = struct();
            end
            
            obj.degree = getfield_default(options, 'degree', 3); % 三次B-spline
            obj.max_velocity = getfield_default(options, 'max_velocity', 1.0); % rad/s
            obj.max_acceleration = getfield_default(options, 'max_acceleration', 2.0); % rad/s²
            
            fprintf('B-spline轨迹生成器初始化\n');
            fprintf('阶数: %d, 最大速度: %.2f rad/s, 最大加速度: %.2f rad/s²\n', ...
                    obj.degree, obj.max_velocity, obj.max_acceleration);
        end
        
        function [trajectory, success] = generateTrajectory(obj, waypoints, time_duration)
            % 从路径点生成B-spline轨迹
            % 输入：
            %   waypoints - 路径点矩阵 (n_points x n_joints)
            %   time_duration - 轨迹持续时间
            % 输出：
            %   trajectory - 轨迹结构体
            %   success - 是否成功生成
            
            fprintf('开始生成B-spline轨迹...\n');
            
            obj.time_duration = time_duration;
            obj.num_joints = size(waypoints, 2);
            
            % 初始化控制点
            obj.initializeControlPoints(waypoints);
            
            % 优化控制点以满足约束
            success = obj.optimizeControlPoints(waypoints);
            
            if success
                % 生成最终轨迹
                trajectory = obj.evaluateTrajectory();
                fprintf('B-spline轨迹生成成功\n');
            else
                trajectory = [];
                fprintf('B-spline轨迹生成失败\n');
            end
        end
        
        function initializeControlPoints(obj, waypoints)
            % 初始化控制点
            % 使用路径点作为初始控制点，并添加额外的控制点以提高灵活性
            
            n_waypoints = size(waypoints, 1);
            
            % 控制点数量：路径点数量 + 额外控制点
            obj.num_control_points = n_waypoints + 2;
            
            % 初始化控制点矩阵
            obj.control_points = zeros(obj.num_control_points, obj.num_joints);
            
            % 设置边界控制点
            obj.control_points(1, :) = waypoints(1, :); % 起始点
            obj.control_points(end, :) = waypoints(end, :); % 终点
            
            % 内部控制点使用线性插值初始化
            for i = 2:obj.num_control_points-1
                alpha = (i-1) / (obj.num_control_points-1);
                waypoint_idx = min(n_waypoints, max(1, round(alpha * n_waypoints)));
                obj.control_points(i, :) = waypoints(waypoint_idx, :);
            end
            
            % 生成节点向量
            obj.generateKnotVector();
            
            fprintf('控制点初始化完成: %d个控制点\n', obj.num_control_points);
        end
        
        function generateKnotVector(obj)
            % 生成均匀的节点向量
            n = obj.num_control_points;
            p = obj.degree;
            
            % 节点向量长度 = 控制点数 + 阶数 + 1
            m = n + p + 1;
            obj.knot_vector = zeros(1, m);
            
            % 开始和结束的重复节点
            for i = 1:p+1
                obj.knot_vector(i) = 0;
                obj.knot_vector(m-i+1) = 1;
            end
            
            % 内部节点均匀分布
            for i = p+2:m-p-1
                obj.knot_vector(i) = (i-p-1) / (n-p);
            end
        end
        
        function success = optimizeControlPoints(obj, waypoints)
            % 优化控制点以满足动力学约束和路径要求
            success = true;
            
            try
                % 设置优化问题
                options = optimoptions('fmincon', ...
                    'Display', 'off', ...
                    'MaxIterations', 1000, ...
                    'ConstraintTolerance', 1e-6);
                
                % 初始值：当前控制点（展平）
                x0 = reshape(obj.control_points, [], 1);
                
                % 边界约束（关节限制）
                [lb, ub] = obj.getJointLimits();
                
                % 等式约束：起始和终点固定
                [Aeq, beq] = obj.getEndpointConstraints(waypoints);
                
                % 优化目标函数：最小化轨迹的加速度
                objective = @(x) obj.objectiveFunction(x);
                
                % 非线性约束：速度和加速度限制
                nonlinear_constraints = @(x) obj.nonlinearConstraints(x);
                
                % 执行优化
                [x_opt, ~, exitflag] = fmincon(objective, x0, [], [], Aeq, beq, lb, ub, ...
                                              nonlinear_constraints, options);
                
                if exitflag > 0
                    % 更新控制点
                    obj.control_points = reshape(x_opt, obj.num_control_points, obj.num_joints);
                    fprintf('控制点优化成功\n');
                else
                    fprintf('控制点优化失败，使用初始控制点\n');
                    success = false;
                end
                
            catch ME
                fprintf('优化过程出错: %s\n', ME.message);
                success = false;
            end
        end
        
        function [lb, ub] = getJointLimits(obj)
            % 获取关节限制作为优化边界
            % 假设所有关节的限制为 [-pi, pi]
            
            n_vars = obj.num_control_points * obj.num_joints;
            lb = -pi * ones(n_vars, 1);
            ub = pi * ones(n_vars, 1);
        end
        
        function [Aeq, beq] = getEndpointConstraints(obj, waypoints)
            % 设置端点约束：起始和终点必须通过指定的路径点
            
            n_vars = obj.num_control_points * obj.num_joints;
            
            % 起始点约束
            Aeq_start = zeros(obj.num_joints, n_vars);
            for j = 1:obj.num_joints
                idx = (j-1) * obj.num_control_points + 1;
                Aeq_start(j, idx) = 1;
            end
            beq_start = waypoints(1, :)';
            
            % 终点约束
            Aeq_end = zeros(obj.num_joints, n_vars);
            for j = 1:obj.num_joints
                idx = j * obj.num_control_points;
                Aeq_end(j, idx) = 1;
            end
            beq_end = waypoints(end, :)';
            
            Aeq = [Aeq_start; Aeq_end];
            beq = [beq_start; beq_end];
        end
        
        function cost = objectiveFunction(obj, x)
            % 目标函数：最小化轨迹的加速度平方积分
            
            % 重构控制点
            control_points = reshape(x, obj.num_control_points, obj.num_joints);
            
            % 计算轨迹的二阶导数（加速度）
            cost = 0;
            n_samples = 50; % 采样点数
            
            for i = 1:n_samples
                t = i / n_samples;
                
                % 计算二阶导数
                acceleration = obj.evaluateDerivative(t, control_points, 2);
                
                % 累加加速度的平方
                cost = cost + sum(acceleration.^2);
            end
            
            cost = cost / n_samples;
        end
        
        function [c, ceq] = nonlinearConstraints(obj, x)
            % 非线性约束：速度和加速度限制
            
            control_points = reshape(x, obj.num_control_points, obj.num_joints);
            
            c = [];
            ceq = [];
            
            n_samples = 20;
            
            for i = 1:n_samples
                t = i / n_samples;
                
                % 速度约束
                velocity = obj.evaluateDerivative(t, control_points, 1);
                max_vel = max(abs(velocity));
                if max_vel > obj.max_velocity
                    c = [c; max_vel - obj.max_velocity];
                end
                
                % 加速度约束
                acceleration = obj.evaluateDerivative(t, control_points, 2);
                max_acc = max(abs(acceleration));
                if max_acc > obj.max_acceleration
                    c = [c; max_acc - obj.max_acceleration];
                end
            end
        end
        
        function derivative = evaluateDerivative(obj, t, control_points, order)
            % 计算B-spline曲线的导数
            % 输入：
            %   t - 参数值 [0,1]
            %   control_points - 控制点矩阵
            %   order - 导数阶数 (1=速度, 2=加速度)
            
            if order == 0
                % 位置
                derivative = obj.evaluateBSpline(t, control_points);
            elseif order == 1
                % 一阶导数（速度）
                derivative = obj.evaluateBSplineDerivative(t, control_points, 1);
            elseif order == 2
                % 二阶导数（加速度）
                derivative = obj.evaluateBSplineDerivative(t, control_points, 2);
            else
                error('不支持的导数阶数');
            end
            
            % 时间缩放
            derivative = derivative / (obj.time_duration^order);
        end
        
        function trajectory = evaluateTrajectory(obj, num_points)
            % 评估完整轨迹
            % 输入：
            %   num_points - 轨迹点数量（可选）
            
            if nargin < 2
                num_points = 100;
            end
            
            trajectory = struct();
            trajectory.time = linspace(0, obj.time_duration, num_points);
            trajectory.position = zeros(num_points, obj.num_joints);
            trajectory.velocity = zeros(num_points, obj.num_joints);
            trajectory.acceleration = zeros(num_points, obj.num_joints);
            
            for i = 1:num_points
                t = (i-1) / (num_points-1);
                
                trajectory.position(i, :) = obj.evaluateDerivative(t, obj.control_points, 0);
                trajectory.velocity(i, :) = obj.evaluateDerivative(t, obj.control_points, 1);
                trajectory.acceleration(i, :) = obj.evaluateDerivative(t, obj.control_points, 2);
            end
            
            trajectory.control_points = obj.control_points;
            trajectory.knot_vector = obj.knot_vector;
        end
        
        function point = evaluateBSpline(obj, t, control_points)
            % 评估B-spline曲线上的点
            point = zeros(1, obj.num_joints);
            
            for i = 1:obj.num_control_points
                basis = obj.basisFunction(i-1, obj.degree, t);
                point = point + basis * control_points(i, :);
            end
        end
        
        function derivative = evaluateBSplineDerivative(obj, t, control_points, order)
            % 评估B-spline曲线的导数
            % 使用递归公式计算导数
            
            if order == 0
                derivative = obj.evaluateBSpline(t, control_points);
                return;
            end
            
            % 计算导数控制点
            n = obj.num_control_points;
            p = obj.degree;
            
            if order > p
                derivative = zeros(1, obj.num_joints);
                return;
            end
            
            % 递归计算导数
            derivative_control_points = control_points;
            
            for k = 1:order
                new_control_points = zeros(n-k, obj.num_joints);
                for i = 1:n-k
                    coeff = (p-k+1) / (obj.knot_vector(i+p+1) - obj.knot_vector(i+k));
                    new_control_points(i, :) = coeff * (derivative_control_points(i+1, :) - derivative_control_points(i, :));
                end
                derivative_control_points = new_control_points;
                n = n - 1;
                p = p - 1;
            end
            
            % 评估导数
            derivative = zeros(1, obj.num_joints);
            for i = 1:size(derivative_control_points, 1)
                basis = obj.basisFunction(i-1, p, t);
                derivative = derivative + basis * derivative_control_points(i, :);
            end
        end
        
        function N = basisFunction(obj, i, p, t)
            % 计算B-spline基函数
            % Cox-de Boor递归公式
            
            if p == 0
                if t >= obj.knot_vector(i+1) && t < obj.knot_vector(i+2)
                    N = 1;
                else
                    N = 0;
                end
            else
                % 递归计算
                left_coeff = 0;
                right_coeff = 0;
                
                if obj.knot_vector(i+p+1) ~= obj.knot_vector(i+1)
                    left_coeff = (t - obj.knot_vector(i+1)) / (obj.knot_vector(i+p+1) - obj.knot_vector(i+1));
                end
                
                if obj.knot_vector(i+p+2) ~= obj.knot_vector(i+2)
                    right_coeff = (obj.knot_vector(i+p+2) - t) / (obj.knot_vector(i+p+2) - obj.knot_vector(i+2));
                end
                
                N = left_coeff * obj.basisFunction(i, p-1, t) + right_coeff * obj.basisFunction(i+1, p-1, t);
            end
        end
    end
end

function value = getfield_default(s, field, default_value)
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end
