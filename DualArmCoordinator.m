classdef DualArmCoordinator < handle
    % 双臂协调器
    % 负责双臂任务调度、避碰协调和执行优化
    
    properties
        robot               % 机器人模型
        collision_checker   % 碰撞检测器
        rrt_planner        % RRT路径规划器
        bspline_generator  % B-spline轨迹生成器
        task_queue         % 任务队列
        execution_schedule % 执行调度
        safety_margin      % 安全距离
        coordination_mode  % 协调模式
    end
    
    methods
        function obj = DualArmCoordinator(robot, options)
            % 构造函数
            % 输入：
            %   robot - 机器人模型
            %   options - 配置选项
            
            obj.robot = robot;
            
            if nargin < 2
                options = struct();
            end
            
            obj.safety_margin = getfield_default(options, 'safety_margin', 0.1);
            obj.coordination_mode = getfield_default(options, 'coordination_mode', 'sequential');
            
            % 初始化子模块
            obj.initializeSubmodules(options);
            
            fprintf('双臂协调器初始化完成\n');
            fprintf('协调模式: %s\n', obj.coordination_mode);
            fprintf('安全距离: %.3f m\n', obj.safety_margin);
        end
        
        function initializeSubmodules(obj, options)
            % 初始化子模块
            
            % 碰撞检测器
            collision_options = getfield_default(options, 'collision_options', struct());
            collision_options.safety_margin = obj.safety_margin;
            obj.collision_checker = CollisionChecker(obj.robot, collision_options);
            
            % RRT规划器
            rrt_options = getfield_default(options, 'rrt_options', struct());
            obj.rrt_planner = RRTPlanner(obj.robot, obj.collision_checker, rrt_options);
            
            % B-spline轨迹生成器
            bspline_options = getfield_default(options, 'bspline_options', struct());
            obj.bspline_generator = BSplineTrajectory(bspline_options);
        end
        
        function trajectories = planDualArmTrajectories(obj, brick_config, qHome)
            % 规划双臂协调轨迹
            % 输入：
            %   brick_config - 乐高配置
            %   qHome - 初始关节角
            % 输出：
            %   trajectories - 协调后的轨迹序列
            
            fprintf('=== 开始双臂协调轨迹规划 ===\n');
            
            % 解析任务序列
            obj.parseTaskSequence(brick_config);
            
            % 生成执行调度
            obj.generateExecutionSchedule();
            
            % 规划协调轨迹
            trajectories = obj.planCoordinatedTrajectories(qHome);
            
            fprintf('双臂协调轨迹规划完成，生成 %d 个轨迹\n', length(trajectories));
        end
        
        function parseTaskSequence(obj, brick_config)
            % 解析任务序列，构建任务队列
            
            obj.task_queue = [];
            if ~isfield(brick_config, 'task_sequence')
                fprintf('错误: brick_config 中未找到 task_sequence 字段。\n');
                return;
            end
            task_sequence = brick_config.task_sequence;
            
            for i = 1:length(task_sequence)
                task = task_sequence(i);
                
                % 构建详细任务信息
                detailed_task = struct();
                detailed_task.id = i;
                detailed_task.arm = task.arm;
                detailed_task.priority = getfield_default(task, 'priority', 1);
                
                % 直接从task中获取抓取和放置位姿
                if isfield(task, 'pick_pose') && isfield(task, 'place_pose')
                    detailed_task.pick_position = task.pick_pose.position;
                    detailed_task.pick_orientation = task.pick_pose.orientation;
                    detailed_task.place_position = task.place_pose.position;
                    detailed_task.place_orientation = task.place_pose.orientation;
                else
                    fprintf('警告: 任务 %d 缺少 pick_pose 或 place_pose。\n', i);
                    continue;
                end
                
                % 估算执行时间
                detailed_task.estimated_duration = obj.estimateTaskDuration(detailed_task);
                
                obj.task_queue = [obj.task_queue; detailed_task];
            end
            
            fprintf('任务队列构建完成，共 %d 个任务\n', length(obj.task_queue));
        end
        
        function duration = estimateTaskDuration(obj, task)
            % 估算任务执行时间
            % 基于距离和复杂度
            
            pick_pos = task.pick_position;
            place_pos = task.place_position;
            
            % 计算移动距离
            distance = norm(place_pos - pick_pos);
            
            % 基础时间 + 距离相关时间
            duration = 5.0 + distance * 10.0; % 秒
        end
        
        function generateExecutionSchedule(obj)
            % 生成执行调度，考虑双臂协调
            
            fprintf('生成执行调度...\n');
            
            if strcmp(obj.coordination_mode, 'sequential')
                obj.execution_schedule = obj.generateSequentialSchedule();
            elseif strcmp(obj.coordination_mode, 'parallel')
                obj.execution_schedule = obj.generateParallelSchedule();
            else
                obj.execution_schedule = obj.generateAdaptiveSchedule();
            end
            
            fprintf('执行调度生成完成\n');
        end
        
        function schedule = generateSequentialSchedule(obj)
            % 生成顺序执行调度（安全但效率较低）
            
            schedule = [];
            current_time = 0;
            
            for i = 1:length(obj.task_queue)
                task = obj.task_queue(i);
                
                schedule_item = struct();
                schedule_item.task_id = task.id;
                schedule_item.arm = task.arm;
                schedule_item.start_time = current_time;
                schedule_item.end_time = current_time + task.estimated_duration;
                schedule_item.conflicts = [];
                
                schedule = [schedule; schedule_item];
                current_time = schedule_item.end_time;
            end
        end
        
        function schedule = generateParallelSchedule(obj)
            % 生成并行执行调度（需要仔细避碰）
            
            schedule = [];
            right_arm_time = 0;
            left_arm_time = 0;
            
            for i = 1:length(obj.task_queue)
                task = obj.task_queue(i);
                
                schedule_item = struct();
                schedule_item.task_id = task.id;
                schedule_item.arm = task.arm;
                
                if strcmp(task.arm, 'right')
                    schedule_item.start_time = right_arm_time;
                    schedule_item.end_time = right_arm_time + task.estimated_duration;
                    right_arm_time = schedule_item.end_time;
                else
                    schedule_item.start_time = left_arm_time;
                    schedule_item.end_time = left_arm_time + task.estimated_duration;
                    left_arm_time = schedule_item.end_time;
                end
                
                % 检查与其他任务的冲突
                schedule_item.conflicts = obj.detectScheduleConflicts(schedule_item, schedule);
                
                schedule = [schedule; schedule_item];
            end
        end
        
        function schedule = generateAdaptiveSchedule(obj)
            % 生成自适应调度（平衡安全性和效率）
            
            % 先生成并行调度
            parallel_schedule = obj.generateParallelSchedule();
            
            % 解决冲突
            schedule = obj.resolveScheduleConflicts(parallel_schedule);
        end
        
        function conflicts = detectScheduleConflicts(obj, new_item, existing_schedule)
            % 检测调度冲突
            
            conflicts = [];
            
            for i = 1:length(existing_schedule)
                existing_item = existing_schedule(i);
                
                % 检查时间重叠
                if obj.hasTimeOverlap(new_item, existing_item)
                    % 检查空间冲突
                    if obj.hasSpaceConflict(new_item, existing_item)
                        conflicts = [conflicts; existing_item.task_id];
                    end
                end
            end
        end
        
        function overlap = hasTimeOverlap(obj, item1, item2)
            % 检查两个调度项是否有时间重叠
            overlap = ~(item1.end_time <= item2.start_time || item2.end_time <= item1.start_time);
        end
        
        function conflict = hasSpaceConflict(obj, item1, item2)
            % 检查两个任务是否有空间冲突
            
            % 如果是同一手臂，肯定冲突
            if strcmp(item1.arm, item2.arm)
                conflict = true;
                return;
            end
            
            % 获取任务的工作区域
            task1 = obj.task_queue(item1.task_id);
            task2 = obj.task_queue(item2.task_id);
            
            % 简化的空间冲突检测：检查工作区域重叠
            workspace1 = obj.getTaskWorkspace(task1);
            workspace2 = obj.getTaskWorkspace(task2);
            
            conflict = obj.workspacesOverlap(workspace1, workspace2);
        end
        
        function workspace = getTaskWorkspace(obj, task)
            % 获取任务的工作空间
            
            pick_pos = task.pick_position;
            place_pos = task.place_position;
            
            % 确保维度一致 (都转换为行向量)
            pick_pos = pick_pos(:)';
            place_pos = place_pos(:)';
            
            % 简化为包围盒
            min_pos = min([pick_pos; place_pos], [], 1) - obj.safety_margin;
            max_pos = max([pick_pos; place_pos], [], 1) + obj.safety_margin;
            
            workspace = struct('min', min_pos, 'max', max_pos);
        end
        
        function overlap = workspacesOverlap(obj, ws1, ws2)
            % 检查两个工作空间是否重叠
            
            overlap = all(ws1.min <= ws2.max) && all(ws2.min <= ws1.max);
        end
        
        function resolved_schedule = resolveScheduleConflicts(obj, schedule)
            % 解决调度冲突
            
            resolved_schedule = schedule;
            
            % 找到所有有冲突的任务
            conflicted_tasks = [];
            for i = 1:length(schedule)
                if ~isempty(schedule(i).conflicts)
                    conflicted_tasks = [conflicted_tasks; i];
                end
            end
            
            % 重新调度冲突任务
            for i = 1:length(conflicted_tasks)
                task_idx = conflicted_tasks(i);
                resolved_schedule = obj.rescheduleTask(resolved_schedule, task_idx);
            end
        end
        
        function schedule = rescheduleTask(obj, schedule, task_idx)
            % 重新调度单个任务以避免冲突
            
            task_item = schedule(task_idx);
            
            % 找到最早的无冲突时间
            earliest_start = 0;
            for conflict_id = task_item.conflicts'
                conflict_idx = find([schedule.task_id] == conflict_id);
                if ~isempty(conflict_idx)
                    earliest_start = max(earliest_start, schedule(conflict_idx).end_time);
                end
            end
            
            % 更新调度时间
            task = obj.task_queue(task_item.task_id);
            schedule(task_idx).start_time = earliest_start;
            schedule(task_idx).end_time = earliest_start + task.estimated_duration;
            schedule(task_idx).conflicts = []; % 清除冲突标记
        end
        
        function trajectories = planCoordinatedTrajectories(obj, qHome)
            % 规划协调后的轨迹
            
            trajectories = {};
            
            for i = 1:length(obj.execution_schedule)
                schedule_item = obj.execution_schedule(i);
                task = obj.task_queue(schedule_item.task_id);
                
                fprintf('规划任务 %d (%s臂)...\n', task.id, task.arm);
                
                % 规划单个任务的轨迹
                trajectory = obj.planSingleTaskTrajectory(task, qHome);
                
                if ~isempty(trajectory)
                    trajectory.schedule = schedule_item;
                    trajectories{end+1} = trajectory;
                    fprintf('任务 %d 轨迹规划成功\n', task.id);
                else
                    fprintf('任务 %d 轨迹规划失败\n', task.id);
                end
            end
        end
        
        function trajectory = planSingleTaskTrajectory(obj, task, qHome)
            % 规划单个任务的轨迹
            
            % 定义关键路径点
            waypoints = obj.generateTaskWaypoints(task, qHome);
            
            if isempty(waypoints)
                trajectory = [];
                return;
            end
            
            % 使用RRT规划路径
            arm_joints = obj.getArmJoints(task.arm);
            q_start = qHome(arm_joints);
            
            path_segments = {};
            for i = 1:size(waypoints, 1)-1
                q_goal = waypoints(i+1, :);
                [path, success] = obj.rrt_planner.planPath(waypoints(i, :), q_goal, task.arm);
                
                if success
                    path_segments{end+1} = path;
                else
                    fprintf('路径段 %d 规划失败\n', i);
                    trajectory = [];
                    return;
                end
            end
            
            % 连接路径段
            full_path = obj.connectPathSegments(path_segments);
            
            % 使用B-spline优化轨迹
            [optimized_trajectory, success] = obj.bspline_generator.generateTrajectory(full_path, task.estimated_duration);
            
            if success
                trajectory = optimized_trajectory;
                trajectory.task = task;
                trajectory.arm = task.arm;
            else
                trajectory = [];
            end
        end
        
        function waypoints = generateTaskWaypoints(obj, task, qHome)
            % 为任务生成关键路径点
            
            arm_joints = obj.getArmJoints(task.arm);
            ee_name = obj.getEndEffectorName(task.arm);
            
            % 使用逆运动学求解关键位置
            ik = inverseKinematics('RigidBodyTree', obj.robot);
            weights = [0.1, 0.1, 0.1, 1, 1, 1];
            
            try
                % 起始位置
                q_start = qHome(arm_joints);
                
                % 抓取预备位置
                T_pre_pick = obj.getPoseTransform(task.pick_position + [0 0 0.05], task.pick_orientation);
                [q_pre_pick, ~] = ik(ee_name, T_pre_pick, weights, q_start);
                
                % 抓取位置
                T_pick = obj.getPoseTransform(task.pick_position, task.pick_orientation);
                [q_pick, ~] = ik(ee_name, T_pick, weights, q_pre_pick);
                
                % 放置预备位置
                T_pre_place = obj.getPoseTransform(task.place_position + [0 0 0.05], task.place_orientation);
                [q_pre_place, ~] = ik(ee_name, T_pre_place, weights, q_pick);
                
                % 放置位置
                T_place = obj.getPoseTransform(task.place_position, task.place_orientation);
                [q_place, ~] = ik(ee_name, T_place, weights, q_pre_place);
                
                % 确保所有关节角都是行向量
                q_start = q_start(:)';
                q_pre_pick = q_pre_pick(:)';
                q_pick = q_pick(:)';
                q_pre_place = q_pre_place(:)';
                q_place = q_place(:)';
                
                waypoints = [q_start; q_pre_pick; q_pick; q_pre_place; q_place];
                
            catch ME
                fprintf('路径点生成失败: %s\n', ME.message);
                waypoints = [];
            end
        end
        
        function T = getPoseTransform(obj, position, orientation)
            % 生成位姿变换矩阵
            T = trvec2tform(position) * eul2tform([0, pi, orientation]);
        end
        
        function joints = getArmJoints(obj, arm_name)
            % 获取手臂关节索引
            if strcmp(arm_name, 'right')
                joints = 1:7;
            else
                joints = 8:14;
            end
        end
        
        function ee_name = getEndEffectorName(obj, arm_name)
            % 获取末端执行器名称
            if strcmp(arm_name, 'right')
                ee_name = 'gripper_r_base';
            else
                ee_name = 'gripper_l_base';
            end
        end
        
        function full_path = connectPathSegments(obj, path_segments)
            % 连接多个路径段
            full_path = [];
            
            for i = 1:length(path_segments)
                if i == 1
                    full_path = path_segments{i};
                else
                    % 跳过重复的连接点
                    full_path = [full_path; path_segments{i}(2:end, :)];
                end
            end
        end
    end
end

function value = getfield_default(s, field, default_value)
    if isfield(s, field)
        value = s.(field);
    else
        value = default_value;
    end
end
