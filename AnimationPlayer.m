classdef AnimationPlayer < handle
    % 带播放进度条的3D动画播放器
    % 支持播放、暂停、快进、倒退等功能
    
    properties
        % 界面组件
        main_figure
        axes_3d
        progress_bar
        time_text
        play_button
        pause_button
        stop_button
        speed_slider
        
        % 动画数据
        yumi_robot
        trajectories
        brick_config
        total_frames
        current_frame
        
        % 播放控制
        is_playing
        animation_speed
        timer_obj
        
        % 可视化对象
        robot_plot
        brick_objects
        trajectory_lines
    end
    
    methods
        function obj = AnimationPlayer(yumi, trajectories, brick_config)
            % 构造函数
            obj.yumi_robot = yumi;
            obj.trajectories = trajectories;
            obj.brick_config = brick_config;
            obj.current_frame = 1;
            obj.is_playing = false;
            obj.animation_speed = 1.0;
            obj.brick_objects = {};
            obj.trajectory_lines = {};
            
            % 计算总帧数
            obj.calculateTotalFrames();
            
            % 创建界面
            obj.createGUI();
            
            % 初始化3D场景
            obj.initialize3DScene();
            
            % 创建定时器
            obj.timer_obj = timer('ExecutionMode', 'fixedRate', ...
                                  'Period', 0.05, ...
                                  'TimerFcn', @(~,~) obj.updateFrame());
        end
        
        function calculateTotalFrames(obj)
            % 计算总帧数
            obj.total_frames = 0;
            for i = 1:length(obj.trajectories)
                if isfield(obj.trajectories{i}, 'Q')
                    obj.total_frames = obj.total_frames + size(obj.trajectories{i}.Q, 1);
                end
            end
            
            if obj.total_frames == 0
                obj.total_frames = 100; % 默认帧数
            end
        end
        
        function createGUI(obj)
            % 创建图形用户界面
            obj.main_figure = figure('Name', '🎬 YuMi城堡拼接动画播放器', ...
                                     'Position', [100, 100, 1400, 900], ...
                                     'Color', [0.1, 0.1, 0.1], ...
                                     'MenuBar', 'none', ...
                                     'ToolBar', 'none', ...
                                     'CloseRequestFcn', @(~,~) obj.closePlayer());
            
            % 创建3D显示区域
            obj.axes_3d = axes('Parent', obj.main_figure, ...
                               'Position', [0.05, 0.25, 0.9, 0.7], ...
                               'Color', [0.05, 0.05, 0.05]);
            
            % 创建控制面板
            control_panel = uipanel('Parent', obj.main_figure, ...
                                    'Position', [0.05, 0.02, 0.9, 0.2], ...
                                    'BackgroundColor', [0.2, 0.2, 0.2]);
            
            % 播放控制按钮
            obj.play_button = uicontrol('Parent', control_panel, ...
                                        'Style', 'pushbutton', ...
                                        'String', '▶️ 播放', ...
                                        'Position', [20, 100, 80, 40], ...
                                        'FontSize', 12, ...
                                        'Callback', @(~,~) obj.playAnimation());
            
            obj.pause_button = uicontrol('Parent', control_panel, ...
                                         'Style', 'pushbutton', ...
                                         'String', '⏸️ 暂停', ...
                                         'Position', [110, 100, 80, 40], ...
                                         'FontSize', 12, ...
                                         'Callback', @(~,~) obj.pauseAnimation());
            
            obj.stop_button = uicontrol('Parent', control_panel, ...
                                        'Style', 'pushbutton', ...
                                        'String', '⏹️ 停止', ...
                                        'Position', [200, 100, 80, 40], ...
                                        'FontSize', 12, ...
                                        'Callback', @(~,~) obj.stopAnimation());
            
            % 进度条
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', '播放进度:', ...
                      'Position', [20, 60, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10);
            
            obj.progress_bar = uicontrol('Parent', control_panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 60, 400, 20], ...
                                         'Min', 1, ...
                                         'Max', obj.total_frames, ...
                                         'Value', 1, ...
                                         'Callback', @(src,~) obj.seekToFrame(round(src.Value)));
            
            % 时间显示
            obj.time_text = uicontrol('Parent', control_panel, ...
                                      'Style', 'text', ...
                                      'String', sprintf('帧: 1/%d', obj.total_frames), ...
                                      'Position', [520, 60, 100, 20], ...
                                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                                      'ForegroundColor', 'white', ...
                                      'FontSize', 10);
            
            % 速度控制
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', '播放速度:', ...
                      'Position', [20, 20, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10);
            
            obj.speed_slider = uicontrol('Parent', control_panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 20, 200, 20], ...
                                         'Min', 0.1, ...
                                         'Max', 3.0, ...
                                         'Value', 1.0, ...
                                         'Callback', @(src,~) obj.setSpeed(src.Value));
            
            % 速度显示
            uicontrol('Parent', control_panel, ...
                      'Style', 'text', ...
                      'String', '1.0x', ...
                      'Position', [320, 20, 50, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white', ...
                      'FontSize', 10, ...
                      'Tag', 'speed_display');
        end
        
        function initialize3DScene(obj)
            % 初始化3D场景
            axes(obj.axes_3d);
            hold on;

            % 设置坐标轴
            xlabel('X (mm)', 'Color', 'white');
            ylabel('Y (mm)', 'Color', 'white');
            zlabel('Z (mm)', 'Color', 'white');
            title('🏰 YuMi机器人城堡拼接动画', 'Color', 'white', 'FontSize', 14);

            % 设置视角
            view(45, 30);
            axis equal;
            grid on;

            try
                % 显示初始机器人
                obj.robot_plot = show(obj.yumi_robot, obj.yumi_robot.homeConfiguration, ...
                                      'Parent', obj.axes_3d, ...
                                      'PreservePlot', true, ...
                                      'Frames', 'off');
                fprintf('✅ 机器人显示成功\n');
            catch ME
                fprintf('⚠️ 机器人显示失败: %s\n', ME.message);
                % 创建简单的机器人替代显示
                plot3(0, 0, 0, 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'red');
                text(0, 0, 50, 'YuMi Robot', 'Color', 'white', 'FontSize', 12);
            end

            % 设置坐标轴范围
            xlim([-200, 600]);
            ylim([-200, 400]);
            zlim([0, 300]);

            % 添加地面
            [X, Y] = meshgrid(-200:50:600, -200:50:400);
            Z = zeros(size(X));
            surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');

            % 添加一些初始积木来测试显示
            obj.addTestBricks();

            % 强制刷新显示
            drawnow;
        end
        
        function playAnimation(obj)
            % 开始播放动画
            obj.is_playing = true;
            start(obj.timer_obj);
            fprintf('🎬 开始播放动画...\n');
        end
        
        function pauseAnimation(obj)
            % 暂停动画
            obj.is_playing = false;
            stop(obj.timer_obj);
            fprintf('⏸️ 动画已暂停\n');
        end
        
        function stopAnimation(obj)
            % 停止动画
            obj.is_playing = false;
            stop(obj.timer_obj);
            obj.current_frame = 1;
            obj.updateDisplay();
            fprintf('⏹️ 动画已停止\n');
        end
        
        function setSpeed(obj, speed)
            % 设置播放速度
            obj.animation_speed = speed;
            obj.timer_obj.Period = 0.05 / speed;
            
            % 更新速度显示
            speed_display = findobj(obj.main_figure, 'Tag', 'speed_display');
            if ~isempty(speed_display)
                speed_display.String = sprintf('%.1fx', speed);
            end
        end
        
        function seekToFrame(obj, frame)
            % 跳转到指定帧
            obj.current_frame = max(1, min(frame, obj.total_frames));
            obj.updateDisplay();
        end
        
        function updateFrame(obj)
            % 更新动画帧
            if obj.is_playing && obj.current_frame <= obj.total_frames
                obj.updateDisplay();
                obj.current_frame = obj.current_frame + 1;
                
                if obj.current_frame > obj.total_frames
                    obj.pauseAnimation();
                    fprintf('✅ 动画播放完成\n');
                end
            end
        end
        
        function updateDisplay(obj)
            % 更新显示
            try
                % 更新进度条
                obj.progress_bar.Value = obj.current_frame;

                % 更新时间显示
                obj.time_text.String = sprintf('帧: %d/%d', obj.current_frame, obj.total_frames);

                % 更新机器人位置（简化版本）
                frame_ratio = obj.current_frame / obj.total_frames;

                try
                    % 简单的机器人运动模拟
                    q = obj.yumi_robot.homeConfiguration;

                    % 转换为数值数组
                    if isstruct(q)
                        q_numeric = [q.JointPosition];
                    else
                        q_numeric = q;
                    end

                    % 创建运动
                    if length(q_numeric) >= 8
                        q(1).JointPosition = 0.3 * sin(frame_ratio * 4 * pi);
                        q(8).JointPosition = -0.3 * sin(frame_ratio * 4 * pi);
                    end

                    % 更新机器人显示
                    show(obj.yumi_robot, q, 'Parent', obj.axes_3d, 'PreservePlot', true, 'Frames', 'off');

                catch robot_error
                    % 如果机器人更新失败，显示简单的运动指示
                    axes(obj.axes_3d);
                    delete(findobj(obj.axes_3d, 'Tag', 'robot_indicator'));

                    x_pos = 200 * sin(frame_ratio * 4 * pi);
                    plot3(x_pos, 0, 100, 'ro', 'MarkerSize', 15, 'MarkerFaceColor', 'red', 'Tag', 'robot_indicator');
                end

                % 添加积木（根据进度）
                obj.updateBricks(frame_ratio);

                drawnow limitrate;

            catch ME
                fprintf('显示更新错误: %s\n', ME.message);
            end
        end
        
        function updateBricks(obj, progress)
            % 根据进度更新积木显示
            total_bricks = 47;
            bricks_to_show = round(progress * total_bricks);
            
            % 清除之前的积木
            for i = 1:length(obj.brick_objects)
                if isvalid(obj.brick_objects{i})
                    delete(obj.brick_objects{i});
                end
            end
            obj.brick_objects = {};
            
            % 显示新积木
            for i = 1:bricks_to_show
                brick_pos = obj.calculateBrickPosition(i);
                brick_color = obj.getBrickColor(i);
                
                % 创建积木对象
                brick_obj = obj.createBrick(brick_pos, brick_color);
                obj.brick_objects{end+1} = brick_obj;
            end
        end
        
        function pos = calculateBrickPosition(obj, brick_index)
            % 计算积木位置
            layer = ceil(brick_index / 12);
            pos_in_layer = mod(brick_index - 1, 12) + 1;
            
            % 基础位置计算
            x = 400 + 32 * mod(pos_in_layer - 1, 4);
            y = -100 + 32 * floor((pos_in_layer - 1) / 4);
            z = 9.6 * (layer - 1);
            
            pos = [x, y, z];
        end
        
        function color = getBrickColor(obj, brick_index)
            % 获取积木颜色
            colors = {[1, 0, 0], [0, 1, 0], [0, 0, 1], [1, 1, 0], [1, 0, 1], [0, 1, 1]};
            layer = ceil(brick_index / 12);
            color = colors{mod(layer - 1, 6) + 1};
        end
        
        function brick_obj = createBrick(obj, position, color)
            % 创建积木3D对象
            brick_size = [32, 16, 9.6];

            % 创建立方体的顶点
            vertices = [
                0, 0, 0;
                brick_size(1), 0, 0;
                brick_size(1), brick_size(2), 0;
                0, brick_size(2), 0;
                0, 0, brick_size(3);
                brick_size(1), 0, brick_size(3);
                brick_size(1), brick_size(2), brick_size(3);
                0, brick_size(2), brick_size(3)
            ];

            % 平移到正确位置
            vertices = vertices + repmat(position, 8, 1);

            % 定义面
            faces = [
                1, 2, 3, 4;  % 底面
                5, 6, 7, 8;  % 顶面
                1, 2, 6, 5;  % 前面
                3, 4, 8, 7;  % 后面
                1, 4, 8, 5;  % 左面
                2, 3, 7, 6   % 右面
            ];

            % 绘制积木
            brick_obj = patch('Vertices', vertices, ...
                              'Faces', faces, ...
                              'FaceColor', color, ...
                              'EdgeColor', 'black', ...
                              'FaceAlpha', 0.8, ...
                              'LineWidth', 1, ...
                              'Parent', obj.axes_3d);
        end
        
        function addTestBricks(obj)
            % 添加一些测试积木来验证显示
            fprintf('添加测试积木...\n');

            % 创建几个测试积木
            test_positions = [
                100, 100, 0;
                150, 100, 0;
                200, 100, 0;
                100, 150, 10;
                150, 150, 10
            ];

            test_colors = {
                [1, 0, 0],  % 红色
                [0, 1, 0],  % 绿色
                [0, 0, 1],  % 蓝色
                [1, 1, 0],  % 黄色
                [1, 0, 1]   % 紫色
            };

            for i = 1:size(test_positions, 1)
                brick_obj = obj.createBrick(test_positions(i, :), test_colors{i});
                obj.brick_objects{end+1} = brick_obj;
            end

            fprintf('✅ 添加了 %d 个测试积木\n', size(test_positions, 1));
        end

        function closePlayer(obj)
            % 关闭播放器
            if isvalid(obj.timer_obj)
                stop(obj.timer_obj);
                delete(obj.timer_obj);
            end
            delete(obj.main_figure);
        end
    end
end
