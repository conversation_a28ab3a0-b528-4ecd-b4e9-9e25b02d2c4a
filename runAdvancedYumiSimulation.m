function runAdvancedYumiSimulation(varargin)
% 运行高级YuMi机器人Simulink仿真
% 支持乐高堆叠任务、3D可视化、实时监控

fprintf('🚀 === 启动高级YuMi Simulink仿真 === 🚀\n');

% 解析输入参数
p = inputParser;
addParameter(p, 'SimTime', 10, @isnumeric);
addParameter(p, 'TaskMode', 'lego_stacking', @ischar);
addParameter(p, 'Visualization', true, @islogical);
addParameter(p, 'RealTime', false, @islogical);
addParameter(p, 'DataLogging', true, @islogical);
parse(p, varargin{:});

simTime = p.Results.SimTime;
taskMode = p.Results.TaskMode;
enableViz = p.Results.Visualization;
realTime = p.Results.RealTime;
dataLogging = p.Results.DataLogging;

modelName = 'AdvancedYumiSimscape';

% === 1. 检查和准备模型 ===
fprintf('步骤1: 检查和准备模型...\n');

% 检查模型是否存在
if ~exist([modelName '.slx'], 'file')
    fprintf('模型不存在，正在创建...\n');
    createAdvancedYumiSimscapeModel();
    configureYumiPhysicsSimulation();
    integrateYumiControlSystem();
end

% 加载模型
if ~bdIsLoaded(modelName)
    load_system(modelName);
    fprintf('✅ 模型加载完成: %s\n', modelName);
end

% === 2. 配置仿真参数 ===
fprintf('步骤2: 配置仿真参数...\n');

% 设置仿真时间
set_param(modelName, 'StopTime', num2str(simTime));

% 配置求解器
if realTime
    set_param(modelName, 'EnablePacing', 'on');
    set_param(modelName, 'PacingRate', '1');
    fprintf('  ✅ 实时仿真: 启用\n');
else
    set_param(modelName, 'EnablePacing', 'off');
    fprintf('  ✅ 快速仿真: 启用\n');
end

% 配置数据记录
if dataLogging
    set_param(modelName, 'SaveOutput', 'on');
    set_param(modelName, 'SaveTime', 'on');
    set_param(modelName, 'ReturnWorkspaceOutputs', 'on');
    fprintf('  ✅ 数据记录: 启用\n');
else
    set_param(modelName, 'SaveOutput', 'off');
    fprintf('  ✅ 数据记录: 禁用\n');
end

fprintf('  ✅ 仿真时间: %.1f秒\n', simTime);
fprintf('  ✅ 任务模式: %s\n', taskMode);

% === 3. 配置3D可视化 ===
fprintf('步骤3: 配置3D可视化...\n');

if enableViz
    try
        % 打开Mechanics Explorer
        if getSimulinkBlockHandle([modelName '/MechanicsExplorer']) ~= -1
            set_param([modelName '/MechanicsExplorer'], 'StartVisualization', 'on');
            fprintf('  ✅ Mechanics Explorer: 启用\n');
        end
        
        % 配置可视化参数
        configureVisualization(modelName);
        
    catch ME
        fprintf('  ⚠️ 3D可视化配置失败: %s\n', ME.message);
    end
else
    fprintf('  ⚠️ 3D可视化: 禁用\n');
end

% === 4. 设置任务参数 ===
fprintf('步骤4: 设置任务参数...\n');

switch lower(taskMode)
    case 'lego_stacking'
        setupLegoStackingTask(modelName);
        fprintf('  ✅ 乐高堆叠任务配置完成\n');
        
    case 'pick_and_place'
        setupPickAndPlaceTask(modelName);
        fprintf('  ✅ 抓取放置任务配置完成\n');
        
    case 'coordination_test'
        setupCoordinationTest(modelName);
        fprintf('  ✅ 双臂协调测试配置完成\n');
        
    otherwise
        fprintf('  ⚠️ 未知任务模式，使用默认配置\n');
end

% === 5. 启动仿真 ===
fprintf('步骤5: 启动仿真...\n');

try
    % 编译模型
    fprintf('  编译模型...\n');
    eval([modelName '([],[],[],''compile'')']);
    
    % 启动仿真
    fprintf('  🚀 开始仿真 (%.1f秒)...\n', simTime);
    tic;
    
    simOut = sim(modelName, 'StopTime', num2str(simTime));
    
    simElapsed = toc;
    fprintf('  ✅ 仿真完成，耗时: %.2f秒\n', simElapsed);
    
    % 终止编译
    eval([modelName '([],[],[],''term'')']);
    
catch ME
    fprintf('  ❌ 仿真失败: %s\n', ME.message);
    eval([modelName '([],[],[],''term'')']);
    return;
end

% === 6. 处理仿真结果 ===
fprintf('步骤6: 处理仿真结果...\n');

if dataLogging && exist('simOut', 'var')
    processSimulationResults(simOut, modelName);
    fprintf('  ✅ 仿真结果处理完成\n');
end

% === 7. 生成报告 ===
fprintf('步骤7: 生成仿真报告...\n');
generateSimulationReport(modelName, simTime, taskMode, simElapsed);

fprintf('\n🎉 === 高级YuMi仿真完成 === 🎉\n');
fprintf('仿真总结:\n');
fprintf('  📊 仿真时间: %.1f秒\n', simTime);
fprintf('  ⏱️ 计算时间: %.2f秒\n', simElapsed);
fprintf('  🎯 任务模式: %s\n', taskMode);
fprintf('  📈 性能比: %.1fx实时\n', simTime/simElapsed);

if enableViz
    fprintf('\n💡 提示: 请查看Mechanics Explorer窗口观看3D动画\n');
end

end

% === 辅助函数 ===

function configureVisualization(modelName)
% 配置3D可视化参数

try
    % 设置视角
    set_param([modelName '/MechanicsExplorer'], 'ViewerType', 'Mechanics Explorer');
    set_param([modelName '/MechanicsExplorer'], 'ShowMachineEnvironment', 'on');
    
    % 设置显示选项
    set_param([modelName '/MechanicsExplorer'], 'ShowFrameLabels', 'on');
    set_param([modelName '/MechanicsExplorer'], 'ShowMassProperties', 'off');
    
catch
    % 忽略配置错误
end

end

function setupLegoStackingTask(modelName)
% 设置乐高堆叠任务参数

% 在工作空间中设置任务参数
assignin('base', 'task_mode', 1);  % 乐高堆叠模式
assignin('base', 'target_force', 5.0);  % 目标抓取力 (N)
assignin('base', 'stacking_height', 0.02);  % 堆叠高度 (m)

end

function setupPickAndPlaceTask(modelName)
% 设置抓取放置任务参数

assignin('base', 'task_mode', 2);  % 抓取放置模式
assignin('base', 'target_force', 3.0);
assignin('base', 'pick_position', [0.5, 0.2, 0.1]);

end

function setupCoordinationTest(modelName)
% 设置双臂协调测试参数

assignin('base', 'task_mode', 3);  % 协调测试模式
assignin('base', 'sync_tolerance', 0.01);  % 同步容差 (rad)

end

function processSimulationResults(simOut, modelName)
% 处理仿真结果

try
    % 提取时间数据
    if isfield(simOut, 'tout')
        time = simOut.tout;
        fprintf('    数据点数: %d\n', length(time));
    end
    
    % 提取关节数据
    if exist('joint_data', 'var')
        fprintf('    关节数据: 已记录\n');
    end
    
    % 提取力数据
    if exist('force_data', 'var')
        fprintf('    力数据: 已记录\n');
    end
    
catch ME
    fprintf('    ⚠️ 结果处理警告: %s\n', ME.message);
end

end

function generateSimulationReport(modelName, simTime, taskMode, elapsed)
% 生成仿真报告

reportFile = sprintf('YuMi_Simulation_Report_%s.txt', datestr(now, 'yyyymmdd_HHMMSS'));

try
    fid = fopen(reportFile, 'w');
    
    fprintf(fid, '=== YuMi机器人Simulink仿真报告 ===\n\n');
    fprintf(fid, '仿真时间: %s\n', datestr(now));
    fprintf(fid, '模型名称: %s\n', modelName);
    fprintf(fid, '仿真时长: %.1f秒\n', simTime);
    fprintf(fid, '计算时间: %.2f秒\n', elapsed);
    fprintf(fid, '任务模式: %s\n', taskMode);
    fprintf(fid, '性能比: %.1fx实时\n', simTime/elapsed);
    fprintf(fid, '\n=== 系统配置 ===\n');
    fprintf(fid, '- ABB YuMi IRB 14000双臂机器人\n');
    fprintf(fid, '- 21个关节 (7+7+7)\n');
    fprintf(fid, '- Simscape Multibody物理仿真\n');
    fprintf(fid, '- PID轨迹跟踪控制\n');
    fprintf(fid, '- 力/位混合控制\n');
    fprintf(fid, '- 双臂协调控制\n');
    fprintf(fid, '- 3D实时可视化\n');
    
    fclose(fid);
    fprintf('  ✅ 仿真报告已保存: %s\n', reportFile);
    
catch
    fprintf('  ⚠️ 报告生成失败\n');
end

end
