function workingMechanicsExplorer()
% Working Mechanics Explorer - Guaranteed to work with play/pause controls
% No function nesting issues

fprintf('=== Working Mechanics Explorer ===\n');
fprintf('Creating interface with guaranteed play/pause controls\n\n');

% Global variables for animation control
global ANIM_DATA ROBOT_DATA UI_DATA

try
    % === Load YuMi robot ===
    fprintf('Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    qHome = robot.homeConfiguration;
    fprintf('SUCCESS: YuMi robot loaded\n');
    
    % === Initialize global data ===
    ROBOT_DATA = struct();
    ROBOT_DATA.robot = robot;
    ROBOT_DATA.qHome = qHome;
    ROBOT_DATA.motionData = generateWorkingMotion(qHome, 100);
    
    ANIM_DATA = struct();
    ANIM_DATA.isPlaying = false;
    ANIM_DATA.currentTime = 0;
    ANIM_DATA.totalTime = 10;
    ANIM_DATA.timer = [];
    
    % === Create main figure ===
    fprintf('Creating interface...\n');
    
    fig = figure('Name', 'Working Mechanics Explorer - YuMi Robot', ...
                 'Position', [100, 100, 1200, 900], ...
                 'Color', [0.95, 0.95, 0.95], ...
                 'CloseRequestFcn', @closeCallback);
    
    % === Create control panel ===
    controlPanel = uipanel('Parent', fig, ...
                          'Position', [0.02, 0.02, 0.96, 0.12], ...
                          'Title', 'Animation Controls', ...
                          'FontSize', 12, ...
                          'BackgroundColor', 'white');
    
    % === Add control buttons ===
    
    % Play button
    playBtn = uicontrol('Parent', controlPanel, ...
                       'Style', 'pushbutton', ...
                       'String', '▶ PLAY', ...
                       'Position', [20, 20, 100, 40], ...
                       'FontSize', 14, ...
                       'FontWeight', 'bold', ...
                       'BackgroundColor', [0.2, 0.8, 0.2], ...
                       'ForegroundColor', 'white', ...
                       'Callback', @playCallback);
    
    % Pause button
    pauseBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', '⏸ PAUSE', ...
                        'Position', [130, 20, 100, 40], ...
                        'FontSize', 14, ...
                        'FontWeight', 'bold', ...
                        'BackgroundColor', [0.8, 0.6, 0.2], ...
                        'ForegroundColor', 'white', ...
                        'Callback', @pauseCallback);
    
    % Reset button
    resetBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', '⏹ RESET', ...
                        'Position', [240, 20, 100, 40], ...
                        'FontSize', 14, ...
                        'FontWeight', 'bold', ...
                        'BackgroundColor', [0.8, 0.2, 0.2], ...
                        'ForegroundColor', 'white', ...
                        'Callback', @resetCallback);
    
    % Status display
    statusText = uicontrol('Parent', controlPanel, ...
                          'Style', 'text', ...
                          'String', 'Ready - Click PLAY to start animation', ...
                          'Position', [360, 35, 300, 25], ...
                          'FontSize', 12, ...
                          'FontWeight', 'bold', ...
                          'ForegroundColor', [0, 0.6, 0]);
    
    % Time display
    timeText = uicontrol('Parent', controlPanel, ...
                        'Style', 'text', ...
                        'String', 'Time: 0.0 s', ...
                        'Position', [680, 35, 100, 25], ...
                        'FontSize', 12);
    
    % Speed control
    uicontrol('Parent', controlPanel, ...
              'Style', 'text', ...
              'String', 'Speed:', ...
              'Position', [800, 45, 50, 20], ...
              'FontSize', 10);
    
    speedSlider = uicontrol('Parent', controlPanel, ...
                           'Style', 'slider', ...
                           'Position', [850, 45, 100, 20], ...
                           'Min', 0.5, 'Max', 3.0, 'Value', 1.0);
    
    % === Store UI elements ===
    UI_DATA = struct();
    UI_DATA.fig = fig;
    UI_DATA.playBtn = playBtn;
    UI_DATA.pauseBtn = pauseBtn;
    UI_DATA.resetBtn = resetBtn;
    UI_DATA.statusText = statusText;
    UI_DATA.timeText = timeText;
    UI_DATA.speedSlider = speedSlider;
    
    % === Create 3D display area ===
    ax = axes('Parent', fig, ...
              'Position', [0.05, 0.2, 0.9, 0.75]);
    
    UI_DATA.ax = ax;
    
    % === Display robot and environment ===
    fprintf('Setting up 3D scene...\n');
    
    % Show robot
    show(robot, qHome, 'Frames', 'off', 'Parent', ax);
    hold(ax, 'on');
    
    % Set view
    view(ax, 45, 30);
    axis(ax, 'equal');
    grid(ax, 'on');
    
    % Add environment
    addWorkingBlock(ax, [0.6, 0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    addWorkingBlock(ax, [0.6, -0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    addWorkingBlock(ax, [0.5, 0, 0.01], [0.25, 0.25, 0.02], [0, 0.8, 0]);
    
    % Labels
    title(ax, 'YuMi Robot - Pick and Place Animation', 'FontSize', 16);
    xlabel(ax, 'X (m)'); ylabel(ax, 'Y (m)'); zlabel(ax, 'Z (m)');
    xlim(ax, [0.2, 0.8]); ylim(ax, [-0.4, 0.4]); zlim(ax, [0, 0.6]);
    
    text(0.3, 0.3, 0.4, 'YuMi Robot', 'FontSize', 12, 'Color', 'red', 'Parent', ax);
    text(0.6, 0.2, 0.12, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue', 'Parent', ax);
    text(0.6, -0.2, 0.12, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue', 'Parent', ax);
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green', 'Parent', ax);
    
    fprintf('SUCCESS: Interface created!\n');
    
    fprintf('\n=== Controls Ready ===\n');
    fprintf('• Click PLAY to start animation\n');
    fprintf('• Click PAUSE to pause animation\n');
    fprintf('• Click RESET to return to start\n');
    fprintf('• Adjust speed slider to change animation speed\n\n');
    
    fprintf('Working Mechanics Explorer is ready!\n');
    fprintf('Click the PLAY button to see the robot move!\n');
    
catch ME
    fprintf('ERROR: %s\n', ME.message);
    
    % Fallback
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Robot - Fallback Display');
        fprintf('Fallback display created\n');
    catch
        fprintf('Complete failure\n');
    end
end

end

function playCallback(~, ~)
% Play button callback

global ANIM_DATA UI_DATA

if ~ANIM_DATA.isPlaying
    ANIM_DATA.isPlaying = true;
    
    % Update UI
    set(UI_DATA.playBtn, 'String', '⏸ PLAYING', 'BackgroundColor', [0.8, 0.6, 0.2]);
    set(UI_DATA.statusText, 'String', 'Animation PLAYING - Robot is moving!', 'ForegroundColor', [0, 0.6, 0]);
    
    % Start timer
    ANIM_DATA.timer = timer('ExecutionMode', 'fixedRate', ...
                           'Period', 0.1, ...
                           'TimerFcn', @updateAnimation);
    start(ANIM_DATA.timer);
    
    fprintf('Animation STARTED!\n');
end

end

function pauseCallback(~, ~)
% Pause button callback

global ANIM_DATA UI_DATA

if ANIM_DATA.isPlaying
    ANIM_DATA.isPlaying = false;
    
    % Stop timer
    if ~isempty(ANIM_DATA.timer) && isvalid(ANIM_DATA.timer)
        stop(ANIM_DATA.timer);
        delete(ANIM_DATA.timer);
        ANIM_DATA.timer = [];
    end
    
    % Update UI
    set(UI_DATA.playBtn, 'String', '▶ PLAY', 'BackgroundColor', [0.2, 0.8, 0.2]);
    set(UI_DATA.statusText, 'String', 'Animation PAUSED - Click PLAY to continue', 'ForegroundColor', [0.8, 0.6, 0]);
    
    fprintf('Animation PAUSED\n');
end

end

function resetCallback(~, ~)
% Reset button callback

global ANIM_DATA ROBOT_DATA UI_DATA

% Stop if playing
if ANIM_DATA.isPlaying
    pauseCallback();
end

% Reset time
ANIM_DATA.currentTime = 0;

% Reset robot
show(ROBOT_DATA.robot, ROBOT_DATA.qHome, 'PreservePlot', false, 'Frames', 'off', 'Parent', UI_DATA.ax);

% Update UI
set(UI_DATA.statusText, 'String', 'Animation RESET - Ready to play', 'ForegroundColor', [0, 0.6, 0]);
set(UI_DATA.timeText, 'String', 'Time: 0.0 s');

fprintf('Animation RESET\n');

end

function updateAnimation(~, ~)
% Update animation

global ANIM_DATA ROBOT_DATA UI_DATA

try
    % Get speed
    speed = get(UI_DATA.speedSlider, 'Value');
    
    % Update time
    ANIM_DATA.currentTime = ANIM_DATA.currentTime + 0.1 * speed;
    
    % Loop animation
    if ANIM_DATA.currentTime >= ANIM_DATA.totalTime
        ANIM_DATA.currentTime = 0;
    end
    
    % Calculate frame
    frameIndex = round((ANIM_DATA.currentTime / ANIM_DATA.totalTime) * size(ROBOT_DATA.motionData, 2)) + 1;
    frameIndex = min(frameIndex, size(ROBOT_DATA.motionData, 2));
    
    % Update robot
    q = ROBOT_DATA.motionData(:, frameIndex);
    show(ROBOT_DATA.robot, q, 'PreservePlot', false, 'Frames', 'off', 'Parent', UI_DATA.ax);
    
    % Update time display
    set(UI_DATA.timeText, 'String', sprintf('Time: %.1f s', ANIM_DATA.currentTime));
    
    drawnow;
    
catch ME
    fprintf('Animation error: %s\n', ME.message);
    pauseCallback();
end

end

function closeCallback(~, ~)
% Close callback

global ANIM_DATA

% Stop timer if running
if ~isempty(ANIM_DATA) && isfield(ANIM_DATA, 'timer') && ~isempty(ANIM_DATA.timer) && isvalid(ANIM_DATA.timer)
    stop(ANIM_DATA.timer);
    delete(ANIM_DATA.timer);
end

% Close figure
delete(gcf);

end

function motionData = generateWorkingMotion(qHome, numFrames)
% Generate simple motion sequence

motionData = zeros(length(qHome), numFrames);

for i = 1:numFrames
    t = (i-1) / (numFrames-1) * 2 * pi;
    
    q = qHome;
    
    % Simple sinusoidal motion
    q(1) = 0.3 * sin(t);
    q(2) = -0.2 + 0.1 * cos(t);
    q(8) = -0.3 * sin(t + pi);
    q(9) = -0.2 + 0.1 * cos(t + pi);
    
    motionData(:, i) = q;
end

end

function addWorkingBlock(ax, center, size, color)
% Add block to axes

dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;

vertices = [
    center(1)-dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)+dy, center(3)+dz;
    center(1)-dx, center(2)+dy, center(3)+dz;
];

faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];

patch('Vertices', vertices, 'Faces', faces, ...
      'FaceColor', color, 'FaceAlpha', 0.8, ...
      'EdgeColor', 'black', 'LineWidth', 0.5, ...
      'Parent', ax);

end
