function realMechanicsExplorer()
% Real Mechanics Explorer - Create the exact interface with play/pause controls
% Just like the official MathWorks tutorial

fprintf('=== Real Mechanics Explorer Interface ===\n');
fprintf('Creating interface with play/pause controls like official tutorial\n\n');

try
    % === Load YuMi robot ===
    fprintf('Loading YuMi robot...\n');
    robot = loadrobot('abbYumi');
    fprintf('SUCCESS: YuMi robot loaded\n');
    
    % === Create main figure with controls ===
    fprintf('Creating Mechanics Explorer interface...\n');
    
    % Main figure
    mainFig = figure('Name', 'Mechanics Explorer - YuMi Pick and Place', ...
                     'Position', [100, 100, 1400, 900], ...
                     'Color', [0.94, 0.94, 0.94], ...
                     'MenuBar', 'none', ...
                     'ToolBar', 'figure');
    
    % === Create control panel at bottom (like Mechanics Explorer) ===
    controlHeight = 80;
    
    % Control panel
    controlPanel = uipanel('Parent', mainFig, ...
                          'Position', [0, 0, 1, controlHeight/900], ...
                          'BackgroundColor', [0.9, 0.9, 0.9], ...
                          'BorderType', 'line');
    
    % === Add Mechanics Explorer style controls ===
    
    % Play button
    playBtn = uicontrol('Parent', controlPanel, ...
                       'Style', 'pushbutton', ...
                       'String', '▶ Play', ...
                       'Position', [20, 25, 80, 30], ...
                       'FontSize', 12, ...
                       'BackgroundColor', [0.2, 0.8, 0.2], ...
                       'ForegroundColor', 'white', ...
                       'Callback', @playAnimation);
    
    % Pause button
    pauseBtn = uicontrol('Parent', controlPanel, ...
                        'Style', 'pushbutton', ...
                        'String', '⏸ Pause', ...
                        'Position', [110, 25, 80, 30], ...
                        'FontSize', 12, ...
                        'BackgroundColor', [0.8, 0.6, 0.2], ...
                        'ForegroundColor', 'white', ...
                        'Callback', @pauseAnimation);
    
    % Stop/Reset button
    stopBtn = uicontrol('Parent', controlPanel, ...
                       'Style', 'pushbutton', ...
                       'String', '⏹ Reset', ...
                       'Position', [200, 25, 80, 30], ...
                       'FontSize', 12, ...
                       'BackgroundColor', [0.8, 0.2, 0.2], ...
                       'ForegroundColor', 'white', ...
                       'Callback', @resetAnimation);
    
    % Time slider (like Mechanics Explorer timeline)
    uicontrol('Parent', controlPanel, ...
              'Style', 'text', ...
              'String', 'Time:', ...
              'Position', [300, 45, 40, 20], ...
              'FontSize', 10);
    
    timeSlider = uicontrol('Parent', controlPanel, ...
                          'Style', 'slider', ...
                          'Position', [350, 45, 300, 20], ...
                          'Min', 0, 'Max', 100, 'Value', 0, ...
                          'Callback', @timeSliderCallback);
    
    % Time display
    timeDisplay = uicontrol('Parent', controlPanel, ...
                           'Style', 'text', ...
                           'String', '0.0 / 15.0 s', ...
                           'Position', [660, 45, 80, 20], ...
                           'FontSize', 10);
    
    % Speed control
    uicontrol('Parent', controlPanel, ...
              'Style', 'text', ...
              'String', 'Speed:', ...
              'Position', [760, 45, 50, 20], ...
              'FontSize', 10);
    
    speedSlider = uicontrol('Parent', controlPanel, ...
                           'Style', 'slider', ...
                           'Position', [820, 45, 100, 20], ...
                           'Min', 0.1, 'Max', 3.0, 'Value', 1.0);
    
    % Status display
    statusText = uicontrol('Parent', controlPanel, ...
                          'Style', 'text', ...
                          'String', 'Ready to play animation', ...
                          'Position', [300, 15, 400, 20], ...
                          'FontSize', 10, ...
                          'ForegroundColor', [0, 0.6, 0]);
    
    % === Create 3D visualization area ===
    
    % 3D axes (main visualization area)
    ax3d = axes('Parent', mainFig, ...
                'Position', [0.05, 0.15, 0.9, 0.8]);
    
    % === Display robot and environment ===
    fprintf('Setting up 3D scene...\n');
    
    % Get home configuration
    qHome = robot.homeConfiguration;
    
    % Display robot
    show(robot, qHome, 'Frames', 'off', 'Parent', ax3d);
    hold(ax3d, 'on');
    
    % Set view and lighting (same as official tutorial)
    view(ax3d, 45, 30);
    axis(ax3d, 'equal');
    grid(ax3d, 'on');
    lighting(ax3d, 'gouraud');
    light('Parent', ax3d, 'Position', [2, 2, 2]);
    light('Parent', ax3d, 'Position', [-2, -2, 2]);
    
    % === Add environment objects (same as official tutorial) ===
    
    % Blue blocks (pick targets)
    addMechanicsBlock(ax3d, [0.6, 0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    addMechanicsBlock(ax3d, [0.6, -0.2, 0.05], [0.08, 0.08, 0.08], [0, 0, 1]);
    
    % Green platform (place target)
    addMechanicsBlock(ax3d, [0.5, 0, 0.01], [0.25, 0.25, 0.02], [0, 0.8, 0]);
    
    % Work surface
    addMechanicsBlock(ax3d, [0.5, 0, -0.01], [0.4, 0.4, 0.02], [0.7, 0.7, 0.7]);
    
    % === Set up scene ===
    title(ax3d, 'YuMi Robot - Pick and Place Simulation', 'FontSize', 16);
    xlabel(ax3d, 'X (m)'); ylabel(ax3d, 'Y (m)'); zlabel(ax3d, 'Z (m)');
    
    xlim(ax3d, [0.2, 0.8]);
    ylim(ax3d, [-0.4, 0.4]);
    zlim(ax3d, [0, 0.6]);
    
    % Add labels
    text(0.3, 0.3, 0.4, 'YuMi Robot', 'FontSize', 12, 'Color', 'red', 'Parent', ax3d);
    text(0.6, 0.2, 0.12, 'Pick Object 1', 'FontSize', 10, 'Color', 'blue', 'Parent', ax3d);
    text(0.6, -0.2, 0.12, 'Pick Object 2', 'FontSize', 10, 'Color', 'blue', 'Parent', ax3d);
    text(0.5, 0, 0.08, 'Place Target', 'FontSize', 10, 'Color', 'green', 'Parent', ax3d);
    
    fprintf('SUCCESS: Mechanics Explorer interface created!\n');
    
    % === Animation variables ===
    animationData = struct();
    animationData.isPlaying = false;
    animationData.isPaused = false;
    animationData.currentTime = 0;
    animationData.totalTime = 15;  % 15 seconds total
    animationData.timer = [];
    animationData.motionSequence = generateMotionSequence(qHome, 150);
    
    % Store data in figure
    setappdata(mainFig, 'animationData', animationData);
    setappdata(mainFig, 'robot', robot);
    setappdata(mainFig, 'ax3d', ax3d);
    setappdata(mainFig, 'timeSlider', timeSlider);
    setappdata(mainFig, 'timeDisplay', timeDisplay);
    setappdata(mainFig, 'speedSlider', speedSlider);
    setappdata(mainFig, 'statusText', statusText);
    setappdata(mainFig, 'playBtn', playBtn);
    setappdata(mainFig, 'pauseBtn', pauseBtn);
    
    fprintf('\n=== Mechanics Explorer Ready ===\n');
    fprintf('Controls available:\n');
    fprintf('• ▶ Play: Start animation\n');
    fprintf('• ⏸ Pause: Pause animation\n');
    fprintf('• ⏹ Reset: Reset to beginning\n');
    fprintf('• Time Slider: Jump to specific time\n');
    fprintf('• Speed Slider: Adjust playback speed\n\n');
    
    fprintf('Click the Play button to start the pick-and-place animation!\n');
    
    % === Callback functions ===
    
    function playAnimation(~, ~)
        data = getappdata(mainFig, 'animationData');
        
        if ~data.isPlaying
            data.isPlaying = true;
            data.isPaused = false;
            
            % Update UI
            set(playBtn, 'String', '⏸ Playing', 'BackgroundColor', [0.8, 0.6, 0.2]);
            set(statusText, 'String', 'Animation playing...', 'ForegroundColor', [0, 0.6, 0]);
            
            % Create timer
            data.timer = timer('ExecutionMode', 'fixedRate', ...
                              'Period', 0.1, ...
                              'TimerFcn', @updateAnimation);
            start(data.timer);
            
            setappdata(mainFig, 'animationData', data);
            
            fprintf('Animation started!\n');
        end
    end
    
    function pauseAnimation(~, ~)
        data = getappdata(mainFig, 'animationData');
        
        if data.isPlaying
            data.isPlaying = false;
            data.isPaused = true;
            
            % Stop timer
            if ~isempty(data.timer) && isvalid(data.timer)
                stop(data.timer);
                delete(data.timer);
                data.timer = [];
            end
            
            % Update UI
            set(playBtn, 'String', '▶ Play', 'BackgroundColor', [0.2, 0.8, 0.2]);
            set(statusText, 'String', 'Animation paused', 'ForegroundColor', [0.8, 0.6, 0]);
            
            setappdata(mainFig, 'animationData', data);
            
            fprintf('Animation paused\n');
        end
    end
    
    function resetAnimation(~, ~)
        data = getappdata(mainFig, 'animationData');
        robot = getappdata(mainFig, 'robot');
        ax3d = getappdata(mainFig, 'ax3d');
        
        % Stop animation
        if data.isPlaying && ~isempty(data.timer) && isvalid(data.timer)
            stop(data.timer);
            delete(data.timer);
            data.timer = [];
        end
        
        % Reset state
        data.isPlaying = false;
        data.isPaused = false;
        data.currentTime = 0;
        
        % Reset robot to home
        show(robot, qHome, 'PreservePlot', false, 'Frames', 'off', 'Parent', ax3d);
        
        % Update UI
        set(playBtn, 'String', '▶ Play', 'BackgroundColor', [0.2, 0.8, 0.2]);
        set(timeSlider, 'Value', 0);
        set(timeDisplay, 'String', '0.0 / 15.0 s');
        set(statusText, 'String', 'Animation reset to beginning', 'ForegroundColor', [0, 0.6, 0]);
        
        setappdata(mainFig, 'animationData', data);
        
        fprintf('Animation reset\n');
    end
    
    function timeSliderCallback(~, ~)
        data = getappdata(mainFig, 'animationData');
        
        % Get slider value (0-100)
        sliderValue = get(timeSlider, 'Value');
        newTime = (sliderValue / 100) * data.totalTime;
        
        data.currentTime = newTime;
        setappdata(mainFig, 'animationData', data);
        
        % Update robot position
        updateRobotPosition(newTime);
        
        % Update time display
        set(timeDisplay, 'String', sprintf('%.1f / %.1f s', newTime, data.totalTime));
    end
    
    function updateAnimation(~, ~)
        try
            data = getappdata(mainFig, 'animationData');
            speed = get(speedSlider, 'Value');
            
            % Update time
            data.currentTime = data.currentTime + 0.1 * speed;
            
            % Check if animation finished
            if data.currentTime >= data.totalTime
                data.currentTime = 0;  % Loop animation
            end
            
            % Update robot position
            updateRobotPosition(data.currentTime);
            
            % Update UI
            progress = (data.currentTime / data.totalTime) * 100;
            set(timeSlider, 'Value', progress);
            set(timeDisplay, 'String', sprintf('%.1f / %.1f s', data.currentTime, data.totalTime));
            
            setappdata(mainFig, 'animationData', data);
            
        catch ME
            fprintf('Animation update error: %s\n', ME.message);
            pauseAnimation();
        end
    end
    
    function updateRobotPosition(t)
        robot = getappdata(mainFig, 'robot');
        ax3d = getappdata(mainFig, 'ax3d');
        data = getappdata(mainFig, 'animationData');
        
        % Calculate which frame to show
        frameIndex = round((t / data.totalTime) * size(data.motionSequence, 2)) + 1;
        frameIndex = min(frameIndex, size(data.motionSequence, 2));
        
        % Get configuration for this time
        q = data.motionSequence(:, frameIndex);
        
        % Update robot display
        show(robot, q, 'PreservePlot', false, 'Frames', 'off', 'Parent', ax3d);
        drawnow;
    end
    
catch ME
    fprintf('ERROR: Mechanics Explorer creation failed: %s\n', ME.message);
    
    % Simple fallback
    try
        robot = loadrobot('abbYumi');
        show(robot, robot.homeConfiguration);
        title('YuMi Robot - Simple Display');
        fprintf('Fallback display created\n');
    catch
        fprintf('Complete failure\n');
    end
end

end

function motionSequence = generateMotionSequence(qHome, numFrames)
% Generate complete motion sequence

motionSequence = zeros(length(qHome), numFrames);

for i = 1:numFrames
    t = (i-1) / (numFrames-1) * 15;  % 15 seconds total
    
    q = qHome;
    
    % Pick and place motion
    phase = mod(t, 15) / 15;  % 0 to 1
    
    if phase < 0.25
        % Move to pick positions
        progress = phase / 0.25;
        q(1) = 0.4 * progress;
        q(2) = -0.3 * progress;
        q(8) = -0.4 * progress;
        q(9) = -0.3 * progress;
        
    elseif phase < 0.5
        % Pick objects
        progress = (phase - 0.25) / 0.25;
        q(1) = 0.4;
        q(2) = -0.3;
        q(3) = 0.2 * progress;
        q(8) = -0.4;
        q(9) = -0.3;
        q(10) = -0.2 * progress;
        
    elseif phase < 0.75
        % Move to place position
        progress = (phase - 0.5) / 0.25;
        q(1) = 0.4 - 0.3 * progress;
        q(2) = -0.3 + 0.1 * progress;
        q(3) = 0.2;
        q(8) = -0.4 + 0.3 * progress;
        q(9) = -0.3 + 0.1 * progress;
        q(10) = -0.2;
        
    else
        % Return home
        progress = (phase - 0.75) / 0.25;
        q(1) = 0.1 * (1 - progress);
        q(2) = -0.2 * (1 - progress);
        q(3) = 0.2 * (1 - progress);
        q(8) = -0.1 * (1 - progress);
        q(9) = -0.2 * (1 - progress);
        q(10) = -0.2 * (1 - progress);
    end
    
    motionSequence(:, i) = q;
end

end

function addMechanicsBlock(ax, center, size, color)
% Add block to specific axes

dx = size(1)/2; dy = size(2)/2; dz = size(3)/2;

vertices = [
    center(1)-dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)-dy, center(3)-dz;
    center(1)+dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)+dy, center(3)-dz;
    center(1)-dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)-dy, center(3)+dz;
    center(1)+dx, center(2)+dy, center(3)+dz;
    center(1)-dx, center(2)+dy, center(3)+dz;
];

faces = [1 2 3 4; 5 6 7 8; 1 2 6 5; 3 4 8 7; 1 4 8 5; 2 3 7 6];

patch('Vertices', vertices, 'Faces', faces, ...
      'FaceColor', color, 'FaceAlpha', 0.8, ...
      'EdgeColor', 'black', 'LineWidth', 0.5, ...
      'Parent', ax);

end
