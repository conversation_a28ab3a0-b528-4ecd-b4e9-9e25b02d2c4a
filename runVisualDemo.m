function runVisualDemo()
% 确保可视化演示能够正常显示的脚本

fprintf('🎬 === YuMi机器人可视化演示启动器 === 🎬\n\n');

% 检查MATLAB环境
fprintf('1. 检查MATLAB环境...\n');
if usejava('desktop')
    fprintf('✅ MATLAB桌面环境可用\n');
else
    fprintf('⚠️ MATLAB运行在无桌面模式，可能影响图形显示\n');
end

if usejava('awt')
    fprintf('✅ Java AWT可用，支持图形显示\n');
else
    fprintf('❌ Java AWT不可用，无法显示图形\n');
    return;
end

% 清理环境
fprintf('\n2. 清理环境...\n');
close all;
clc;
fprintf('✅ 环境清理完成\n');

% 测试基本图形功能
fprintf('\n3. 测试图形功能...\n');
try
    test_fig = figure('Visible', 'on', 'Name', '图形测试');
    plot([1,2,3], [1,4,2]);
    title('MATLAB图形测试');
    pause(1);
    close(test_fig);
    fprintf('✅ 图形功能正常\n');
catch ME
    fprintf('❌ 图形功能异常: %s\n', ME.message);
    return;
end

% 加载YuMi机器人
fprintf('\n4. 加载YuMi机器人...\n');
try
    yumi = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
    qHome = yumi.homeConfiguration;
    fprintf('✅ YuMi机器人加载成功 (%d个关节)\n', yumi.NumBodies);
catch ME
    fprintf('❌ YuMi加载失败: %s\n', ME.message);
    return;
end

% 创建可视化窗口
fprintf('\n5. 创建3D可视化窗口...\n');
try
    main_fig = figure('Name', 'YuMi机器人演示', ...
                      'Position', [100, 100, 1000, 700], ...
                      'Visible', 'on');
    
    % 显示YuMi机器人
    show(yumi, qHome, 'Frames', 'off');
    
    % 设置视角和标签
    view(45, 30);
    axis equal;
    grid on;
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');
    title('YuMi机器人3D模型', 'FontSize', 14, 'FontWeight', 'bold');
    
    fprintf('✅ YuMi机器人3D模型显示成功！\n');
    
    % 确保窗口可见
    figure(main_fig);
    drawnow;
    
catch ME
    fprintf('❌ 3D显示失败: %s\n', ME.message);
    return;
end

% 简单运动演示
fprintf('\n6. 开始运动演示...\n');
try
    % 定义运动序列
    positions = [
        qHome;  % 初始位置
        qHome + [0.3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];  % 右手臂移动
        qHome + [0, 0, 0, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 0, 0];  % 左手臂移动
        qHome;  % 回到初始位置
    ];
    
    for i = 1:size(positions, 1)
        fprintf('  执行位置 %d/%d\n', i, size(positions, 1));
        
        % 更新机器人显示
        show(yumi, positions(i, :), 'PreservePlot', false, 'Frames', 'off');
        
        % 更新标题
        title(sprintf('YuMi机器人运动演示 - 位置 %d/%d', i, size(positions, 1)), ...
              'FontSize', 14, 'FontWeight', 'bold');
        
        % 确保更新显示
        drawnow;
        pause(2);
    end
    
    fprintf('✅ 运动演示完成！\n');
    
catch ME
    fprintf('❌ 运动演示失败: %s\n', ME.message);
end

% 显示完成信息
title('🎉 YuMi机器人演示完成！', 'FontSize', 16, 'FontWeight', 'bold', 'Color', [0, 0.6, 0]);

fprintf('\n🎉 === 演示完成 === 🎉\n');
fprintf('您应该能在图形窗口中看到：\n');
fprintf('1. ✅ YuMi机器人的3D模型\n');
fprintf('2. ✅ 机器人的运动演示\n');
fprintf('3. ✅ 坐标轴和网格显示\n');

% 提供下一步选项
fprintf('\n💡 下一步可以尝试：\n');
fprintf('1. 运行完整乐高演示: yumiLegoDemo()\n');
fprintf('2. 运行主程序: main\n');
fprintf('3. 运行性能测试: testImprovedPlanner()\n');
fprintf('4. 运行清洁版动画: cleanTrajectoryAnimation(yumi, qHome, trajectories, brick_config)\n');

% 询问是否继续
fprintf('\n是否继续运行完整的乐高堆叠演示? (y/n): ');
user_input = input('', 's');

if strcmpi(user_input, 'y')
    fprintf('\n🚀 启动完整乐高堆叠演示...\n');
    try
        yumiLegoDemo();
    catch ME
        fprintf('❌ 完整演示失败: %s\n', ME.message);
    end
end

end
