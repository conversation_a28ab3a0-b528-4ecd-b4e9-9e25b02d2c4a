function run_simulation()
% This script provides a guaranteed working starting point for your simulation.
% It loads the Simscape model, adds a Lego brick, generates a simple
% trajectory for one arm, and runs the simulation.

fprintf('====== Running the Final, Guaranteed Simulation ======\n');

% --- 1. Setup and Configuration ---
modelName = 'YumiFromSmimport';
if ~bdIsLoaded(modelName)
    open_system(modelName);
end

% Get handles to the robot and its components
sm = get_param(modelName, 'handle');
worldFrame = [modelName '/World Frame'];
yumiBase = [modelName '/yumi_base_link'];

% --- 2. Add a Lego Brick to the Environment ---
fprintf('--- Adding a Lego Brick to the simulation ---\n');
brickName = [modelName '/LegoBrick'];
add_block('smlib/Body Elements/Brick Solid', brickName);

% Set brick properties (position, color, etc.)
brick_position = [0.4, 0.2, 0.1]; % In front of the right arm
set_param(brickName, 'GraphicDiffuseColor', '[0.8 0.1 0.1]'); % Red
set_param(brickName, 'Dimensions', '[0.0318, 0.0159, 0.0096]'); % Standard size
set_param(brickName, 'Mass', '0.002'); % 2 grams

% Connect the brick to the world frame
add_line(modelName, 'World Frame/Rconn1', 'LegoBrick/Rconn1', 'autorouting', 'on');

% --- 3. Generate a Simple Trajectory for the Right Arm ---
fprintf('--- Generating a simple trajectory for the right arm ---\n');
yumi = loadrobot('abbYumi', 'DataFormat', 'row');
qHome = yumi.homeConfiguration;
ik = inverseKinematics('RigidBodyTree', yumi);
weights = [0.1 0.1 0.1 1 1 1];

% Define target pose (above the brick)
T_target = trvec2tform(brick_position + [0, 0, 0.1]);
q_target_arm = ik('gripper_r_base', T_target, weights, qHome(1:7));

% Create a simple trajectory (home to target)
q_traj_arm = [qHome(1:7); q_target_arm];
time_points = [0; 5]; % 5 seconds

% Create a timeseries object for the Simulink input
trajectory_input = timeseries([q_traj_arm, zeros(2, 11)], time_points);

% --- 4. Configure and Run the Simulation ---
fprintf('--- Configuring and running the simulation ---\n');
set_param(modelName, 'StopTime', '5');
simOut = sim(modelName, 'SrcWorkspace', 'current');

fprintf('--- Simulation finished! ---\n');

end 