function testYuMiSystem()
% YuMi乐高拼接系统完整测试脚本
% 验证所有功能是否正常工作

fprintf('🧪 === YuMi系统完整测试 === 🧪\n');
fprintf('测试目标：验证与参考图片相同的效果\n\n');

%% 第一阶段：环境测试
fprintf('📋 第一阶段：环境测试\n');
test_environment();

%% 第二阶段：机器人模型测试
fprintf('\n🤖 第二阶段：机器人模型测试\n');
test_robot_model();

%% 第三阶段：LDR文件测试
fprintf('\n🏗️ 第三阶段：LDR文件测试\n');
test_ldr_parsing();

%% 第四阶段：完整系统测试
fprintf('\n🎬 第四阶段：完整系统测试\n');
test_complete_system();

%% 第五阶段：动画测试
fprintf('\n🎭 第五阶段：动画测试\n');
test_animation_system();

fprintf('\n🎉 === 测试完成 === 🎉\n');

end

function test_environment()
% 测试环境和依赖

fprintf('检查必要文件...\n');
required_files = {
    'mainbu.ldr', 
    'LDRParser.m', 
    'EnhancedYumiAssembly.m', 
    'runEnhancedYumiSystem.m'
};

for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('  ✅ %s 存在\n', required_files{i});
    else
        fprintf('  ❌ %s 缺失\n', required_files{i});
        error('缺少必要文件: %s', required_files{i});
    end
end

fprintf('检查MATLAB工具箱...\n');
if exist('loadrobot', 'file')
    fprintf('  ✅ Robotics System Toolbox 可用\n');
else
    fprintf('  ❌ Robotics System Toolbox 不可用\n');
    error('需要安装 Robotics System Toolbox');
end

fprintf('✅ 环境测试通过\n');

end

function test_robot_model()
% 测试YuMi机器人模型

fprintf('加载YuMi机器人模型...\n');
try
    robot = loadrobot('abbYumi', 'DataFormat', 'row');
    fprintf('  ✅ YuMi机器人加载成功\n');
    
    % 检查机器人结构
    fprintf('  机器人信息:\n');
    fprintf('    - 关节数量: %d\n', robot.NumBodies);
    fprintf('    - 基座名称: %s\n', robot.BaseName);
    
    % 测试显示
    fprintf('测试机器人显示...\n');
    test_fig = figure('Name', '机器人模型测试', 'Position', [100, 100, 800, 600]);
    
    config = robot.homeConfiguration;
    show(robot, config, 'Visuals', 'on', 'Frames', 'off');
    
    % 设置视角
    view(45, 30);
    axis equal;
    grid on;
    xlabel('X (m)');
    ylabel('Y (m)');
    zlabel('Z (m)');
    title('YuMi机器人模型测试');
    
    fprintf('  ✅ 机器人显示成功\n');
    fprintf('  💡 请检查是否看到完整的YuMi机器人（黄色基座+灰色双臂）\n');
    
    % 等待用户确认
    response = input('  ❓ 是否看到完整的YuMi机器人模型？(y/n): ', 's');
    if strcmpi(response, 'y')
        fprintf('  ✅ 机器人模型显示正常\n');
    else
        fprintf('  ⚠️ 机器人模型显示异常\n');
        fprintf('  🔧 尝试调整视角和范围...\n');
        
        % 调整显示参数
        xlim([-0.5, 0.8]);
        ylim([-0.6, 0.6]);
        zlim([-0.3, 0.8]);
        drawnow;
        
        response2 = input('  ❓ 现在是否看到机器人？(y/n): ', 's');
        if strcmpi(response2, 'y')
            fprintf('  ✅ 调整后显示正常\n');
        else
            fprintf('  ❌ 机器人显示仍有问题\n');
        end
    end
    
    close(test_fig);
    
catch ME
    fprintf('  ❌ YuMi机器人测试失败: %s\n', ME.message);
    error('机器人模型测试失败');
end

fprintf('✅ 机器人模型测试通过\n');

end

function test_ldr_parsing()
% 测试LDR文件解析

fprintf('测试LDR文件解析...\n');
try
    parser = LDRParser('mainbu.ldr');
    success = parser.parseLDRFile();
    
    if success
        fprintf('  ✅ LDR文件解析成功\n');
        fprintf('  积木信息:\n');
        fprintf('    - 总积木数: %d\n', parser.total_bricks);
        
        % 分析积木分布
        positions = reshape([parser.bricks.position], 3, [])';
        fprintf('    - X范围: %.1f 到 %.1f mm\n', min(positions(:,1)), max(positions(:,1)));
        fprintf('    - Y范围: %.1f 到 %.1f mm\n', min(positions(:,2)), max(positions(:,2)));
        fprintf('    - Z范围: %.1f 到 %.1f mm\n', min(positions(:,3)), max(positions(:,3)));
        
        % 分析任务分配
        left_count = sum(positions(:,2) >= -100);
        right_count = sum(positions(:,2) < -100);
        fprintf('    - 左臂任务: %d 个积木\n', left_count);
        fprintf('    - 右臂任务: %d 个积木\n', right_count);
        
        if left_count > 0 && right_count > 0
            fprintf('  ✅ 双臂任务分配正常\n');
        else
            fprintf('  ⚠️ 任务分配可能不均衡\n');
        end
        
    else
        error('LDR文件解析失败');
    end
    
catch ME
    fprintf('  ❌ LDR解析测试失败: %s\n', ME.message);
    error('LDR文件测试失败');
end

fprintf('✅ LDR文件测试通过\n');

end

function test_complete_system()
% 测试完整系统

fprintf('启动完整拼接系统...\n');
try
    % 创建系统
    assembly_system = EnhancedYumiAssembly();
    
    fprintf('  ✅ 拼接系统创建成功\n');
    fprintf('  系统信息:\n');
    fprintf('    - 目标积木数: %d\n', length(assembly_system.target_bricks));
    fprintf('    - 拼接步骤数: %d\n', assembly_system.total_steps);
    fprintf('    - 当前步骤: %d\n', assembly_system.current_step);
    
    % 检查界面
    if isvalid(assembly_system.main_figure)
        fprintf('  ✅ 用户界面创建成功\n');
        
        % 等待用户确认界面
        fprintf('  💡 请检查界面是否包含以下元素:\n');
        fprintf('    - 3D显示区域（上方）\n');
        fprintf('    - 控制面板（下方）\n');
        fprintf('    - 播放控制按钮\n');
        fprintf('    - 进度条\n');
        fprintf('    - 夹爪控制按钮\n');
        
        response = input('  ❓ 界面显示是否正常？(y/n): ', 's');
        if strcmpi(response, 'y')
            fprintf('  ✅ 界面显示正常\n');
        else
            fprintf('  ⚠️ 界面显示异常\n');
        end
        
    else
        error('用户界面创建失败');
    end
    
catch ME
    fprintf('  ❌ 完整系统测试失败: %s\n', ME.message);
    error('完整系统测试失败');
end

fprintf('✅ 完整系统测试通过\n');

end

function test_animation_system()
% 测试动画系统

fprintf('测试动画系统...\n');
fprintf('  💡 请手动测试以下功能:\n');
fprintf('    1. 点击"开始拼接"按钮\n');
fprintf('    2. 观察机械臂是否开始运动\n');
fprintf('    3. 检查状态文本是否更新\n');
fprintf('    4. 验证进度条是否变化\n');
fprintf('    5. 测试暂停/停止按钮\n');
fprintf('    6. 测试夹爪控制按钮\n');

fprintf('\n  🎯 预期效果:\n');
fprintf('    - 左右臂轮流工作\n');
fprintf('    - 每个步骤包含7个子动作\n');
fprintf('    - 积木逐个变为红色\n');
fprintf('    - 动画流畅连续\n');

fprintf('\n  📊 动画流程验证:\n');
fprintf('    1. 机械臂移动到积木供应区\n');
fprintf('    2. 夹爪开启准备抓取\n');
fprintf('    3. 夹爪关闭抓取积木\n');
fprintf('    4. 机械臂移动到目标位置\n');
fprintf('    5. 夹爪开启放置积木\n');
fprintf('    6. 积木出现在目标位置\n');
fprintf('    7. 机械臂返回待机位置\n');

response = input('  ❓ 动画效果是否符合预期？(y/n): ', 's');
if strcmpi(response, 'y')
    fprintf('  ✅ 动画系统正常\n');
else
    fprintf('  ⚠️ 动画系统需要调试\n');
    fprintf('  🔧 建议检查:\n');
    fprintf('    - 定时器是否正常启动\n');
    fprintf('    - 机械臂位置更新是否正确\n');
    fprintf('    - 积木显示是否正常\n');
end

fprintf('✅ 动画系统测试完成\n');

end
