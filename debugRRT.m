% 调试RRT路径规划器
function debugRRT()
    fprintf('=== 调试RRT路径规划器 ===\n');
    
    try
        % 加载YuMi机器人
        fprintf('加载YuMi机器人...\n');
        robot = loadrobot('abbYumi', 'DataFormat', 'row', 'Gravity', [0 0 -9.81]);
        fprintf('机器人关节数: %d\n', robot.NumBodies);
        
        % 创建简化的碰撞检测器
        fprintf('创建简化碰撞检测器...\n');
        collision_checker = CollisionChecker(robot);
        collision_checker.check_self_collision = false;  % 禁用自碰撞检测
        collision_checker.check_env_collision = false;   % 禁用环境碰撞检测

        % 创建RRT规划器
        fprintf('创建RRT规划器...\n');
        rrt_planner = RRTPlanner(robot, collision_checker);
        
        % 检查关节限制
        fprintf('关节限制:\n');
        for i = 1:7
            fprintf('  关节%d: [%.2f, %.2f]\n', i, rrt_planner.joint_limits(i,1), rrt_planner.joint_limits(i,2));
        end

        % 使用机器人的home配置作为起始点
        home_config = robot.homeConfiguration;
        q_start = home_config(1:7);  % 左臂起始配置
        q_goal = [0.0, -0.2, 0.0, 0.5, 0.0, 0.2, 0.0];   % 左臂目标配置
        arm_name = 'left';

        fprintf('起始配置: [%s]\n', num2str(q_start));
        fprintf('目标配置: [%s]\n', num2str(q_goal));
        fprintf('手臂: %s\n', arm_name);
        
        % 测试配置有效性
        fprintf('测试起始配置有效性...\n');
        is_valid_start = rrt_planner.isValidConfiguration(q_start, arm_name);
        fprintf('起始配置有效: %s\n', mat2str(is_valid_start));

        fprintf('测试目标配置有效性...\n');
        is_valid_goal = rrt_planner.isValidConfiguration(q_goal, arm_name);
        fprintf('目标配置有效: %s\n', mat2str(is_valid_goal));

        % 调用planPath
        fprintf('调用planPath...\n');
        [path, success, info] = rrt_planner.planPath(q_start, q_goal, arm_name);
        
        if success
            fprintf('✅ 路径规划成功！\n');
            fprintf('路径长度: %d\n', size(path, 1));
        else
            fprintf('❌ 路径规划失败\n');
        end
        
    catch ME
        fprintf('❌ 错误: %s\n', ME.message);
        fprintf('错误位置: %s\n', ME.stack(1).name);
        fprintf('错误行号: %d\n', ME.stack(1).line);
    end
end
