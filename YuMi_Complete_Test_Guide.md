# YuMi乐高拼接系统完整测试指南

## 🎯 **目标效果**
实现与参考图片相同的YuMi双臂机器人3D可视化效果：
- 完整的YuMi机器人模型（黄色基座 + 灰色双臂）
- 左右臂轮流工作的动态拼接过程
- 严格按照mainbu.ldr设计的建筑拼接
- 流畅的机械臂运动和夹爪动作

---

## 📋 **第一步：环境准备**

### 1.1 检查必要文件
确保以下文件在当前目录：
```
✅ mainbu.ldr              - 建筑设计文件
✅ LDRParser.m             - LDR文件解析器
✅ EnhancedYumiAssembly.m  - 增强拼接系统
✅ runEnhancedYumiSystem.m - 启动脚本
```

### 1.2 检查MATLAB工具箱
```matlab
% 在MATLAB命令窗口运行：
ver  % 查看已安装工具箱

% 必需工具箱：
% - Robotics System Toolbox
% - Simulink (可选)
```

### 1.3 验证YuMi机器人模型
```matlab
% 测试机器人加载：
robot = loadrobot('abbYumi');
show(robot);
```

---

## 🚀 **第二步：启动系统**

### 2.1 基本启动命令
```matlab
% 方法1：直接运行启动脚本
runEnhancedYumiSystem

% 方法2：手动创建系统
assembly_system = EnhancedYumiAssembly();
```

### 2.2 验证启动成功
启动成功后应该看到：
```
🤖 === 启动增强YuMi乐高拼接系统 === 🤖
✅ YuMi机器人初始化成功
✅ 建筑数据加载完成: 50个积木
✅ 用户界面创建完成
✅ 增强YuMi拼接系统创建完成
```

### 2.3 界面检查
应该出现包含以下元素的窗口：
- 3D显示区域（占窗口上方70%）
- 控制面板（占窗口下方30%）
- 播放控制按钮
- 进度条和状态显示
- 夹爪控制按钮

---

## 👀 **第三步：可视化验证**

### 3.1 YuMi机器人模型检查
在3D场景中应该看到：
- **黄色基座**：YuMi机器人的底座
- **左臂**：7个关节的完整机械臂
- **右臂**：7个关节的完整机械臂
- **夹爪**：每个臂末端的夹爪装置

### 3.2 如果看不到完整机器人模型
```matlab
% 解决方案1：调整视角
view(45, 30);
axis equal;

% 解决方案2：调整坐标轴范围
xlim([-0.5, 0.8]);
ylim([-0.6, 0.6]);
zlim([-0.3, 0.8]);

% 解决方案3：强制刷新
drawnow;
```

### 3.3 积木轮廓检查
应该看到50个半透明的积木轮廓，分布在：
- X范围：-0.13 到 0.31 米
- Y范围：-0.216 到 -0.024 米
- Z范围：-0.16 到 -0.1 米

---

## 🎬 **第四步：动画测试**

### 4.1 启动拼接动画
1. 点击 **"▶️ 开始拼接"** 按钮
2. 观察状态文本变化
3. 确认动画定时器启动

### 4.2 验证动画流程
每个拼接步骤应包含：
```
1. 机械臂移动到积木供应区    ← 应该看到臂的运动
2. 夹爪开启准备抓取          ← 夹爪张开
3. 夹爪关闭抓取积木          ← 夹爪闭合
4. 机械臂移动到目标位置      ← 臂移动到放置点
5. 夹爪开启放置积木          ← 夹爪张开
6. 积木出现在目标位置        ← 积木变为红色
7. 机械臂返回待机位置        ← 臂返回初始位置
```

### 4.3 左右臂分工验证
根据mainbu.ldr数据：
- **右臂负责**：Y < -100的积木（大部分积木）
- **左臂负责**：Y ≥ -100的积木（靠近中心的积木）

---

## 🔧 **第五步：故障排除**

### 5.1 机器人模型不显示
**问题**：只看到简化的点或线
**解决方案**：
```matlab
% 检查机器人加载
robot = loadrobot('abbYumi');
if isempty(robot)
    error('YuMi机器人模型加载失败');
end

% 强制显示
figure;
show(robot, robot.homeConfiguration, 'Visuals', 'on');
```

### 5.2 动画不流畅或卡顿
**问题**：动画播放不连续
**解决方案**：
```matlab
% 调整定时器频率
timer_period = 0.2;  % 增加到0.2秒

% 减少图形更新频率
drawnow limitrate;
```

### 5.3 左右臂分工不正确
**问题**：只有一个臂在工作
**解决方案**：
```matlab
% 检查任务分配
for i = 1:length(assembly_sequence)
    step = assembly_sequence(i);
    fprintf('步骤%d: %s臂, 位置Y=%.1f\n', i, step.arm, step.place_pos(2)*1000);
end
```

### 5.4 积木位置不正确
**问题**：积木不在正确位置
**解决方案**：
```matlab
% 验证LDR解析
parser = LDRParser('mainbu.ldr');
parser.parseLDRFile();
for i = 1:length(parser.bricks)
    brick = parser.bricks(i);
    fprintf('积木%d: [%.1f, %.1f, %.1f]\n', i, brick.position);
end
```

---

## 📊 **第六步：性能优化**

### 6.1 提高显示质量
```matlab
% 设置高质量渲染
set(gcf, 'Renderer', 'opengl');
set(gca, 'Projection', 'perspective');
```

### 6.2 优化动画速度
```matlab
% 调整动画参数
animation_speed = 1.0;  % 1.0 = 正常速度
timer_period = 0.1 / animation_speed;
```

### 6.3 内存优化
```matlab
% 定期清理图形对象
if mod(current_step, 10) == 0
    drawnow;
    pause(0.01);
end
```

---

## ✅ **第七步：验收标准**

### 7.1 视觉效果检查清单
- [ ] 看到完整的YuMi双臂机器人（黄色基座+灰色双臂）
- [ ] 左右臂结构清晰可见
- [ ] 50个积木轮廓正确显示
- [ ] 3D场景渲染正常

### 7.2 动画效果检查清单
- [ ] 左右臂轮流工作
- [ ] 机械臂运动轨迹流畅
- [ ] 夹爪开合动作可见
- [ ] 积木逐个放置到正确位置

### 7.3 功能检查清单
- [ ] 播放/暂停/停止按钮正常
- [ ] 进度条可以拖动
- [ ] 夹爪控制按钮响应
- [ ] 状态信息实时更新

### 7.4 最终建筑检查
- [ ] 50个积木全部放置
- [ ] 建筑结构与mainbu.ldr一致
- [ ] 颜色分布正确（41个Color_28 + 9个Dark_Gray）

---

## 🆘 **紧急故障排除**

如果系统完全无法启动：
```matlab
% 最小化测试
try
    robot = loadrobot('abbYumi');
    figure;
    show(robot);
    fprintf('✅ 基本功能正常\n');
catch ME
    fprintf('❌ 基本功能失败: %s\n', ME.message);
end
```

如果需要重新安装：
```matlab
% 清理环境
clear all;
close all;
clc;

% 重新启动
runEnhancedYumiSystem;
```
