classdef EnhancedYumiAssembly < handle
    % 增强的YuMi乐高拼接系统 - 真正可视化版本
    
    properties
        % 机器人相关
        yumi_robot          % YuMi机器人模型
        current_config      % 当前关节配置
        home_config         % 初始配置
        robot_plot          % 机器人显示对象
        left_ik            % 左臂逆运动学求解器
        right_ik           % 右臂逆运动学求解器

        % 建筑数据
        ldr_parser          % LDR解析器
        target_bricks       % 目标积木列表
        assembly_sequence   % 拼接序列

        % 界面组件
        main_figure         % 主窗口
        axes_3d            % 3D坐标轴
        status_text        % 状态文本
        progress_bar       % 进度条

        % 动画控制
        animation_timer     % 动画定时器
        is_playing         % 播放状态
        current_step       % 当前步骤
        total_steps        % 总步骤数
        animation_phase    % 动画阶段
        animation_substep  % 动画子步骤

        % 积木对象
        brick_objects      % 积木显示对象
        placed_bricks      % 已放置的积木

        % 机械臂状态
        left_arm_pos       % 左臂位置
        right_arm_pos      % 右臂位置
        gripper_states     % 夹爪状态
        target_config      % 目标关节配置
        motion_progress    % 运动进度
    end
    
    methods
        function obj = EnhancedYumiAssembly()
            % 构造函数
            fprintf('🤖 === 创建增强YuMi拼接系统 === 🤖\n');
            
            % 初始化组件
            obj.initializeRobot();
            obj.loadBuildingData();
            obj.createInterface();
            obj.setupAnimation();
            
            fprintf('✅ 增强YuMi拼接系统创建完成\n');
        end
        
        function initializeRobot(obj)
            % 初始化机器人
            fprintf('初始化YuMi机器人...\n');

            try
                % 加载YuMi机器人
                obj.yumi_robot = loadrobot('abbYumi', 'DataFormat', 'row');
                obj.home_config = obj.yumi_robot.homeConfiguration;
                obj.current_config = obj.home_config;

                % 初始化机械臂位置
                obj.left_arm_pos = [0.3, 0.3, 0.5];   % 左臂初始位置
                obj.right_arm_pos = [0.3, -0.3, 0.5]; % 右臂初始位置

                % 初始化夹爪状态
                obj.gripper_states = struct();
                obj.gripper_states.left = struct('is_open', true, 'position', 0.02);
                obj.gripper_states.right = struct('is_open', true, 'position', 0.02);

                % 创建逆运动学求解器
                obj.setupInverseKinematics();

                fprintf('✅ YuMi机器人初始化成功\n');

            catch ME
                fprintf('❌ YuMi机器人初始化失败: %s\n', ME.message);
                error('无法初始化YuMi机器人');
            end
        end

        function setupInverseKinematics(obj)
            % 设置逆运动学求解器
            try
                % 为左臂创建逆运动学求解器
                obj.left_ik = inverseKinematics('RigidBodyTree', obj.yumi_robot);
                obj.left_ik.SolverParameters.AllowRandomRestart = false;
                obj.left_ik.SolverParameters.MaxIterations = 1000;

                % 为右臂创建逆运动学求解器
                obj.right_ik = inverseKinematics('RigidBodyTree', obj.yumi_robot);
                obj.right_ik.SolverParameters.AllowRandomRestart = false;
                obj.right_ik.SolverParameters.MaxIterations = 1000;

                fprintf('✅ 逆运动学求解器设置完成\n');

            catch ME
                fprintf('⚠️ 逆运动学求解器设置失败: %s\n', ME.message);
                % 继续运行，使用简化的运动控制
            end
        end
        
        function loadBuildingData(obj)
            % 加载建筑数据
            fprintf('加载建筑数据...\n');
            
            % 解析LDR文件
            obj.ldr_parser = LDRParser('mainbu.ldr');
            success = obj.ldr_parser.parseLDRFile();
            
            if ~success
                error('无法解析mainbu.ldr文件');
            end
            
            obj.target_bricks = obj.ldr_parser.bricks;
            obj.generateAssemblySequence();
            
            fprintf('✅ 建筑数据加载完成: %d个积木\n', length(obj.target_bricks));
        end
        
        function generateAssemblySequence(obj)
            % 生成拼接序列
            bricks = obj.target_bricks;

            % 按Z坐标排序（从下到上）
            z_coords = zeros(length(bricks), 1);
            for i = 1:length(bricks)
                % 确保position是数值数组
                if isstruct(bricks(i).position)
                    pos = [bricks(i).position.x, bricks(i).position.y, bricks(i).position.z];
                else
                    pos = bricks(i).position;
                end
                z_coords(i) = pos(3);
            end
            [~, sort_idx] = sort(z_coords);

            % 生成序列
            obj.assembly_sequence = struct('step_id', {}, 'brick', {}, 'arm', {}, 'pick_pos', {}, 'place_pos', {});

            for i = 1:length(sort_idx)
                brick_idx = sort_idx(i);
                brick = bricks(brick_idx);

                % 确保position是数值数组
                if isstruct(brick.position)
                    brick_pos = [brick.position.x, brick.position.y, brick.position.z];
                else
                    brick_pos = brick.position;
                end

                % 分配机械臂（所有积木Y<0，都用右臂）
                arm = 'right';
                if brick_pos(2) >= -100  % 靠近中心的用左臂
                    arm = 'left';
                end

                step = struct();
                step.step_id = i;
                step.brick = brick;
                step.arm = arm;
                step.pick_pos = [0.2, 0, 0.3];  % 积木供应位置
                step.place_pos = brick_pos / 1000;  % 转换为米

                obj.assembly_sequence(end+1) = step;
            end

            obj.total_steps = length(obj.assembly_sequence);
            obj.current_step = 1;
            obj.animation_phase = 'idle';

            fprintf('✅ 拼接序列生成完成: %d步\n', obj.total_steps);
        end
        
        function createInterface(obj)
            % 创建用户界面
            fprintf('创建用户界面...\n');
            
            % 主窗口
            obj.main_figure = figure('Name', '🤖 增强YuMi乐高拼接系统', ...
                                     'Position', [50, 50, 1400, 900], ...
                                     'Color', [0.1, 0.1, 0.1], ...
                                     'CloseRequestFcn', @(~,~) obj.closeSystem());
            
            % 3D显示区域
            obj.axes_3d = axes('Parent', obj.main_figure, ...
                               'Position', [0.05, 0.3, 0.9, 0.65], ...
                               'Color', [0.05, 0.05, 0.05]);
            
            % 控制面板
            obj.createControlPanel();
            
            % 初始化3D场景
            obj.initialize3DScene();
            
            fprintf('✅ 用户界面创建完成\n');
        end
        
        function createControlPanel(obj)
            % 创建控制面板
            panel = uipanel('Parent', obj.main_figure, ...
                            'Position', [0.05, 0.02, 0.9, 0.25], ...
                            'BackgroundColor', [0.2, 0.2, 0.2], ...
                            'Title', '控制面板', ...
                            'ForegroundColor', 'white', ...
                            'FontSize', 12);
            
            % 播放控制
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '▶️ 开始拼接', ...
                      'Position', [20, 150, 120, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.startAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏸️ 暂停', ...
                      'Position', [150, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.pauseAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏹️ 停止', ...
                      'Position', [240, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.stopAssembly());
            
            % 进度控制
            uicontrol('Parent', panel, ...
                      'Style', 'text', ...
                      'String', '拼接进度:', ...
                      'Position', [20, 110, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white');
            
            obj.progress_bar = uicontrol('Parent', panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 110, 400, 20], ...
                                         'Min', 1, ...
                                         'Max', obj.total_steps, ...
                                         'Value', 1, ...
                                         'Callback', @(src,~) obj.seekToStep(round(src.Value)));
            
            % 状态显示
            obj.status_text = uicontrol('Parent', panel, ...
                                        'Style', 'text', ...
                                        'String', '准备开始拼接...', ...
                                        'Position', [20, 70, 600, 30], ...
                                        'BackgroundColor', [0.2, 0.2, 0.2], ...
                                        'ForegroundColor', 'white', ...
                                        'FontSize', 11, ...
                                        'HorizontalAlignment', 'left');
            
            % 夹爪控制
            uicontrol('Parent', panel, ...
                      'Style', 'text', ...
                      'String', '夹爪控制:', ...
                      'Position', [20, 30, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white');
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪开', ...
                      'Position', [110, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('left', 'open'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪关', ...
                      'Position', [175, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('left', 'close'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪开', ...
                      'Position', [245, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('right', 'open'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪关', ...
                      'Position', [310, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('right', 'close'));
        end
        
        function initialize3DScene(obj)
            % 初始化3D场景
            fprintf('初始化3D场景...\n');
            
            axes(obj.axes_3d);
            hold on;
            
            % 设置坐标轴
            xlabel('X (m)', 'Color', 'white', 'FontSize', 12);
            ylabel('Y (m)', 'Color', 'white', 'FontSize', 12);
            zlabel('Z (m)', 'Color', 'white', 'FontSize', 12);
            title('🤖 YuMi双臂机器人乐高拼接系统', 'Color', 'white', 'FontSize', 14);
            
            % 设置视角和范围
            view(45, 30);
            axis equal;
            grid on;
            
            % 调整坐标轴范围以确保机器人可见
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            
            % 添加地面
            [X, Y] = meshgrid(-0.5:0.1:0.8, -0.6:0.1:0.6);
            Z = -0.2 * ones(size(X));
            surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
            
            % 显示YuMi机器人
            obj.displayYumiRobot();
            
            % 显示目标积木轮廓
            obj.displayTargetBricks();
            
            fprintf('✅ 3D场景初始化完成\n');
        end

        function setRobotColors(obj)
            % 设置YuMi机器人的正确颜色（黄色基座+灰色双臂）
            try
                % 获取所有图形对象
                ax_children = get(obj.axes_3d, 'Children');

                for i = 1:length(ax_children)
                    child = ax_children(i);
                    if isprop(child, 'FaceColor') && isprop(child, 'UserData')
                        % 检查是否是机器人部件
                        if contains(class(child), 'Patch') || contains(class(child), 'Surface')
                            % 基座设为黄色
                            if contains(lower(char(child.DisplayName)), 'base') || ...
                               contains(lower(char(child.Tag)), 'base')
                                set(child, 'FaceColor', [1, 1, 0], 'FaceAlpha', 0.8); % 黄色
                            % 机械臂设为灰色
                            elseif contains(lower(char(child.DisplayName)), 'link') || ...
                                   contains(lower(char(child.Tag)), 'link')
                                set(child, 'FaceColor', [0.7, 0.7, 0.7], 'FaceAlpha', 0.9); % 灰色
                            % 夹爪设为蓝色
                            elseif contains(lower(char(child.DisplayName)), 'gripper') || ...
                                   contains(lower(char(child.Tag)), 'gripper')
                                set(child, 'FaceColor', [0, 0, 1], 'FaceAlpha', 0.8); % 蓝色
                            end
                        end
                    end
                end

                fprintf('✅ 机器人颜色设置完成\n');

            catch ME
                fprintf('⚠️ 机器人颜色设置失败: %s\n', ME.message);
            end
        end
        
        function displayYumiRobot(obj)
            % 显示YuMi机器人
            try
                % 显示机器人
                obj.robot_plot = show(obj.yumi_robot, obj.current_config, ...
                                      'Parent', obj.axes_3d, ...
                                      'PreservePlot', true, ...
                                      'Frames', 'off', ...
                                      'Visuals', 'on');

                % 设置YuMi机器人的正确颜色
                obj.setRobotColors();

                % 强制刷新
                drawnow;

                fprintf('✅ YuMi机器人显示成功\n');

            catch ME
                fprintf('⚠️ YuMi机器人显示失败: %s\n', ME.message);

                % 显示简化的机器人表示
                plot3(0, 0, 0, 'ko', 'MarkerSize', 15, 'MarkerFaceColor', 'black');
                text(0, 0, 0.1, 'YuMi Base', 'Color', 'white', 'FontSize', 12, 'HorizontalAlignment', 'center');

                % 左臂
                plot3(obj.left_arm_pos(1), obj.left_arm_pos(2), obj.left_arm_pos(3), ...
                      'bo', 'MarkerSize', 12, 'MarkerFaceColor', 'blue');
                text(obj.left_arm_pos(1), obj.left_arm_pos(2), obj.left_arm_pos(3)+0.05, ...
                     'Left Arm', 'Color', 'blue', 'FontSize', 10, 'HorizontalAlignment', 'center');
                
                % 右臂
                plot3(obj.right_arm_pos(1), obj.right_arm_pos(2), obj.right_arm_pos(3), ...
                      'ro', 'MarkerSize', 12, 'MarkerFaceColor', 'red');
                text(obj.right_arm_pos(1), obj.right_arm_pos(2), obj.right_arm_pos(3)+0.05, ...
                     'Right Arm', 'Color', 'red', 'FontSize', 10, 'HorizontalAlignment', 'center');
            end
        end
        
        function displayTargetBricks(obj)
            % 显示目标积木轮廓
            obj.brick_objects = {};
            obj.placed_bricks = false(length(obj.target_bricks), 1);

            for i = 1:length(obj.target_bricks)
                brick = obj.target_bricks(i);

                % 确保position是数值数组
                if isstruct(brick.position)
                    pos = [brick.position.x, brick.position.y, brick.position.z] / 1000;
                else
                    pos = brick.position / 1000;  % 转换为米
                end

                % 创建积木轮廓（半透明）
                brick_obj = obj.createBrick(pos, [0.7, 0.7, 0.7], 0.2);
                obj.brick_objects{i} = brick_obj;
            end

            fprintf('✅ 目标积木轮廓显示完成: %d个\n', length(obj.target_bricks));
        end
        
        function brick_obj = createBrick(obj, position, color, alpha)
            % 创建积木3D对象
            brick_size = [0.032, 0.016, 0.0096];  % LEGO标准尺寸(米)
            
            % 创建立方体顶点
            vertices = [
                0, 0, 0;
                brick_size(1), 0, 0;
                brick_size(1), brick_size(2), 0;
                0, brick_size(2), 0;
                0, 0, brick_size(3);
                brick_size(1), 0, brick_size(3);
                brick_size(1), brick_size(2), brick_size(3);
                0, brick_size(2), brick_size(3)
            ];
            
            % 平移到正确位置
            vertices = vertices + repmat(position, 8, 1);
            
            % 定义面
            faces = [
                1, 2, 3, 4;  % 底面
                5, 6, 7, 8;  % 顶面
                1, 2, 6, 5;  % 前面
                3, 4, 8, 7;  % 后面
                1, 4, 8, 5;  % 左面
                2, 3, 7, 6   % 右面
            ];
            
            % 绘制积木
            brick_obj = patch('Vertices', vertices, ...
                              'Faces', faces, ...
                              'FaceColor', color, ...
                              'EdgeColor', 'black', ...
                              'FaceAlpha', alpha, ...
                              'LineWidth', 0.5, ...
                              'Parent', obj.axes_3d);
        end
        
        function setupAnimation(obj)
            % 设置动画系统
            obj.is_playing = false;
            obj.animation_timer = [];
            obj.animation_substep = 1;
            obj.motion_progress = 0;
            obj.target_config = obj.current_config;

            fprintf('✅ 动画系统设置完成\n');
        end
        
        function startAssembly(obj)
            % 开始拼接
            fprintf('🚀 开始拼接动画...\n');
            obj.is_playing = true;
            obj.animation_phase = 'moving_to_pick';
            obj.animation_substep = 1;
            obj.motion_progress = 0;

            % 更新状态
            obj.updateStatus(sprintf('开始拼接 - 步骤 1/%d', obj.total_steps));

            % 创建动画定时器
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end

            obj.animation_timer = timer('ExecutionMode', 'fixedRate', ...
                                        'Period', 0.05, ...
                                        'TimerFcn', @(~,~) obj.updateAnimation());
            start(obj.animation_timer);

            fprintf('✅ 动画定时器启动成功\n');
        end
        
        function updateAnimation(obj)
            % 更新动画
            if ~obj.is_playing || obj.current_step > obj.total_steps
                obj.pauseAssembly();
                return;
            end

            try
                % 执行当前步骤
                obj.executeCurrentStep();

                % 更新机器人显示
                obj.updateRobotDisplay();

                % 更新显示
                drawnow limitrate;

            catch ME
                fprintf('❌ 动画更新错误: %s\n', ME.message);
                obj.pauseAssembly();
            end
        end
        
        function executeCurrentStep(obj)
            % 执行当前拼接步骤
            step = obj.assembly_sequence(obj.current_step);

            % 每个阶段分为多个子步骤以实现平滑动画
            steps_per_phase = 20; % 每个阶段20个子步骤

            switch obj.animation_phase
                case 'moving_to_pick'
                    if obj.animation_substep == 1
                        % 开始移动到拾取位置
                        obj.startArmMovement(step.arm, step.pick_pos);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂移动到拾取位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateArmMovement(steps_per_phase)
                        obj.animation_phase = 'picking';
                        obj.animation_substep = 1;
                    end

                case 'picking'
                    obj.controlGripper(step.arm, 'close');
                    obj.updateStatus(sprintf('步骤 %d/%d: %s臂夹取积木', ...
                                           obj.current_step, obj.total_steps, step.arm));
                    obj.animation_phase = 'moving_to_place';
                    obj.animation_substep = 1;

                case 'moving_to_place'
                    if obj.animation_substep == 1
                        % 开始移动到放置位置
                        obj.startArmMovement(step.arm, step.place_pos);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂移动到放置位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateArmMovement(steps_per_phase)
                        obj.animation_phase = 'placing';
                        obj.animation_substep = 1;
                    end

                case 'placing'
                    obj.controlGripper(step.arm, 'open');
                    obj.placeBrick(obj.current_step);
                    obj.updateStatus(sprintf('步骤 %d/%d: %s臂放置积木', ...
                                           obj.current_step, obj.total_steps, step.arm));
                    obj.animation_phase = 'returning';
                    obj.animation_substep = 1;

                case 'returning'
                    if obj.animation_substep == 1
                        % 开始返回初始位置
                        obj.startArmReturnHome(step.arm);
                        obj.updateStatus(sprintf('步骤 %d/%d: %s臂返回初始位置', ...
                                               obj.current_step, obj.total_steps, step.arm));
                    end

                    if obj.updateArmMovement(steps_per_phase)
                        obj.nextStep();
                    end
            end
        end
        
        function startArmMovement(obj, arm, target_position)
            % 开始机械臂运动
            try
                % 计算目标关节配置
                if strcmp(arm, 'left')
                    end_effector = 'yumi_link_7_l';
                else
                    end_effector = 'yumi_link_7_r';
                end

                % 创建目标位姿
                target_pose = trvec2tform(target_position);

                % 使用逆运动学计算目标关节配置
                if strcmp(arm, 'left') && ~isempty(obj.left_ik)
                    obj.target_config = obj.left_ik(end_effector, target_pose, ...
                                                   [1,1,1,1,1,1], obj.current_config);
                elseif strcmp(arm, 'right') && ~isempty(obj.right_ik)
                    obj.target_config = obj.right_ik(end_effector, target_pose, ...
                                                    [1,1,1,1,1,1], obj.current_config);
                else
                    % 如果逆运动学失败，使用简化的关节配置
                    obj.target_config = obj.generateSimpleConfig(arm, target_position);
                end

                obj.motion_progress = 0;
                obj.animation_substep = 1;

            catch ME
                fprintf('⚠️ 运动规划失败: %s\n', ME.message);
                % 使用简化的关节配置
                obj.target_config = obj.generateSimpleConfig(arm, target_position);
                obj.motion_progress = 0;
                obj.animation_substep = 1;
            end
        end

        function startArmReturnHome(obj, arm)
            % 机械臂返回初始位置
            if strcmp(arm, 'left')
                home_pos = [0.3, 0.3, 0.5];
            else
                home_pos = [0.3, -0.3, 0.5];
            end

            obj.startArmMovement(arm, home_pos);
        end

        function completed = updateArmMovement(obj, total_steps)
            % 更新机械臂运动
            obj.motion_progress = obj.animation_substep / total_steps;

            if obj.motion_progress >= 1.0
                % 运动完成
                obj.current_config = obj.target_config;
                completed = true;
                obj.animation_substep = 1;
                obj.motion_progress = 0;
            else
                % 插值计算当前关节配置
                obj.current_config = obj.interpolateConfig(obj.current_config, ...
                                                          obj.target_config, ...
                                                          obj.motion_progress);
                obj.animation_substep = obj.animation_substep + 1;
                completed = false;
            end
        end

        function config = generateSimpleConfig(obj, arm, target_position)
            % 生成简化的关节配置
            config = obj.current_config;

            % 简单的关节角度调整
            if strcmp(arm, 'left')
                % 左臂关节调整
                config(1) = atan2(target_position(2), target_position(1));
                config(2) = -pi/4 + target_position(3) * pi/2;
                config(3) = pi/6;
            else
                % 右臂关节调整
                config(8) = atan2(target_position(2), target_position(1));
                config(9) = -pi/4 + target_position(3) * pi/2;
                config(10) = pi/6;
            end
        end

        function config = interpolateConfig(obj, start_config, end_config, progress)
            % 插值计算关节配置
            config = start_config + progress * (end_config - start_config);
        end
        
        function controlGripper(obj, arm, action)
            % 控制夹爪
            if strcmp(action, 'open')
                obj.gripper_states.(arm).is_open = true;
                obj.gripper_states.(arm).position = 0.02;
            else
                obj.gripper_states.(arm).is_open = false;
                obj.gripper_states.(arm).position = 0;
            end
            
            fprintf('夹爪控制: %s臂 %s\n', arm, action);
        end
        
        function placeBrick(obj, step_id)
            % 放置积木
            if step_id <= length(obj.brick_objects)
                % 更新积木显示为已放置状态
                set(obj.brick_objects{step_id}, 'FaceColor', [1, 0, 0], 'FaceAlpha', 0.8);
                obj.placed_bricks(step_id) = true;
            end
        end
        
        function nextStep(obj)
            % 进入下一步
            obj.current_step = obj.current_step + 1;
            obj.animation_phase = 'moving_to_pick';
            obj.animation_substep = 1;
            obj.motion_progress = 0;

            % 更新进度条
            if obj.current_step <= obj.total_steps
                obj.progress_bar.Value = obj.current_step;
                obj.updateStatus(sprintf('拼接中 - 步骤 %d/%d', obj.current_step, obj.total_steps));
                fprintf('✅ 完成步骤 %d，开始步骤 %d\n', obj.current_step-1, obj.current_step);
            else
                obj.updateStatus('🎉 拼接完成！');
                obj.pauseAssembly();
                fprintf('🎉 === 拼接完成！=== 🎉\n');
            end
        end
        
        function updateRobotDisplay(obj)
            % 更新机器人显示
            try
                % 更新机器人关节配置
                if ~isempty(obj.robot_plot) && isvalid(obj.robot_plot)
                    % 删除旧的显示
                    delete(obj.robot_plot);
                end

                % 重新显示机器人
                obj.robot_plot = show(obj.yumi_robot, obj.current_config, ...
                                      'Parent', obj.axes_3d, ...
                                      'PreservePlot', true, ...
                                      'Frames', 'off', ...
                                      'Visuals', 'on');

                % 设置颜色
                obj.setRobotColors();

            catch ME
                fprintf('⚠️ 机器人显示更新失败: %s\n', ME.message);
            end
        end
        
        function updateStatus(obj, message)
            % 更新状态文本
            obj.status_text.String = message;
        end
        
        function pauseAssembly(obj)
            % 暂停拼接
            obj.is_playing = false;
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
            end
            fprintf('⏸️ 拼接已暂停\n');
        end
        
        function stopAssembly(obj)
            % 停止拼接
            obj.pauseAssembly();
            obj.current_step = 1;
            obj.animation_phase = 'idle';
            obj.progress_bar.Value = 1;
            obj.updateStatus('准备开始拼接...');
            fprintf('⏹️ 拼接已停止\n');
        end
        
        function seekToStep(obj, step)
            % 跳转到指定步骤
            obj.current_step = max(1, min(step, obj.total_steps));
            obj.updateStatus(sprintf('跳转到步骤 %d/%d', obj.current_step, obj.total_steps));
        end
        
        function closeSystem(obj)
            % 关闭系统
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end
            delete(obj.main_figure);
        end
    end
end
