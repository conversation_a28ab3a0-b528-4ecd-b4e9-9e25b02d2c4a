classdef EnhancedYumiAssembly < handle
    % 增强的YuMi乐高拼接系统 - 真正可视化版本
    
    properties
        % 机器人相关
        yumi_robot          % YuMi机器人模型
        current_config      % 当前关节配置
        home_config         % 初始配置
        robot_plot          % 机器人显示对象
        
        % 建筑数据
        ldr_parser          % LDR解析器
        target_bricks       % 目标积木列表
        assembly_sequence   % 拼接序列
        
        % 界面组件
        main_figure         % 主窗口
        axes_3d            % 3D坐标轴
        status_text        % 状态文本
        progress_bar       % 进度条
        
        % 动画控制
        animation_timer     % 动画定时器
        is_playing         % 播放状态
        current_step       % 当前步骤
        total_steps        % 总步骤数
        animation_phase    % 动画阶段
        
        % 积木对象
        brick_objects      % 积木显示对象
        placed_bricks      % 已放置的积木
        
        % 机械臂状态
        left_arm_pos       % 左臂位置
        right_arm_pos      % 右臂位置
        gripper_states     % 夹爪状态
    end
    
    methods
        function obj = EnhancedYumiAssembly()
            % 构造函数
            fprintf('🤖 === 创建增强YuMi拼接系统 === 🤖\n');
            
            % 初始化组件
            obj.initializeRobot();
            obj.loadBuildingData();
            obj.createInterface();
            obj.setupAnimation();
            
            fprintf('✅ 增强YuMi拼接系统创建完成\n');
        end
        
        function initializeRobot(obj)
            % 初始化机器人
            fprintf('初始化YuMi机器人...\n');
            
            try
                % 加载YuMi机器人
                obj.yumi_robot = loadrobot('abbYumi', 'DataFormat', 'row');
                obj.home_config = obj.yumi_robot.homeConfiguration;
                obj.current_config = obj.home_config;
                
                % 初始化机械臂位置
                obj.left_arm_pos = [0.3, 0.3, 0.5];   % 左臂初始位置
                obj.right_arm_pos = [0.3, -0.3, 0.5]; % 右臂初始位置
                
                % 初始化夹爪状态
                obj.gripper_states = struct();
                obj.gripper_states.left = struct('is_open', true, 'position', 0.02);
                obj.gripper_states.right = struct('is_open', true, 'position', 0.02);
                
                fprintf('✅ YuMi机器人初始化成功\n');
                
            catch ME
                fprintf('❌ YuMi机器人初始化失败: %s\n', ME.message);
                error('无法初始化YuMi机器人');
            end
        end
        
        function loadBuildingData(obj)
            % 加载建筑数据
            fprintf('加载建筑数据...\n');
            
            % 解析LDR文件
            obj.ldr_parser = LDRParser('mainbu.ldr');
            success = obj.ldr_parser.parseLDRFile();
            
            if ~success
                error('无法解析mainbu.ldr文件');
            end
            
            obj.target_bricks = obj.ldr_parser.bricks;
            obj.generateAssemblySequence();
            
            fprintf('✅ 建筑数据加载完成: %d个积木\n', length(obj.target_bricks));
        end
        
        function generateAssemblySequence(obj)
            % 生成拼接序列
            bricks = obj.target_bricks;

            % 按Z坐标排序（从下到上）
            z_coords = zeros(length(bricks), 1);
            for i = 1:length(bricks)
                % 确保position是数值数组
                if isstruct(bricks(i).position)
                    pos = [bricks(i).position.x, bricks(i).position.y, bricks(i).position.z];
                else
                    pos = bricks(i).position;
                end
                z_coords(i) = pos(3);
            end
            [~, sort_idx] = sort(z_coords);

            % 生成序列
            obj.assembly_sequence = struct('step_id', {}, 'brick', {}, 'arm', {}, 'pick_pos', {}, 'place_pos', {});

            for i = 1:length(sort_idx)
                brick_idx = sort_idx(i);
                brick = bricks(brick_idx);

                % 确保position是数值数组
                if isstruct(brick.position)
                    brick_pos = [brick.position.x, brick.position.y, brick.position.z];
                else
                    brick_pos = brick.position;
                end

                % 分配机械臂（所有积木Y<0，都用右臂）
                arm = 'right';
                if brick_pos(2) >= -100  % 靠近中心的用左臂
                    arm = 'left';
                end

                step = struct();
                step.step_id = i;
                step.brick = brick;
                step.arm = arm;
                step.pick_pos = [0.2, 0, 0.3];  % 积木供应位置
                step.place_pos = brick_pos / 1000;  % 转换为米

                obj.assembly_sequence(end+1) = step;
            end

            obj.total_steps = length(obj.assembly_sequence);
            obj.current_step = 1;
            obj.animation_phase = 'idle';

            fprintf('✅ 拼接序列生成完成: %d步\n', obj.total_steps);
        end
        
        function createInterface(obj)
            % 创建用户界面
            fprintf('创建用户界面...\n');
            
            % 主窗口
            obj.main_figure = figure('Name', '🤖 增强YuMi乐高拼接系统', ...
                                     'Position', [50, 50, 1400, 900], ...
                                     'Color', [0.1, 0.1, 0.1], ...
                                     'CloseRequestFcn', @(~,~) obj.closeSystem());
            
            % 3D显示区域
            obj.axes_3d = axes('Parent', obj.main_figure, ...
                               'Position', [0.05, 0.3, 0.9, 0.65], ...
                               'Color', [0.05, 0.05, 0.05]);
            
            % 控制面板
            obj.createControlPanel();
            
            % 初始化3D场景
            obj.initialize3DScene();
            
            fprintf('✅ 用户界面创建完成\n');
        end
        
        function createControlPanel(obj)
            % 创建控制面板
            panel = uipanel('Parent', obj.main_figure, ...
                            'Position', [0.05, 0.02, 0.9, 0.25], ...
                            'BackgroundColor', [0.2, 0.2, 0.2], ...
                            'Title', '控制面板', ...
                            'ForegroundColor', 'white', ...
                            'FontSize', 12);
            
            % 播放控制
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '▶️ 开始拼接', ...
                      'Position', [20, 150, 120, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.startAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏸️ 暂停', ...
                      'Position', [150, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.pauseAssembly());
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '⏹️ 停止', ...
                      'Position', [240, 150, 80, 40], ...
                      'FontSize', 12, ...
                      'Callback', @(~,~) obj.stopAssembly());
            
            % 进度控制
            uicontrol('Parent', panel, ...
                      'Style', 'text', ...
                      'String', '拼接进度:', ...
                      'Position', [20, 110, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white');
            
            obj.progress_bar = uicontrol('Parent', panel, ...
                                         'Style', 'slider', ...
                                         'Position', [110, 110, 400, 20], ...
                                         'Min', 1, ...
                                         'Max', obj.total_steps, ...
                                         'Value', 1, ...
                                         'Callback', @(src,~) obj.seekToStep(round(src.Value)));
            
            % 状态显示
            obj.status_text = uicontrol('Parent', panel, ...
                                        'Style', 'text', ...
                                        'String', '准备开始拼接...', ...
                                        'Position', [20, 70, 600, 30], ...
                                        'BackgroundColor', [0.2, 0.2, 0.2], ...
                                        'ForegroundColor', 'white', ...
                                        'FontSize', 11, ...
                                        'HorizontalAlignment', 'left');
            
            % 夹爪控制
            uicontrol('Parent', panel, ...
                      'Style', 'text', ...
                      'String', '夹爪控制:', ...
                      'Position', [20, 30, 80, 20], ...
                      'BackgroundColor', [0.2, 0.2, 0.2], ...
                      'ForegroundColor', 'white');
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪开', ...
                      'Position', [110, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('left', 'open'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '左爪关', ...
                      'Position', [175, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('left', 'close'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪开', ...
                      'Position', [245, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('right', 'open'));
            
            uicontrol('Parent', panel, ...
                      'Style', 'pushbutton', ...
                      'String', '右爪关', ...
                      'Position', [310, 30, 60, 25], ...
                      'Callback', @(~,~) obj.controlGripper('right', 'close'));
        end
        
        function initialize3DScene(obj)
            % 初始化3D场景
            fprintf('初始化3D场景...\n');
            
            axes(obj.axes_3d);
            hold on;
            
            % 设置坐标轴
            xlabel('X (m)', 'Color', 'white', 'FontSize', 12);
            ylabel('Y (m)', 'Color', 'white', 'FontSize', 12);
            zlabel('Z (m)', 'Color', 'white', 'FontSize', 12);
            title('🤖 YuMi双臂机器人乐高拼接系统', 'Color', 'white', 'FontSize', 14);
            
            % 设置视角和范围
            view(45, 30);
            axis equal;
            grid on;
            
            % 调整坐标轴范围以确保机器人可见
            xlim([-0.5, 0.8]);
            ylim([-0.6, 0.6]);
            zlim([-0.3, 0.8]);
            
            % 添加地面
            [X, Y] = meshgrid(-0.5:0.1:0.8, -0.6:0.1:0.6);
            Z = -0.2 * ones(size(X));
            surf(X, Y, Z, 'FaceColor', [0.3, 0.3, 0.3], 'FaceAlpha', 0.3, 'EdgeColor', 'none');
            
            % 显示YuMi机器人
            obj.displayYumiRobot();
            
            % 显示目标积木轮廓
            obj.displayTargetBricks();
            
            fprintf('✅ 3D场景初始化完成\n');
        end
        
        function displayYumiRobot(obj)
            % 显示YuMi机器人
            try
                % 显示机器人
                obj.robot_plot = show(obj.yumi_robot, obj.current_config, ...
                                      'Parent', obj.axes_3d, ...
                                      'PreservePlot', true, ...
                                      'Frames', 'off', ...
                                      'Visuals', 'on');
                
                % 强制刷新
                drawnow;
                
                fprintf('✅ YuMi机器人显示成功\n');
                
            catch ME
                fprintf('⚠️ YuMi机器人显示失败: %s\n', ME.message);
                
                % 显示简化的机器人表示
                plot3(0, 0, 0, 'ko', 'MarkerSize', 15, 'MarkerFaceColor', 'black');
                text(0, 0, 0.1, 'YuMi Base', 'Color', 'white', 'FontSize', 12, 'HorizontalAlignment', 'center');
                
                % 左臂
                plot3(obj.left_arm_pos(1), obj.left_arm_pos(2), obj.left_arm_pos(3), ...
                      'bo', 'MarkerSize', 12, 'MarkerFaceColor', 'blue');
                text(obj.left_arm_pos(1), obj.left_arm_pos(2), obj.left_arm_pos(3)+0.05, ...
                     'Left Arm', 'Color', 'blue', 'FontSize', 10, 'HorizontalAlignment', 'center');
                
                % 右臂
                plot3(obj.right_arm_pos(1), obj.right_arm_pos(2), obj.right_arm_pos(3), ...
                      'ro', 'MarkerSize', 12, 'MarkerFaceColor', 'red');
                text(obj.right_arm_pos(1), obj.right_arm_pos(2), obj.right_arm_pos(3)+0.05, ...
                     'Right Arm', 'Color', 'red', 'FontSize', 10, 'HorizontalAlignment', 'center');
            end
        end
        
        function displayTargetBricks(obj)
            % 显示目标积木轮廓
            obj.brick_objects = {};
            obj.placed_bricks = false(length(obj.target_bricks), 1);

            for i = 1:length(obj.target_bricks)
                brick = obj.target_bricks(i);

                % 确保position是数值数组
                if isstruct(brick.position)
                    pos = [brick.position.x, brick.position.y, brick.position.z] / 1000;
                else
                    pos = brick.position / 1000;  % 转换为米
                end

                % 创建积木轮廓（半透明）
                brick_obj = obj.createBrick(pos, [0.7, 0.7, 0.7], 0.2);
                obj.brick_objects{i} = brick_obj;
            end

            fprintf('✅ 目标积木轮廓显示完成: %d个\n', length(obj.target_bricks));
        end
        
        function brick_obj = createBrick(obj, position, color, alpha)
            % 创建积木3D对象
            brick_size = [0.032, 0.016, 0.0096];  % LEGO标准尺寸(米)
            
            % 创建立方体顶点
            vertices = [
                0, 0, 0;
                brick_size(1), 0, 0;
                brick_size(1), brick_size(2), 0;
                0, brick_size(2), 0;
                0, 0, brick_size(3);
                brick_size(1), 0, brick_size(3);
                brick_size(1), brick_size(2), brick_size(3);
                0, brick_size(2), brick_size(3)
            ];
            
            % 平移到正确位置
            vertices = vertices + repmat(position, 8, 1);
            
            % 定义面
            faces = [
                1, 2, 3, 4;  % 底面
                5, 6, 7, 8;  % 顶面
                1, 2, 6, 5;  % 前面
                3, 4, 8, 7;  % 后面
                1, 4, 8, 5;  % 左面
                2, 3, 7, 6   % 右面
            ];
            
            % 绘制积木
            brick_obj = patch('Vertices', vertices, ...
                              'Faces', faces, ...
                              'FaceColor', color, ...
                              'EdgeColor', 'black', ...
                              'FaceAlpha', alpha, ...
                              'LineWidth', 0.5, ...
                              'Parent', obj.axes_3d);
        end
        
        function setupAnimation(obj)
            % 设置动画系统
            obj.is_playing = false;
            obj.animation_timer = [];
            
            fprintf('✅ 动画系统设置完成\n');
        end
        
        function startAssembly(obj)
            % 开始拼接
            fprintf('🚀 开始拼接动画...\n');
            obj.is_playing = true;
            obj.animation_phase = 'moving_to_pick';
            
            % 更新状态
            obj.updateStatus(sprintf('开始拼接 - 步骤 1/%d', obj.total_steps));
            
            % 创建动画定时器
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end
            
            obj.animation_timer = timer('ExecutionMode', 'fixedRate', ...
                                        'Period', 0.1, ...
                                        'TimerFcn', @(~,~) obj.updateAnimation());
            start(obj.animation_timer);
        end
        
        function updateAnimation(obj)
            % 更新动画
            if ~obj.is_playing || obj.current_step > obj.total_steps
                obj.pauseAssembly();
                return;
            end
            
            % 执行当前步骤
            obj.executeCurrentStep();
            
            % 更新显示
            obj.updateDisplay();
        end
        
        function executeCurrentStep(obj)
            % 执行当前拼接步骤
            step = obj.assembly_sequence(obj.current_step);
            
            switch obj.animation_phase
                case 'moving_to_pick'
                    obj.moveArmToPosition(step.arm, step.pick_pos);
                    obj.animation_phase = 'picking';
                    
                case 'picking'
                    obj.controlGripper(step.arm, 'close');
                    obj.animation_phase = 'moving_to_place';
                    
                case 'moving_to_place'
                    obj.moveArmToPosition(step.arm, step.place_pos);
                    obj.animation_phase = 'placing';
                    
                case 'placing'
                    obj.controlGripper(step.arm, 'open');
                    obj.placeBrick(obj.current_step);
                    obj.animation_phase = 'returning';
                    
                case 'returning'
                    obj.moveArmToHome(step.arm);
                    obj.nextStep();
            end
        end
        
        function moveArmToPosition(obj, arm, position)
            % 移动机械臂到指定位置
            if strcmp(arm, 'left')
                obj.left_arm_pos = position;
            else
                obj.right_arm_pos = position;
            end
            
            % 这里可以添加逆运动学计算
            % 暂时使用简化的位置更新
        end
        
        function moveArmToHome(obj, arm)
            % 机械臂返回初始位置
            if strcmp(arm, 'left')
                obj.left_arm_pos = [0.3, 0.3, 0.5];
            else
                obj.right_arm_pos = [0.3, -0.3, 0.5];
            end
        end
        
        function controlGripper(obj, arm, action)
            % 控制夹爪
            if strcmp(action, 'open')
                obj.gripper_states.(arm).is_open = true;
                obj.gripper_states.(arm).position = 0.02;
            else
                obj.gripper_states.(arm).is_open = false;
                obj.gripper_states.(arm).position = 0;
            end
            
            fprintf('夹爪控制: %s臂 %s\n', arm, action);
        end
        
        function placeBrick(obj, step_id)
            % 放置积木
            if step_id <= length(obj.brick_objects)
                % 更新积木显示为已放置状态
                set(obj.brick_objects{step_id}, 'FaceColor', [1, 0, 0], 'FaceAlpha', 0.8);
                obj.placed_bricks(step_id) = true;
            end
        end
        
        function nextStep(obj)
            % 进入下一步
            obj.current_step = obj.current_step + 1;
            obj.animation_phase = 'moving_to_pick';
            
            % 更新进度条
            if obj.current_step <= obj.total_steps
                obj.progress_bar.Value = obj.current_step;
                obj.updateStatus(sprintf('拼接中 - 步骤 %d/%d', obj.current_step, obj.total_steps));
            else
                obj.updateStatus('拼接完成！');
                obj.pauseAssembly();
            end
        end
        
        function updateDisplay(obj)
            % 更新显示
            try
                % 更新机器人显示
                if ~isempty(obj.robot_plot) && isvalid(obj.robot_plot)
                    % 这里可以更新机器人关节角度
                    % 暂时跳过以避免错误
                end
                
                drawnow limitrate;
                
            catch ME
                % 忽略显示错误，继续动画
            end
        end
        
        function updateStatus(obj, message)
            % 更新状态文本
            obj.status_text.String = message;
        end
        
        function pauseAssembly(obj)
            % 暂停拼接
            obj.is_playing = false;
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
            end
            fprintf('⏸️ 拼接已暂停\n');
        end
        
        function stopAssembly(obj)
            % 停止拼接
            obj.pauseAssembly();
            obj.current_step = 1;
            obj.animation_phase = 'idle';
            obj.progress_bar.Value = 1;
            obj.updateStatus('准备开始拼接...');
            fprintf('⏹️ 拼接已停止\n');
        end
        
        function seekToStep(obj, step)
            % 跳转到指定步骤
            obj.current_step = max(1, min(step, obj.total_steps));
            obj.updateStatus(sprintf('跳转到步骤 %d/%d', obj.current_step, obj.total_steps));
        end
        
        function closeSystem(obj)
            % 关闭系统
            if ~isempty(obj.animation_timer) && isvalid(obj.animation_timer)
                stop(obj.animation_timer);
                delete(obj.animation_timer);
            end
            delete(obj.main_figure);
        end
    end
end
