classdef LDRParser < handle
    % LDR Parser - 解析main_building.ldr文件
    % 提取积木坐标、颜色、类型信息
    
    properties
        filename
        bricks
        colors
        assembly_sequence
        total_bricks
    end
    
    methods
        function obj = LDRParser(filename)
            obj.filename = filename;
            obj.bricks = [];
            obj.colors = containers.Map();
            obj.assembly_sequence = [];
            obj.total_bricks = 0;
            
            % 初始化LEGO颜色映射
            obj.initializeColors();
        end
        
        function success = parseLDRFile(obj)
            % 解析LDR文件
            fprintf('=== 解析LDR文件: %s ===\n', obj.filename);
            
            try
                if ~exist(obj.filename, 'file')
                    fprintf('错误: 文件不存在 %s\n', obj.filename);
                    success = false;
                    return;
                end
                
                % 读取文件
                fid = fopen(obj.filename, 'r');
                if fid == -1
                    fprintf('错误: 无法打开文件\n');
                    success = false;
                    return;
                end
                
                lineNum = 0;
                brickCount = 0;
                
                while ~feof(fid)
                    line = fgetl(fid);
                    lineNum = lineNum + 1;
                    
                    if ischar(line) && ~isempty(line)
                        brick = obj.parseLine(line, lineNum);
                        if ~isempty(brick)
                            brickCount = brickCount + 1;
                            if isempty(obj.bricks)
                                obj.bricks = brick;
                            else
                                obj.bricks(end+1) = brick;
                            end
                        end
                    end
                end
                
                fclose(fid);
                obj.total_bricks = brickCount;
                
                fprintf('成功解析 %d 个积木\n', obj.total_bricks);
                obj.displayBrickSummary();
                
                success = true;
                
            catch ME
                fprintf('解析错误: %s\n', ME.message);
                success = false;
            end
        end
        
        function brick = parseLine(obj, line, lineNum)
            % 解析单行LDR数据
            brick = [];
            
            % 跳过注释和空行
            if isempty(line) || line(1) == '0'
                return;
            end
            
            % 分割行数据
            parts = strsplit(strtrim(line));
            
            if length(parts) < 15
                return;
            end
            
            try
                % LDR格式: 1 color x y z a b c d e f g h i part.dat
                lineType = str2double(parts{1});
                
                if lineType == 1  % 积木行
                    brick = struct();
                    brick.id = lineNum;
                    brick.color = str2double(parts{2});
                    brick.position = [str2double(parts{3}), str2double(parts{4}), str2double(parts{5})];
                    
                    % 变换矩阵 (3x3)
                    brick.transform = [
                        str2double(parts{6}), str2double(parts{7}), str2double(parts{8});
                        str2double(parts{9}), str2double(parts{10}), str2double(parts{11});
                        str2double(parts{12}), str2double(parts{13}), str2double(parts{14})
                    ];
                    
                    brick.part = parts{15};
                    brick.size = obj.getBrickSize(brick.part);
                    brick.color_name = obj.getColorName(brick.color);
                    brick.priority = obj.calculatePriority(brick);
                    
                    fprintf('积木 %d: %s %s 位置[%.1f,%.1f,%.1f]\n', ...
                            brick.id, brick.color_name, brick.part, brick.position);
                end
                
            catch ME
                fprintf('警告: 第%d行解析失败: %s\n', lineNum, ME.message);
            end
        end
        
        function size = getBrickSize(obj, partName)
            % 根据零件名称确定积木尺寸
            
            % 标准LEGO积木尺寸映射
            sizeMap = containers.Map();
            sizeMap('3001.dat') = [32, 16, 9.6];    % 2x4 brick
            sizeMap('3002.dat') = [24, 16, 9.6];    % 2x3 brick
            sizeMap('3003.dat') = [16, 16, 9.6];    % 2x2 brick
            sizeMap('3004.dat') = [8, 16, 9.6];     % 1x2 brick
            sizeMap('3005.dat') = [8, 8, 9.6];      % 1x1 brick
            sizeMap('3010.dat') = [8, 32, 9.6];     % 1x4 brick
            sizeMap('3020.dat') = [16, 32, 9.6];    % 2x4 plate
            sizeMap('3021.dat') = [16, 24, 3.2];    % 2x3 plate
            sizeMap('3022.dat') = [16, 16, 3.2];    % 2x2 plate
            sizeMap('3023.dat') = [8, 16, 3.2];     % 1x2 plate
            sizeMap('3024.dat') = [8, 8, 3.2];      % 1x1 plate
            
            if sizeMap.isKey(partName)
                size = sizeMap(partName) / 1000;  % 转换为米
            else
                % 默认尺寸
                size = [0.016, 0.016, 0.0096];  % 1x1 brick
                fprintf('警告: 未知零件 %s，使用默认尺寸\n', partName);
            end
        end
        
        function colorName = getColorName(obj, colorCode)
            % 获取颜色名称
            if obj.colors.isKey(num2str(colorCode))
                colorName = obj.colors(num2str(colorCode));
            else
                colorName = sprintf('Color_%d', colorCode);
            end
        end
        
        function priority = calculatePriority(obj, brick)
            % 计算积木组装优先级 (Z坐标越低优先级越高)
            priority = -brick.position(3);  % 负号使得低位置有高优先级
        end
        
        function initializeColors(obj)
            % 初始化LEGO颜色映射
            obj.colors('0') = 'Black';
            obj.colors('1') = 'Blue';
            obj.colors('2') = 'Green';
            obj.colors('3') = 'Dark_Turquoise';
            obj.colors('4') = 'Red';
            obj.colors('5') = 'Dark_Pink';
            obj.colors('6') = 'Brown';
            obj.colors('7') = 'Light_Gray';
            obj.colors('8') = 'Dark_Gray';
            obj.colors('9') = 'Light_Blue';
            obj.colors('10') = 'Bright_Green';
            obj.colors('11') = 'Light_Turquoise';
            obj.colors('12') = 'Light_Red';
            obj.colors('13') = 'Pink';
            obj.colors('14') = 'Yellow';
            obj.colors('15') = 'White';
            obj.colors('16') = 'Light_Green';
            obj.colors('17') = 'Light_Yellow';
            obj.colors('18') = 'Tan';
            obj.colors('19') = 'Light_Violet';
            obj.colors('20') = 'Purple';
        end
        
        function sequence = generateAssemblySequence(obj)
            % 生成最优组装序列
            fprintf('\n=== 生成组装序列 ===\n');
            
            if isempty(obj.bricks)
                fprintf('错误: 没有积木数据\n');
                sequence = [];
                return;
            end
            
            % 按优先级排序 (Z坐标从低到高)
            [~, sortIdx] = sort([obj.bricks.priority], 'descend');
            sortedBricks = obj.bricks(sortIdx);
            
            % 分组策略: 按层分组
            layers = obj.groupByLayers(sortedBricks);
            
            % 生成序列
            sequence = [];
            for layerIdx = 1:length(layers)
                layer = layers{layerIdx};
                fprintf('第 %d 层: %d 个积木\n', layerIdx, length(layer));

                % 在每层内按位置优化顺序
                layerSequence = obj.optimizeLayerSequence(layer);
                if isempty(sequence)
                    sequence = layerSequence;
                else
                    sequence = [sequence, layerSequence];
                end
            end
            
            obj.assembly_sequence = sequence;
            fprintf('组装序列生成完成: %d 个步骤\n', length(sequence));
        end
        
        function layers = groupByLayers(obj, bricks)
            % 按Z坐标分层
            zPositions = [bricks.position];
            zCoords = zPositions(3:3:end);
            
            % 找到唯一的Z层
            uniqueZ = unique(round(zCoords * 1000) / 1000);  % 四舍五入避免浮点误差
            
            layers = cell(length(uniqueZ), 1);
            
            for i = 1:length(uniqueZ)
                z = uniqueZ(i);
                layerMask = abs(zCoords - z) < 0.001;  % 容差
                layers{i} = bricks(layerMask);
            end
        end
        
        function sequence = optimizeLayerSequence(obj, layer)
            % 优化单层内的组装顺序
            if length(layer) <= 1
                sequence = layer;
                return;
            end
            
            % 简单策略: 从中心向外扩展
            positions = reshape([layer.position], 3, [])';
            center = mean(positions, 1);
            
            % 计算到中心的距离
            distances = sqrt(sum((positions - center).^2, 2));
            [~, sortIdx] = sort(distances);
            
            sequence = layer(sortIdx);
        end
        
        function displayBrickSummary(obj)
            % 显示积木统计信息
            fprintf('\n=== 积木统计 ===\n');
            fprintf('总积木数: %d\n', obj.total_bricks);
            
            if isempty(obj.bricks)
                return;
            end
            
            % 按颜色统计
            colorCounts = containers.Map();
            for i = 1:length(obj.bricks)
                colorName = obj.bricks(i).color_name;
                if colorCounts.isKey(colorName)
                    colorCounts(colorName) = colorCounts(colorName) + 1;
                else
                    colorCounts(colorName) = 1;
                end
            end
            
            fprintf('颜色分布:\n');
            colorNames = keys(colorCounts);
            for i = 1:length(colorNames)
                fprintf('  %s: %d 个\n', colorNames{i}, colorCounts(colorNames{i}));
            end
            
            % 位置范围
            positions = reshape([obj.bricks.position], 3, [])';
            minPos = min(positions, [], 1);
            maxPos = max(positions, [], 1);
            
            fprintf('位置范围:\n');
            fprintf('  X: %.3f 到 %.3f\n', minPos(1), maxPos(1));
            fprintf('  Y: %.3f 到 %.3f\n', minPos(2), maxPos(2));
            fprintf('  Z: %.3f 到 %.3f\n', minPos(3), maxPos(3));
        end
        
        function success = exportToMAT(obj, filename)
            % 导出解析结果到MAT文件
            try
                bricks = obj.bricks;
                assembly_sequence = obj.assembly_sequence;
                colors = obj.colors;
                total_bricks = obj.total_bricks;
                
                save(filename, 'bricks', 'assembly_sequence', 'colors', 'total_bricks');
                fprintf('数据已导出到: %s\n', filename);
                success = true;
            catch ME
                fprintf('导出失败: %s\n', ME.message);
                success = false;
            end
        end
    end
end
